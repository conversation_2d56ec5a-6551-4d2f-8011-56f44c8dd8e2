# Skill Delete Feature

This document explains the implementation of the skill deletion feature in the Skilltrade application.

## Overview

The skill deletion feature allows users to delete their skills from both the skill detail page and the skill edit page. The feature includes:

1. Delete buttons on both pages
2. Confirmation modals to prevent accidental deletions
3. Safety checks to prevent deletion of skills with active or upcoming sessions
4. Proper cleanup of related data (available dates)

## Implementation Details

### Files Modified

1. **app/dashboard/skills/[id]/page.tsx**
   - Added delete button to the skill detail page
   - Added delete confirmation modal
   - Implemented `handleDeleteSkill` function that uses a database function

2. **app/dashboard/skills/[id]/edit/page.tsx**
   - Added delete button to the skill edit page
   - Added delete confirmation modal
   - Implemented `handleDeleteSkill` function that uses a database function

3. **supabase/migrations/20240602_delete_skill_function.sql**
   - Created a comprehensive database function to delete a skill and all its dependencies in a single transaction

### Delete Process

The skill deletion process follows these steps:

1. User clicks the "Delete" button
2. A confirmation modal appears asking the user to confirm the deletion
3. If the user confirms, the system:
   - Calls the `delete_skill_with_dependencies` database function, which:
     1. Checks if the skill has any active or upcoming sessions
     2. If it does, prevents deletion and throws an error
     3. If not, proceeds with the deletion process in a single transaction:
        - Deletes ledger entries associated with the skill's sessions
        - Deletes reviews associated with the skill's sessions
        - Deletes all sessions associated with the skill
        - Deletes all available dates for the skill
        - Finally deletes the skill itself
   - Redirects the user to the skills list page

### Safety Checks

To ensure data integrity and prevent issues, the following safety checks are implemented:

1. **Session Check**: The database function checks if the skill has any active or upcoming sessions (status 'pending' or 'accepted'). If it does, deletion is prevented.

2. **Transaction Safety**: The entire deletion process is wrapped in a database transaction, ensuring that either all related records are deleted or none are (preventing partial deletions that could leave the database in an inconsistent state).

3. **Cascading Delete**: The database function handles the deletion of related records in the correct order to prevent foreign key constraint violations:
   - First deletes ledger entries (which reference sessions)
   - Then deletes reviews (which reference sessions)
   - Then deletes sessions (which reference the skill)
   - Then deletes available dates
   - Finally deletes the skill itself

4. **User Ownership**: The system verifies that the user owns the skill before allowing deletion (this is handled by the existing authentication checks in the pages).

5. **Error Handling**: The database function includes error handling to catch and report any issues that occur, and the frontend code displays these errors to the user.

### UI Components

#### Delete Button

The delete button is prominently displayed but visually distinct from other actions:

```jsx
<button
  onClick={() => setShowDeleteConfirm(true)}
  className="bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-md transition"
>
  Delete
</button>
```

#### Confirmation Modal

The confirmation modal provides clear information about the consequences of deletion:

```jsx
<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
  <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full">
    <h3 className="text-xl font-bold mb-4">Delete Skill</h3>
    <p className="mb-6">
      Are you sure you want to delete this skill? This action cannot be undone.
      {skill.available_dates.some(date => date.is_booked) && (
        <span className="block mt-2 text-yellow-400">
          Note: This skill has booked sessions. Deleting it will affect existing bookings.
        </span>
      )}
    </p>
    <div className="flex justify-end space-x-3">
      <button
        type="button"
        onClick={() => setShowDeleteConfirm(false)}
        className="px-4 py-2 bg-gray-700 text-white rounded-md hover:bg-gray-600 transition"
        disabled={deleting}
      >
        Cancel
      </button>
      <button
        type="button"
        onClick={handleDeleteSkill}
        className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition"
        disabled={deleting}
      >
        {deleting ? 'Deleting...' : 'Delete'}
      </button>
    </div>
  </div>
</div>
```

## Error Handling

The feature includes comprehensive error handling:

1. **Session Check Errors**: If a skill has active or upcoming sessions, the user is informed that they need to cancel these sessions first.

2. **Database Errors**: Any errors during the deletion process are caught and displayed to the user.

3. **Loading States**: The delete button is disabled during the deletion process to prevent multiple clicks.

## User Experience Considerations

1. **Confirmation**: The confirmation modal prevents accidental deletions.

2. **Clear Feedback**: Error messages are displayed if deletion fails.

3. **Consistent Placement**: Delete buttons are consistently placed in both the detail and edit pages.

4. **Visual Distinction**: Delete buttons use a red color to indicate a destructive action.

5. **Warning Messages**: If a skill has booked sessions, a warning message is displayed in the confirmation modal.

## Testing

To test the skill deletion feature:

1. Create a new skill
2. Try deleting the skill from both the detail and edit pages
3. Create a skill with booked sessions and verify that deletion is prevented
4. Check that after successful deletion, the user is redirected to the skills list page
5. Verify that all related data (available dates) is properly deleted

## Future Enhancements

Potential future enhancements for the skill deletion feature:

1. **Soft Delete**: Implement a soft delete mechanism to allow recovery of accidentally deleted skills.

2. **Batch Delete**: Allow users to delete multiple skills at once from the skills list page.

3. **Archive Option**: Provide an option to archive skills instead of deleting them, preserving historical data.

4. **Deletion Reason**: Allow users to specify a reason for deletion for analytics purposes.
