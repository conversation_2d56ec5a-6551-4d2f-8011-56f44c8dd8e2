'use client';

import { useState, useEffect } from 'react';
import { createClientSide } from '@/lib/supabase';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import MainHeader from '@/components/MainHeader';
import MainFooter from '@/components/MainFooter';

interface Review {
  id: string;
  rating: number | string;
  comment: string;
  created_at: string;
  reviewer: {
    display_name: string | null;
    avatar_url: string | null;
  };
}

interface Session {
  id: string;
  scheduled_at: string;
  status: string;
}

interface AvailableDate {
  id: string;
  skill_id: string;
  date_time: string;
  duration_hours: number;
  is_booked: boolean;
  created_at: string;
}

interface Skill {
  id: string;
  owner_id: string;
  title: string;
  description: string;
  tags: string[];
  is_active: boolean;
  created_at: string;
  difficulty_level?: 'beginner' | 'intermediate' | 'advanced';
  image_url?: string | null;
  reviews: Review[];
  sessions: Session[];
  session_count: number;
  avg_rating: number;
  last_session_date: string | null;
  available_dates?: AvailableDate[];
}

interface Profile {
  id: string;
  display_name: string | null;
  avatar_url: string | null;
  bio: string | null;
}

export default function SkillDetail({ params }: { params: { id: string } }) {
  const [skill, setSkill] = useState<Skill | null>(null);
  const [owner, setOwner] = useState<Profile | null>(null);
  const [isOwner, setIsOwner] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [bookingModalOpen, setBookingModalOpen] = useState(false);
  const [selectedDateId, setSelectedDateId] = useState<string>('');
  const [customBooking, setCustomBooking] = useState<boolean>(false);
  const [bookingDate, setBookingDate] = useState<string>('');
  const [bookingTime, setBookingTime] = useState<string>('');
  const [bookingDuration, setBookingDuration] = useState<number>(1);
  const [bookingMessage, setBookingMessage] = useState<string>('');
  const [bookingError, setBookingError] = useState<string | null>(null);
  const [bookingSuccess, setBookingSuccess] = useState<boolean>(false);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const router = useRouter();
  const supabase = createClientSide();
  const { id } = params;

  useEffect(() => {
    const fetchSkill = async () => {
      try {
        setLoading(true);

        // Get the current user
        const { data: { user } } = await supabase.auth.getUser();

        // Get the skill
        const { data: skillData, error: skillError } = await supabase
          .from('skills')
          .select('*')
          .eq('id', id)
          .single();

        if (skillError) {
          throw skillError;
        }

        // Get sessions for this skill
        const { data: sessionsData, error: sessionsError } = await supabase
          .from('sessions')
          .select(`
            id,
            scheduled_at,
            status
          `)
          .eq('skill_id', id)
          .order('scheduled_at', { ascending: false })
          .limit(5);

        if (sessionsError) {
          console.error('Error fetching sessions:', sessionsError);
        }

        // Get session count
        const { count: sessionCount, error: countError } = await supabase
          .from('sessions')
          .select('id', { count: 'exact', head: true })
          .eq('skill_id', id);

        if (countError) {
          console.error('Error fetching session count:', countError);
        }

        // First get session IDs for this skill
        const { data: sessionIds, error: sessionIdsError } = await supabase
          .from('sessions')
          .select('id')
          .eq('skill_id', id);

        if (sessionIdsError) {
          console.error('Error fetching session IDs:', sessionIdsError);
        }

        // Then fetch reviews for these sessions
        let reviewsData: Review[] = [];
        if (sessionIds && sessionIds.length > 0) {
          const sessionIdArray = sessionIds.map(s => s.id);
          const { data: reviews, error: reviewsError } = await supabase
            .from('reviews')
            .select(`
              id,
              rating,
              comment,
              created_at,
              reviewer_id,
              session_id
            `)
            .in('session_id', sessionIdArray)
            .order('created_at', { ascending: false });

          if (reviewsError) {
            console.error('Error fetching reviews:', reviewsError);
          } else {
            // Get all reviewer IDs
            const reviewerIds = reviews?.map(review => review.reviewer_id) || [];

            // Fetch all reviewer profiles in a single query
            const { data: reviewerProfiles, error: profilesError } = await supabase
              .from('profiles')
              .select('id, display_name, avatar_url')
              .in('id', reviewerIds);

            if (profilesError) {
              console.error('Error fetching reviewer profiles:', profilesError);
            }

            // Create a map of reviewer IDs to profiles for quick lookup
            const reviewerMap = (reviewerProfiles || []).reduce<Record<string, any>>((map, profile) => {
              if (profile && profile.id) {
                map[profile.id] = profile;
              }
              return map;
            }, {});

            // Transform the data to match the Review interface
            reviewsData = (reviews || []).map(review => {
              const reviewer = reviewerMap[review.reviewer_id] || {};

              return {
                id: review.id,
                rating: review.rating,
                comment: review.comment,
                created_at: review.created_at,
                reviewer: {
                  display_name: reviewer.display_name,
                  avatar_url: reviewer.avatar_url
                }
              };
            });
          }
        }

        // Calculate average rating (out of 5 stars)
        let avgRating = 0;
        if (reviewsData && reviewsData.length > 0) {
          const totalStars = reviewsData.reduce((sum, review) => {
            // Handle both numeric ratings and positive/negative ratings
            if (typeof review.rating === 'number') {
              return sum + review.rating;
            } else if (review.rating === 'positive') {
              return sum + 5; // Positive is 5 stars
            } else {
              return sum + 1; // Negative is 1 star
            }
          }, 0);
          avgRating = (totalStars / reviewsData.length) * 20; // Convert to percentage (1-5 stars → 20-100%)
        }

        // Get last session date
        const lastSession = sessionsData && sessionsData.length > 0 ? sessionsData[0] : null;
        const lastSessionDate = lastSession ? lastSession.scheduled_at : null;

        // Get available dates
        const { data: availableDates, error: availableDatesError } = await supabase
          .from('skill_available_dates')
          .select('*')
          .eq('skill_id', id)
          .eq('is_booked', false)
          .gte('date_time', new Date().toISOString())
          .order('date_time', { ascending: true });

        if (availableDatesError) {
          console.error('Error fetching available dates:', availableDatesError);
        }

        // Create enhanced skill object
        const enhancedSkill = {
          ...skillData,
          reviews: reviewsData || [],
          sessions: sessionsData || [],
          session_count: sessionCount || 0,
          avg_rating: avgRating,
          last_session_date: lastSessionDate,
          available_dates: availableDates || []
        };

        setSkill(enhancedSkill);

        // Check if the current user is the owner
        if (user && skillData.owner_id === user.id) {
          setIsOwner(true);
        }

        // Get the owner's profile
        const { data: ownerData, error: ownerError } = await supabase
          .from('profiles')
          .select('id, display_name, avatar_url, bio')
          .eq('id', skillData.owner_id)
          .single();

        if (ownerError) {
          throw ownerError;
        }

        setOwner(ownerData);
      } catch (error: any) {
        setError(error.message || 'Failed to load skill');
      } finally {
        setLoading(false);
      }
    };

    fetchSkill();
  }, [id, supabase]);

  const handleBookingSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);
    setBookingError(null);

    try {
      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        router.push('/login');
        return;
      }

      // Check if user is trying to book their own skill
      if (user.id === skill?.owner_id) {
        throw new Error('You cannot book your own skill');
      }

      // Check if user already has a pending or accepted session for this skill
      const { data: existingSessions, error: checkError } = await supabase
        .from('sessions')
        .select('id, status')
        .eq('skill_id', id)
        .eq('learner_id', user.id)
        .in('status', ['pending', 'accepted'])
        .limit(1);

      if (checkError) {
        console.error('Error checking existing sessions:', checkError);
        throw new Error('Failed to check existing bookings');
      }

      if (existingSessions && existingSessions.length > 0) {
        const status = existingSessions[0].status;
        throw new Error(`You already have a ${status} booking for this skill. Please check your sessions page.`);
      }

      let scheduledAt: Date;
      let duration: number;

      if (selectedDateId && !customBooking) {
        // Using a pre-defined available date
        console.log('Booking with selected date ID:', selectedDateId);

        // First, check if the date is still available (not booked)
        const { data: currentDateStatus, error: checkError } = await supabase
          .from('skill_available_dates')
          .select('is_booked')
          .eq('id', selectedDateId)
          .single();

        if (checkError) {
          console.error('Error checking date availability:', checkError);
          throw new Error('Failed to check date availability');
        }

        console.log('Current date status:', currentDateStatus);

        if (currentDateStatus.is_booked) {
          throw new Error('This time slot has already been booked by someone else. Please select another date.');
        }

        const selectedDate = skill?.available_dates?.find(date => date.id === selectedDateId);

        if (!selectedDate) {
          throw new Error('The selected available date is no longer valid');
        }

        scheduledAt = new Date(selectedDate.date_time);
        duration = selectedDate.duration_hours;

        // We'll mark the date as booked AFTER creating the session to ensure atomicity
        // This prevents the issue where dates are marked as booked but no session is created
      } else {
        // Custom booking
        // Validate form
        if (!bookingDate || !bookingTime) {
          throw new Error('Please select a date and time for your session');
        }

        // Combine date and time into a datetime string
        scheduledAt = new Date(`${bookingDate}T${bookingTime}`);

        // Check if the date is in the future
        if (scheduledAt <= new Date()) {
          throw new Error('Please select a future date and time');
        }

        duration = bookingDuration;
      }

      // Insert the session
      const { data: newSession, error } = await supabase
        .from('sessions')
        .insert({
          skill_id: id,
          teacher_id: skill?.owner_id,
          learner_id: user.id,
          scheduled_at: scheduledAt.toISOString(),
          duration_hours: duration,
          status: 'pending',
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      console.log('Session created successfully:', newSession);

      // Now that the session is created, mark the date as booked if using a pre-defined date
      if (selectedDateId && !customBooking) {
        console.log('Marking date as booked:', selectedDateId);

        const { data: updatedDate, error: updateError } = await supabase
          .from('skill_available_dates')
          .update({ is_booked: true })
          .eq('id', selectedDateId)
          .select()
          .single();

        if (updateError) {
          console.error('Error marking date as booked:', updateError);
          // We don't throw here since the session was already created
          // Instead, we log the error and continue
        } else {
          console.log('Date marked as booked successfully:', updatedDate);
        }
      }

      // Show success message
      setBookingSuccess(true);

      // Reset form
      setSelectedDateId('');
      setCustomBooking(false);
      setBookingDate('');
      setBookingTime('');
      setBookingDuration(1);
      setBookingMessage('');

      // Close modal after a delay
      setTimeout(() => {
        setBookingModalOpen(false);
        setBookingSuccess(false);
        router.push('/sessions');
      }, 3000);
    } catch (error: any) {
      setBookingError(error.message || 'Failed to book session');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <MainHeader />
        <div className="container mx-auto px-4 py-8">
          <div className="flex justify-center items-center h-64">
            <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
            <p className="ml-4 text-gray-700 dark:text-gray-300">Loading skill...</p>
          </div>
        </div>
        <MainFooter />
      </div>
    );
  }

  if (!skill || !owner) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <MainHeader />
        <div className="container mx-auto px-4 py-8">
          <div className="bg-white dark:bg-gray-800 rounded-xl p-8 text-center shadow-lg">
            <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Skill not found</h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              The skill you're looking for doesn't exist or has been removed.
            </p>
            <Link
              href="/explore"
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition inline-block"
            >
              Explore Skills
            </Link>
          </div>
        </div>
        <MainFooter />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <MainHeader />

      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {error && (
            <div className="bg-red-100 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 px-4 py-3 rounded-lg mb-6">
              {error}
            </div>
          )}

          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
            {/* Skill Image */}
            {skill.image_url && (
              <div className="w-full h-64 overflow-hidden">
                <img
                  src={skill.image_url}
                  alt={skill.title}
                  className="w-full h-full object-cover"
                />
              </div>
            )}

            <div className="p-6 sm:p-8">
              <div className="flex justify-between items-start mb-6">
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 dark:text-white">{skill.title}</h1>

                  {/* Difficulty Level Badge */}
                  {skill.difficulty_level && (
                    <div className="mt-2">
                      <span className={`text-xs px-3 py-1 rounded-full ${
                        skill.difficulty_level === 'beginner'
                          ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'
                          : skill.difficulty_level === 'intermediate'
                          ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300'
                          : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'
                      }`}>
                        {skill.difficulty_level.charAt(0).toUpperCase() + skill.difficulty_level.slice(1)} Level
                      </span>
                    </div>
                  )}
                </div>

                <div className="flex items-center gap-3">
                  {skill.reviews && skill.reviews.length > 0 && (
                    <div className="bg-gray-100 dark:bg-gray-700 rounded-full px-3 py-1 flex items-center">
                      <div className="flex items-center">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <svg
                            key={star}
                            xmlns="http://www.w3.org/2000/svg"
                            className={`h-4 w-4 ${star <= Math.round(skill.avg_rating / 20) ? 'text-yellow-500' : 'text-gray-300 dark:text-gray-600'}`}
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                          </svg>
                        ))}
                        <span className="ml-1 text-xs text-gray-600 dark:text-gray-400">
                          ({(skill.avg_rating / 20).toFixed(1)}/5)
                        </span>
                      </div>
                    </div>
                  )}
                  {!skill.is_active && (
                    <span className="px-3 py-1 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full text-sm">
                      Inactive
                    </span>
                  )}
                </div>
              </div>

              {/* Stats row */}
              <div className="flex flex-wrap gap-4 mb-6 text-sm text-gray-600 dark:text-gray-400">
                <div className="flex items-center">
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                  <span>
                    {skill.session_count} {skill.session_count === 1 ? 'session' : 'sessions'} completed
                  </span>
                </div>

                {skill.last_session_date && (
                  <div className="flex items-center">
                    <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span>
                      Last taught {formatDate(skill.last_session_date)}
                    </span>
                  </div>
                )}
              </div>

              {skill.tags.length > 0 && (
                <div className="flex flex-wrap gap-2 mb-6">
                  {skill.tags.map((tag, index) => (
                    <span key={index} className="px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full text-sm">
                      {tag}
                    </span>
                  ))}
                </div>
              )}

              <div className="mb-8">
                <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">About this skill</h2>
                <div className="text-gray-600 dark:text-gray-300 whitespace-pre-line">
                  {skill.description}
                </div>
              </div>

              {/* Available dates section */}
              {skill.available_dates && skill.available_dates.length > 0 && (
                <div className="mb-8 border-t border-gray-200 dark:border-gray-700 pt-8">
                  <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Available Dates</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {skill.available_dates.map((date) => (
                      <div
                        key={date.id}
                        className="bg-gray-100 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600"
                      >
                        <div className="flex items-center mb-2">
                          <svg className="w-5 h-5 mr-2 text-blue-500 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                          </svg>
                          <span className="font-medium text-gray-900 dark:text-white">
                            {new Date(date.date_time).toLocaleDateString()}
                          </span>
                        </div>
                        <div className="flex items-center mb-2">
                          <svg className="w-5 h-5 mr-2 text-blue-500 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                          </svg>
                          <span className="text-gray-700 dark:text-gray-300">
                            {new Date(date.date_time).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                          </span>
                        </div>
                        <div className="flex items-center">
                          <svg className="w-5 h-5 mr-2 text-blue-500 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                          </svg>
                          <span className="text-gray-700 dark:text-gray-300">
                            {date.duration_hours} {date.duration_hours === 1 ? 'hour' : 'hours'}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                  {!isOwner && skill.is_active && (
                    <div className="mt-4">
                      <button
                        onClick={() => setBookingModalOpen(true)}
                        className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium flex items-center"
                      >
                        <svg className="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Book a session
                      </button>
                    </div>
                  )}
                </div>
              )}

              {/* Reviews section */}
              {skill.reviews && skill.reviews.length > 0 && (
                <div className="border-t border-gray-200 dark:border-gray-700 pt-8 mb-8">
                  <div className="flex justify-between items-center mb-4">
                    <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Reviews</h2>
                    {skill.reviews.length > 3 && (
                      <span className="text-sm text-blue-600 dark:text-blue-400">
                        {skill.reviews.length} total reviews
                      </span>
                    )}
                  </div>

                  <div className="space-y-4">
                    {skill.reviews.slice(0, 3).map((review) => (
                      <div key={review.id} className="bg-gray-100 dark:bg-gray-700 rounded-lg p-4">
                        <div className="flex items-center mb-2">
                          <div className="flex-shrink-0 mr-3">
                            {review.reviewer?.avatar_url ? (
                              <img
                                src={review.reviewer.avatar_url}
                                alt={review.reviewer?.display_name || 'Reviewer'}
                                className="w-10 h-10 rounded-full object-cover"
                              />
                            ) : (
                              <div className="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                                <span className="text-sm text-gray-500 dark:text-gray-400">
                                  {(review.reviewer?.display_name || 'A').charAt(0).toUpperCase()}
                                </span>
                              </div>
                            )}
                          </div>
                          <div>
                            <div className="flex items-center">
                              <span className="font-medium text-gray-900 dark:text-white mr-2">
                                {review.reviewer?.display_name || 'Anonymous'}
                              </span>
                              <div className="flex items-center">
                                {[1, 2, 3, 4, 5].map((star) => (
                                  <svg
                                    key={star}
                                    xmlns="http://www.w3.org/2000/svg"
                                    className={`h-4 w-4 ${star <= (typeof review.rating === 'number' ? review.rating : (review.rating === 'positive' ? 5 : 1)) ? 'text-yellow-500' : 'text-gray-300 dark:text-gray-600'}`}
                                    viewBox="0 0 20 20"
                                    fill="currentColor"
                                  >
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                ))}
                                <span className="ml-1 text-xs text-gray-600 dark:text-gray-400">
                                  ({typeof review.rating === 'number' ? review.rating : (review.rating === 'positive' ? 5 : 1)}/5)
                                </span>
                              </div>
                            </div>
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                              {formatDate(review.created_at)}
                            </p>
                          </div>
                        </div>
                        <p className="text-gray-600 dark:text-gray-300 mt-2">
                          "{review.comment}"
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="border-t border-gray-200 dark:border-gray-700 pt-8 mb-8">
                <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">About the teacher</h2>
                <div className="flex items-start gap-4">
                  <div className="flex-shrink-0">
                    {owner.avatar_url ? (
                      <Image
                        src={owner.avatar_url}
                        alt={owner.display_name || 'Teacher'}
                        width={80}
                        height={80}
                        className="rounded-full object-cover border-2 border-gray-200 dark:border-gray-700 shadow-md"
                      />
                    ) : (
                      <div className="w-20 h-20 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center border-2 border-gray-200 dark:border-gray-700 shadow-md">
                        <span className="text-2xl text-gray-500 dark:text-gray-400">
                          {(owner.display_name || 'U').charAt(0).toUpperCase()}
                        </span>
                      </div>
                    )}
                  </div>
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                      {owner.display_name || 'Anonymous Teacher'}
                    </h3>
                    {owner.bio && (
                      <p className="text-gray-600 dark:text-gray-300 mt-2 line-clamp-3">
                        {owner.bio}
                      </p>
                    )}
                    <Link
                      href={`/profile/${owner.id}`}
                      className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm mt-2 inline-block"
                    >
                      View profile
                    </Link>
                  </div>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row justify-between gap-4">
                <div>
                  {isOwner ? (
                    <Link
                      href={`/skills/${skill.id}/edit`}
                      className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition inline-block"
                    >
                      Edit Skill
                    </Link>
                  ) : (
                    <button
                      onClick={() => setBookingModalOpen(true)}
                      className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
                      disabled={!skill.is_active}
                    >
                      {skill.is_active ? 'Book a Session' : 'Currently Unavailable'}
                    </button>
                  )}
                </div>

                <Link
                  href={isOwner ? '/skills' : '/explore'}
                  className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition self-center"
                >
                  {isOwner ? 'Back to My Skills' : 'Back to Explore'}
                </Link>
              </div>
            </div>
          </div>
        </div>
      </main>

      <MainFooter />

      {/* Booking Modal */}
      {bookingModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 overflow-y-auto">
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 max-w-md w-full shadow-xl">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Book a Session</h2>
              <button
                onClick={() => setBookingModalOpen(false)}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
            </div>

            {bookingSuccess ? (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-green-600 dark:text-green-400">
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Booking Successful!</h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  Your session request has been sent to {owner?.display_name || 'the teacher'}. You'll be redirected to your sessions page.
                </p>
              </div>
            ) : (
              <form onSubmit={handleBookingSubmit}>
                {bookingError && (
                  <div className="bg-red-100 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 px-4 py-3 rounded-lg mb-6">
                    {bookingError}
                  </div>
                )}

                {/* Available dates section */}
                {skill.available_dates && skill.available_dates.length > 0 && (
                  <div className="mb-6">
                    <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                      Available Dates
                    </label>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mb-3">
                      Select one of the teacher's available dates:
                    </p>

                    <div className="space-y-2">
                      {skill.available_dates.map((date) => (
                        <div
                          key={date.id}
                          onClick={() => {
                            setSelectedDateId(date.id);
                            setCustomBooking(false);
                          }}
                          className={`p-3 border rounded-lg cursor-pointer transition ${
                            selectedDateId === date.id && !customBooking
                              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 dark:border-blue-400'
                              : 'border-gray-300 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-500'
                          }`}
                        >
                          <div className="flex justify-between items-center">
                            <div>
                              <div className="font-medium text-gray-900 dark:text-white">
                                {new Date(date.date_time).toLocaleDateString()} at {new Date(date.date_time).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                              </div>
                              <div className="text-sm text-gray-500 dark:text-gray-400">
                                {date.duration_hours} {date.duration_hours === 1 ? 'hour' : 'hours'}
                              </div>
                            </div>
                            <div className={`w-5 h-5 rounded-full border ${
                              selectedDateId === date.id && !customBooking
                                ? 'border-blue-500 bg-blue-500 dark:border-blue-400 dark:bg-blue-400'
                                : 'border-gray-300 dark:border-gray-500'
                            }`}>
                              {selectedDateId === date.id && !customBooking && (
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white" className="w-5 h-5">
                                  <path fillRule="evenodd" d="M19.916 4.626a.75.75 0 01.208 1.04l-9 13.5a.75.75 0 01-1.154.114l-6-6a.75.75 0 011.06-1.06l5.353 5.353 8.493-12.739a.75.75 0 011.04-.208z" clipRule="evenodd" />
                                </svg>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>

                    <div
                      onClick={() => {
                        setSelectedDateId('');
                        setCustomBooking(true);
                      }}
                      className={`p-3 border rounded-lg cursor-pointer mt-4 transition ${
                        customBooking
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 dark:border-blue-400'
                          : 'border-gray-300 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-500'
                      }`}
                    >
                      <div className="flex justify-between items-center">
                        <div className="font-medium text-gray-900 dark:text-white">
                          I'd like to request a custom date and time
                        </div>
                        <div className={`w-5 h-5 rounded-full border ${
                          customBooking
                            ? 'border-blue-500 bg-blue-500 dark:border-blue-400 dark:bg-blue-400'
                            : 'border-gray-300 dark:border-gray-500'
                        }`}>
                          {customBooking && (
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white" className="w-5 h-5">
                              <path fillRule="evenodd" d="M19.916 4.626a.75.75 0 01.208 1.04l-9 13.5a.75.75 0 01-1.154.114l-6-6a.75.75 0 011.06-1.06l5.353 5.353 8.493-12.739a.75.75 0 011.04-.208z" clipRule="evenodd" />
                            </svg>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Custom booking form */}
                {(customBooking || (!skill.available_dates || skill.available_dates.length === 0)) && (
                  <>
                    <div className="mb-4">
                      <label htmlFor="booking-date" className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                        Date
                      </label>
                      <input
                        id="booking-date"
                        type="date"
                        value={bookingDate}
                        onChange={(e) => setBookingDate(e.target.value)}
                        min={new Date().toISOString().split('T')[0]}
                        className="w-full px-4 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        required
                      />
                    </div>

                    <div className="mb-4">
                      <label htmlFor="booking-time" className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                        Time
                      </label>
                      <input
                        id="booking-time"
                        type="time"
                        value={bookingTime}
                        onChange={(e) => setBookingTime(e.target.value)}
                        className="w-full px-4 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        required
                      />
                    </div>

                    <div className="mb-4">
                      <label htmlFor="booking-duration" className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                        Duration (hours)
                      </label>
                      <select
                        id="booking-duration"
                        value={bookingDuration}
                        onChange={(e) => setBookingDuration(Number(e.target.value))}
                        className="w-full px-4 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value={1}>1 hour</option>
                        <option value={2}>2 hours</option>
                        <option value={3}>3 hours</option>
                      </select>
                    </div>
                  </>
                )}

                <div className="mb-6">
                  <label htmlFor="booking-message" className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                    Message to Teacher (optional)
                  </label>
                  <textarea
                    id="booking-message"
                    value={bookingMessage}
                    onChange={(e) => setBookingMessage(e.target.value)}
                    rows={3}
                    className="w-full px-4 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Let the teacher know what you'd like to learn"
                  ></textarea>
                </div>

                <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-6">
                  {selectedDateId && !customBooking && skill.available_dates && (
                    <div className="flex justify-between mb-2">
                      <span className="text-gray-700 dark:text-gray-300">Selected Date:</span>
                      <span className="text-gray-900 dark:text-white font-medium">
                        {(() => {
                          const selectedDate = skill.available_dates.find(d => d.id === selectedDateId);
                          if (selectedDate) {
                            const date = new Date(selectedDate.date_time);
                            return `${date.toLocaleDateString()} at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
                          }
                          return 'Unknown';
                        })()}
                      </span>
                    </div>
                  )}
                  <div className="flex justify-between mb-2">
                    <span className="text-gray-700 dark:text-gray-300">Duration:</span>
                    <span className="text-gray-900 dark:text-white font-medium">
                      {selectedDateId && !customBooking && skill.available_dates ?
                        (() => {
                          const selectedDate = skill.available_dates.find(d => d.id === selectedDateId);
                          return selectedDate ?
                            `${selectedDate.duration_hours} ${selectedDate.duration_hours === 1 ? 'hour' : 'hours'}` :
                            `${bookingDuration} ${bookingDuration === 1 ? 'hour' : 'hours'}`;
                        })() :
                        `${bookingDuration} ${bookingDuration === 1 ? 'hour' : 'hours'}`
                      }
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-700 dark:text-gray-300">Time Credits:</span>
                    <span className="text-red-600 dark:text-red-400 font-medium">
                      {selectedDateId && !customBooking && skill.available_dates ?
                        (() => {
                          const selectedDate = skill.available_dates.find(d => d.id === selectedDateId);
                          return selectedDate ?
                            `-${selectedDate.duration_hours} credits` :
                            `-${bookingDuration} credits`;
                        })() :
                        `-${bookingDuration} credits`
                      }
                    </span>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row justify-end gap-4">
                  <button
                    type="button"
                    onClick={() => setBookingModalOpen(false)}
                    className="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition text-center"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition font-medium"
                    disabled={submitting || (!selectedDateId && !customBooking) || (customBooking && (!bookingDate || !bookingTime))}
                  >
                    {submitting ? 'Booking...' : 'Book Session'}
                  </button>
                </div>
              </form>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

// Helper function to format dates in a user-friendly way
function formatDate(dateString: string | null): string {
  if (!dateString) return 'N/A';

  const date = new Date(dateString);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - date.getTime());
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 0) {
    return 'Today';
  } else if (diffDays === 1) {
    return 'Yesterday';
  } else if (diffDays < 7) {
    return `${diffDays} days ago`;
  } else if (diffDays < 30) {
    const weeks = Math.floor(diffDays / 7);
    return `${weeks} ${weeks === 1 ? 'week' : 'weeks'} ago`;
  } else if (diffDays < 365) {
    const months = Math.floor(diffDays / 30);
    return `${months} ${months === 1 ? 'month' : 'months'} ago`;
  } else {
    return date.toLocaleDateString();
  }
}
