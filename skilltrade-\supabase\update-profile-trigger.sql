-- Update the handle_new_user function to include display_name from metadata
CREATE OR <PERSON><PERSON>LACE FUNCTION handle_new_user() 
<PERSON><PERSON><PERSON><PERSON> trigger AS $$
BEGIN
  INSERT INTO public.profiles (
    id, 
    email, 
    display_name,
    credit_balance
  )
  VALUES (
    NEW.id, 
    NEW.email, 
    COALESCE(NEW.raw_user_meta_data->>'display_name', NEW.email),
    1
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- The trigger should already exist, but if not, uncomment and run this:
-- CREATE TRIGGER on_auth_user_created
--   AFTER INSERT ON auth.users
--   FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();
