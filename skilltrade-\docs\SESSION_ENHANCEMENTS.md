# Session Enhancements Documentation

This document explains the enhancements made to the session system in Skilltrade, including the updated session completion flow, review system, messaging, notifications, and dispute resolution.

## Database Schema Changes

The following changes have been made to the database schema:

### 1. Sessions Table

Added new columns to track completion status by both parties:

- `teacher_marked_complete`: Boolean flag indicating if the teacher marked the session as complete
- `learner_marked_complete`: Boolean flag indicating if the learner marked the session as complete
- `teacher_marked_at`: Timestamp when the teacher marked the session as complete
- `learner_marked_at`: Timestamp when the learner marked the session as complete
- `notes`: Text field for session notes, including dispute resolution notes

### 2. Reviews Table

Changed the review system from binary (positive/negative) to a 5-star rating system:

- `rating`: Changed from text to integer (1-5)
- Updated the check constraint to ensure ratings are between 1 and 5
- Modified the credit application logic to consider ratings of 3 or higher as positive

### 3. New Tables

Added two new tables to support messaging and notifications:

#### Messages Table

```sql
CREATE TABLE messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  session_id UUID NOT NULL REFERENCES sessions(id) ON DELETE CASCADE,
  sender_id UUID NOT NULL REFERENCES profiles(id),
  content TEXT NOT NULL,
  is_read BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);
```

#### Notifications Table

```sql
CREATE TABLE notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  type TEXT NOT NULL, -- 'message', 'session_update', 'review', etc.
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  related_id UUID, -- Can be session_id, message_id, etc.
  is_read BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);
```

## New Database Functions

### 1. Mark Session Complete

The `mark_session_complete` function allows a participant to mark a session as complete:

```sql
CREATE OR REPLACE FUNCTION mark_session_complete(
  p_session_id UUID,
  p_user_id UUID
) RETURNS VOID
```

- Updates the appropriate completion flag based on the user's role
- Creates a notification for the other participant
- If both participants have marked the session as complete, updates the status to 'completed'

### 2. Auto-Complete Sessions

The `auto_complete_sessions` function automatically completes sessions after 24 hours:

```sql
CREATE OR REPLACE FUNCTION auto_complete_sessions() RETURNS VOID
```

- Finds sessions where one party marked complete more than 24 hours ago
- Auto-completes these sessions
- Creates notifications for both parties

### 3. Apply Credits on Review

The `apply_credits_on_review` function handles credit application based on the new 5-star rating system:

```sql
CREATE OR REPLACE FUNCTION apply_credits_on_review() RETURNS TRIGGER
```

- If rating is 3 or higher, considers it positive and applies credits
- If rating is below 3, marks the session as disputed
- Creates notifications for both parties in case of a dispute

### 4. Resolve Dispute

The `resolve_dispute` function allows participants to resolve disputes without admin intervention:

```sql
CREATE OR REPLACE FUNCTION resolve_dispute(
  p_session_id UUID,
  p_resolution TEXT,
  p_user_id UUID,
  p_agreement_message TEXT
) RETURNS VOID
```

- Adds a message about the resolution
- Updates the session status
- Creates a notification for the other participant
- If resolution is 'completed', applies credits

## Session Status Flow

The enhanced session status flow is as follows:

1. **pending**: Initial state when a session is created
2. **accepted**: Teacher has accepted the session
3. **cancelled**: Session has been cancelled
4. **teacher_completed**: Teacher has marked the session as complete
5. **learner_completed**: Learner has marked the session as complete
6. **completed**: Both parties have marked the session as complete (or auto-completed after 24 hours)
7. **reviewed**: Session has been reviewed with a rating of 3 or higher
8. **disputed**: Session has been reviewed with a rating below 3
9. **resolved**: Disputed session has been resolved by participants

## Notification Types

The system supports the following notification types:

- **session_update**: Updates about session status changes
- **message**: New messages in a session chat
- **review**: New reviews for a session
- **dispute**: Session has been marked as disputed
- **dispute_resolved**: A dispute has been resolved

## Scheduled Functions

The `auto_complete_sessions` function should be scheduled to run daily using Supabase's scheduled functions feature.

## Implementation Notes

1. The session completion now requires both the teacher and learner to mark it as complete.
2. If only one party marks it as complete and the other doesn't respond within 24 hours, the session is automatically marked as complete.
3. The review system now uses a 5-star rating instead of positive/negative.
4. Session participants can now message each other directly through the session chat.
5. Users receive notifications for various events, including messages, session updates, and reviews.
6. Disputes are now resolved by the participants themselves through communication, without admin intervention.

## Setting Up Scheduled Functions in Supabase

To set up the auto-completion scheduled function:

1. Navigate to the Supabase dashboard
2. Go to Database > Functions
3. Click on "Create a new scheduled function"
4. Set the schedule (e.g., daily at midnight)
5. Use the SQL from `supabase/functions/auto_complete_sessions.sql`
