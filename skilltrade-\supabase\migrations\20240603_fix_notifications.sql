-- Check if notifications table exists
DO $$
BEGIN
    -- Check if the table exists
    IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'notifications') THEN
        -- Check if 'message' column exists
        IF NOT EXISTS (SELECT FROM information_schema.columns
                      WHERE table_schema = 'public'
                      AND table_name = 'notifications'
                      AND column_name = 'message') THEN
            -- Check if 'content' column exists
            IF EXISTS (SELECT FROM information_schema.columns
                      WHERE table_schema = 'public'
                      AND table_name = 'notifications'
                      AND column_name = 'content') THEN
                -- Rename 'content' to 'message' for consistency
                ALTER TABLE notifications RENAME COLUMN content TO message;
            ELSE
                -- Add 'message' column if neither exists
                ALTER TABLE notifications ADD COLUMN message TEXT;
            END IF;
        END IF;

        -- Check if 'link' column exists
        IF NOT EXISTS (SELECT FROM information_schema.columns
                      WHERE table_schema = 'public'
                      AND table_name = 'notifications'
                      AND column_name = 'link') THEN
            -- Add 'link' column if it doesn't exist
            ALTER TABLE notifications ADD COLUMN link TEXT;
        END IF;
    END IF;
END
$$;
