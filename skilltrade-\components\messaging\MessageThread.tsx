'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { createClientSide } from '@/lib/supabase';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { formatDistanceToNow } from 'date-fns';
import { Send, AlertCircle } from 'lucide-react';

interface Message {
  id: string;
  conversation_id: string;
  sender_id: string;
  content: string;
  created_at: string;
  sender: {
    id: string;
    display_name: string | null;
    avatar_url: string | null;
  };
}

interface Conversation {
  id: string;
  last_message_preview: string | null;
  last_message_at: string | null;
  created_at: string;
  updated_at: string;
  status: string;
  other_participants: {
    id: string;
    display_name: string | null;
    avatar_url: string | null;
  }[];
  is_recipient?: boolean; // Flag to indicate if the current user is the recipient of the conversation request
}

interface MessageThreadProps {
  conversationId: string;
  currentUser: any;
}

export default function MessageThread({ conversationId, currentUser }: MessageThreadProps) {
  const router = useRouter();
  const [messages, setMessages] = useState<Message[]>([]);
  const [conversation, setConversation] = useState<Conversation | null>(null);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const supabase = createClientSide();

  // Memoized function to fetch conversation and messages
  const fetchConversationAndMessages = useCallback(async () => {
    if (!conversationId || !currentUser) return;

    try {
      // Only show loading indicator on initial load
      if (!conversation) {
        setLoading(true);
      }
      setError(null);

      console.log('Fetching conversation and messages for:', conversationId);

      // Use AbortController to handle timeouts
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      // Add cache busting to prevent browser caching
      const timestamp = new Date().getTime();
      const response = await fetch(`/api/conversations/${conversationId}?t=${timestamp}`, {
        signal: controller.signal,
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache',
          'Expires': '0',
        }
      }).finally(() => clearTimeout(timeoutId));

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch conversation');
      }

      const data = await response.json();

      // Check if we have new data to avoid unnecessary updates
      const newConversation = data.conversation;
      const newMessages = data.messages || [];

      // Update conversation if it's changed
      if (!conversation ||
          conversation.status !== newConversation.status ||
          conversation.last_message_at !== newConversation.last_message_at) {
        setConversation(newConversation);
      }

      // Check if we have new messages
      const currentMessageIds = new Set(messages.map((msg: Message) => msg.id));
      const hasNewMessages = newMessages.some((msg: any) => !currentMessageIds.has(msg.id));

      if (hasNewMessages || messages.length === 0) {
        console.log('Setting new messages, count:', newMessages.length);
        setMessages(newMessages);
      }

      // Mark conversation as read
      await fetch(`/api/conversations/${conversationId}/read`, {
        method: 'POST',
      });
    } catch (error: any) {
      // Only set error if it's not an abort error (which is expected during cleanup)
      if (error.name !== 'AbortError') {
        console.error('Error fetching conversation:', error);
        setError(error.message || 'Failed to load conversation');
      }
    } finally {
      setLoading(false);
    }
  }, [conversationId, currentUser, conversation, messages]);

  // Initial fetch of conversation and messages
  useEffect(() => {
    if (conversationId && currentUser) {
      fetchConversationAndMessages();
    }
  }, [conversationId, currentUser, fetchConversationAndMessages]);

  // Set up polling for messages (forum-style)
  useEffect(() => {
    if (!conversationId || !currentUser) return;

    console.log('Setting up message polling for conversation:', conversationId);

    // Initial fetch
    fetchConversationAndMessages();

    // Set up polling interval
    const pollingInterval = setInterval(() => {
      console.log('Polling for message updates in thread');
      fetchConversationAndMessages();
    }, 3000); // Poll every 3 seconds

    return () => {
      console.log('Cleaning up message polling for conversation:', conversationId);
      clearInterval(pollingInterval);
    };
  }, [conversationId, currentUser?.id, fetchConversationAndMessages]);

  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const fetchNewMessage = async (messageId: string) => {
    try {
      // Use AbortController to handle timeouts
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      const response = await fetch(`/api/messages/${messageId}`, {
        signal: controller.signal,
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache',
          'Expires': '0',
        }
      }).finally(() => clearTimeout(timeoutId));

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch message');
      }

      const { message } = await response.json();

      if (message) {
        // Check if the message already exists in the state to avoid duplicates
        setMessages(prev => {
          // If the message already exists, don't add it again
          if (prev.some(msg => msg.id === message.id)) {
            return prev;
          }
          return [...prev, message];
        });
      }
    } catch (error: any) {
      // Only log errors that aren't abort errors
      if (error.name !== 'AbortError') {
        console.error('Error fetching new message:', error);
      }
    }
  };

  const sendMessage = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newMessage.trim()) return;

    try {
      setSending(true);
      setError(null);

      const messageContent = newMessage.trim();

      // Use the API endpoint for message creation with bad word filtering
      const response = await fetch('/api/messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          conversation_id: conversationId,
          content: messageContent,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to send message');
      }

      const data = await response.json();
      const insertedMessage = data.message;

      console.log('Message sent successfully:', insertedMessage);

      // Add the message to the state immediately
      setMessages(prev => [...prev, insertedMessage]);

      // If the message was censored, show a warning
      if (data.censored) {
        setError(`Your message contained inappropriate language (${data.badWords.join(', ')}) and has been censored.`);
      }

      setNewMessage('');

      // If we're showing a censorship warning, keep it visible for a few seconds then clear it
      if (data.censored) {
        setTimeout(() => {
          setError(null);
        }, 5000);
      }
    } catch (error: any) {
      setError(error.message || 'Failed to send message');
    } finally {
      setSending(false);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const isCurrentUser = (senderId: string) => {
    return senderId === currentUser.id;
  };

  const formatMessageTime = (timestamp: string) => {
    return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
  };

  // Type guard function to check conversation status
  const isStatus = (status: string, value: string): boolean => {
    return status === value;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error || !conversation) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="text-center p-6 max-w-md">
          <div className="text-red-500 dark:text-red-400 text-lg font-medium mb-2">
            {error || "Conversation not found or you don't have access to it."}
          </div>
          <p className="text-gray-500 dark:text-gray-400 mb-4">
            This could happen if the conversation was deleted or if you're not a participant.
          </p>
          <button
            onClick={() => {
              // Use window.history to update the URL without a full page reload
              window.history.pushState({}, '', '/dashboard/messages');
              router.replace('/dashboard/messages');
            }}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
          >
            Back to Messages
          </button>
        </div>
      </div>
    );
  }

  // Safely access other participants
  const otherParticipant = conversation.other_participants && conversation.other_participants.length > 0
    ? conversation.other_participants[0]
    : null;
  const displayName = otherParticipant?.display_name || 'Unknown User';
  const avatarUrl = otherParticipant?.avatar_url;

  // Log participant information for debugging
  console.log('Current user:', currentUser);
  console.log('Other participant:', otherParticipant);
  console.log('Is recipient:', conversation.is_recipient);
  console.log('Conversation status:', conversation.status);

  // Handle conversation request acceptance/rejection
  const handleAcceptRequest = async () => {
    try {
      setLoading(true);
      console.log('Accepting conversation request:', conversationId);

      const response = await fetch(`/api/conversations/${conversationId}/accept`, {
        method: 'POST',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to accept conversation request');
      }

      const result = await response.json();
      console.log('Accept conversation response:', result);

      // Refresh the conversation data without redirecting
      const fetchResponse = await fetch(`/api/conversations/${conversationId}`);

      if (!fetchResponse.ok) {
        throw new Error('Failed to refresh conversation data');
      }

      const data = await fetchResponse.json();
      setConversation(data.conversation);
      setMessages(data.messages || []);

    } catch (error: any) {
      console.error('Error accepting conversation request:', error);
      setError(error.message || 'Failed to accept conversation request');
    } finally {
      setLoading(false);
    }
  };

  const handleRejectRequest = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('Rejecting conversation request:', conversationId);

      const response = await fetch(`/api/conversations/${conversationId}/reject`, {
        method: 'POST',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to reject conversation request');
      }

      const result = await response.json();
      console.log('Reject conversation response:', result);

      // Use a more reliable approach to navigation
      // First update the local state to show the conversation is rejected
      setConversation(prev => {
        if (!prev) return prev;
        return {
          ...prev,
          status: 'rejected'
        };
      });

      // Use window.history to update the URL without a full page reload
      window.history.pushState({}, '', '/dashboard/messages');
      router.replace('/dashboard/messages');

    } catch (error: any) {
      console.error('Error rejecting conversation request:', error);
      setError(error.message || 'Failed to reject conversation request');
      setLoading(false);
    }
  };

  // Handle ending a conversation
  const handleEndConversation = async () => {
    if (window.confirm('Are you sure you want to end this conversation? This will prevent any further messages from being sent.')) {
      try {
        setLoading(true);
        console.log('Ending conversation:', conversationId);

        const response = await fetch(`/api/conversations/${conversationId}/end`, {
          method: 'POST',
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to end conversation');
        }

        const result = await response.json();
        console.log('End conversation response:', result);

        // Update the conversation status in state
        setConversation(prev => {
          if (!prev) return prev;
          return {
            ...prev,
            status: 'ended'
          };
        });

      } catch (error: any) {
        console.error('Error ending conversation:', error);
        setError(error.message || 'Failed to end conversation');
      } finally {
        setLoading(false);
      }
    }
  };

  // Handle deleting a conversation
  const handleDeleteConversation = async () => {
    if (window.confirm('Are you sure you want to delete this conversation? This action cannot be undone.')) {
      try {
        setLoading(true);
        setError(null);

        console.log('Deleting conversation:', conversationId);

        const response = await fetch(`/api/conversations/${conversationId}/delete`, {
          method: 'POST',
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to delete conversation');
        }

        const result = await response.json();
        console.log('Delete conversation response:', result);

        // Use window.history to update the URL without a full page reload
        window.history.pushState({}, '', '/dashboard/messages');
        router.replace('/dashboard/messages');

      } catch (error: any) {
        console.error('Error deleting conversation:', error);
        setError(error.message || 'Failed to delete conversation');
        setLoading(false);
      }
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="p-3 sm:p-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
        <div className="flex items-center">
          <div className="flex-shrink-0 mr-2 sm:mr-3">
            {avatarUrl ? (
              <Image
                src={avatarUrl}
                alt={displayName}
                width={36}
                height={36}
                className="rounded-full"
              />
            ) : (
              <div className="w-9 h-9 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                <span className="text-gray-600 dark:text-gray-300 text-sm font-medium">
                  {displayName.charAt(0).toUpperCase()}
                </span>
              </div>
            )}
          </div>
          <div>
            <h3 className="text-base sm:text-lg font-medium text-gray-900 dark:text-white truncate max-w-[120px] sm:max-w-full">{displayName}</h3>
          </div>
        </div>

        {/* Conversation actions */}
        <div className="flex space-x-1 sm:space-x-2">
          {conversation.status === 'accepted' && (
            <button
              onClick={handleEndConversation}
              disabled={loading}
              className="px-2 sm:px-3 py-1 bg-yellow-600 text-white text-xs sm:text-sm rounded hover:bg-yellow-700 transition disabled:opacity-50"
              title="End conversation"
            >
              End
            </button>
          )}
          <button
            onClick={handleDeleteConversation}
            disabled={loading}
            className="px-2 sm:px-3 py-1 bg-red-600 text-white text-xs sm:text-sm rounded hover:bg-red-700 transition disabled:opacity-50"
            title="Delete conversation"
          >
            Delete
          </button>
        </div>
      </div>

      {/* Conversation Request Banner (if status is pending) */}
      {isStatus(conversation.status, 'pending') && (
        <div className="p-4 bg-yellow-100 dark:bg-yellow-900/30 border-b border-yellow-200 dark:border-yellow-800">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
            <div className="flex items-center text-yellow-800 dark:text-yellow-300">
              <AlertCircle size={16} className="mr-2 flex-shrink-0" />
              <span className="text-sm">
                {conversation.is_recipient
                  ? <><strong>{otherParticipant?.display_name || 'This user'}</strong> wants to start a conversation with you.</>
                  : <>Waiting for <strong>{otherParticipant?.display_name || 'the other user'}</strong> to accept your conversation request.</>
                }
              </span>
            </div>
            {/* Only show accept/decline buttons to the recipient */}
            {conversation.is_recipient && (
              <div className="flex gap-2">
                <button
                  onClick={handleAcceptRequest}
                  disabled={loading}
                  className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 transition disabled:opacity-50"
                >
                  Accept
                </button>
                <button
                  onClick={handleRejectRequest}
                  disabled={loading}
                  className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 transition disabled:opacity-50"
                >
                  Decline
                </button>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Rejected Banner */}
      {isStatus(conversation.status, 'rejected') && (
        <div className="p-4 bg-red-100 dark:bg-red-900/30 border-b border-red-200 dark:border-red-800">
          <div className="flex items-center text-red-800 dark:text-red-300">
            <AlertCircle size={16} className="mr-2" />
            <span className="text-sm">
              This conversation request has been declined.
            </span>
          </div>
        </div>
      )}

      {/* Ended Banner */}
      {isStatus(conversation.status, 'ended') && (
        <div className="p-4 bg-yellow-100 dark:bg-yellow-900/30 border-b border-yellow-200 dark:border-yellow-800">
          <div className="flex items-center text-yellow-800 dark:text-yellow-300">
            <AlertCircle size={16} className="mr-2" />
            <span className="text-sm">
              This conversation has been ended. No new messages can be sent.
            </span>
          </div>
        </div>
      )}

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-2 sm:p-4 space-y-3 sm:space-y-4">
        {messages.length === 0 ? (
          <div className="text-center text-gray-500 dark:text-gray-400 py-8">
            {conversation.status === 'accepted'
              ? 'No messages yet. Start the conversation!'
              : conversation.status === 'pending'
                ? 'You can exchange messages once the conversation request is accepted.'
                : 'This conversation request has been declined.'}
          </div>
        ) : (
          messages.map((message) => {
            const isSentByCurrentUser = isCurrentUser(message.sender_id);
            return (
              <div
                key={message.id}
                className={`flex ${isSentByCurrentUser ? 'justify-end' : 'justify-start'} mb-3`}
              >
                <div className={`flex items-end ${isSentByCurrentUser ? 'flex-row-reverse' : 'flex-row'}`}>
                  {/* Avatar - shown for both parties */}
                  <div className={`flex-shrink-0 ${isSentByCurrentUser ? 'ml-1.5 sm:ml-2' : 'mr-1.5 sm:mr-2'}`}>
                    {message.sender.avatar_url ? (
                      <Image
                        src={message.sender.avatar_url}
                        alt={message.sender.display_name || 'User'}
                        width={28}
                        height={28}
                        className="rounded-full w-7 h-7 sm:w-8 sm:h-8"
                      />
                    ) : (
                      <div className="w-7 h-7 sm:w-8 sm:h-8 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                        <span className="text-gray-600 dark:text-gray-300 text-xs font-medium">
                          {(message.sender.display_name || 'U').charAt(0).toUpperCase()}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Message bubble */}
                  <div
                    className={`max-w-[75vw] md:max-w-xs lg:max-w-md px-3 py-1.5 sm:px-4 sm:py-2 rounded-lg ${
                      isSentByCurrentUser
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-white'
                    }`}
                  >
                    {/* Sender name */}
                    <p className={`text-xs font-medium mb-0.5 ${
                      isSentByCurrentUser ? 'text-blue-200' : 'text-gray-500 dark:text-gray-400'
                    }`}>
                      {message.sender.display_name || 'Unknown User'}
                    </p>

                    {/* Message content */}
                    <p className="text-sm break-words">{message.content}</p>

                    {/* Timestamp */}
                    <p className={`text-xs mt-0.5 ${
                      isSentByCurrentUser ? 'text-blue-200' : 'text-gray-500 dark:text-gray-400'
                    }`}>
                      {formatMessageTime(message.created_at)}
                    </p>
                  </div>
                </div>
              </div>
            );
          })
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Error message */}
      {error && (
        <div className="px-3 py-1.5 sm:px-4 sm:py-2 bg-red-100 dark:bg-red-900/30 border-t border-red-200 dark:border-red-800">
          <div className="flex items-center text-red-800 dark:text-red-300">
            <AlertCircle size={16} className="mr-2 flex-shrink-0" />
            <span className="text-xs sm:text-sm">{error}</span>
          </div>
        </div>
      )}

      {/* Message input - only show if conversation is accepted and not ended */}
      {isStatus(conversation.status, 'accepted') && (
        <div className="p-2 sm:p-4 border-t border-gray-200 dark:border-gray-700">
          <form onSubmit={sendMessage} className="flex">
            <input
              type="text"
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              placeholder={isStatus(conversation.status, 'ended') ? "This conversation has ended" : "Type your message..."}
              className="flex-1 px-2 sm:px-4 py-2 text-sm bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              disabled={sending || isStatus(conversation.status, 'ended')}
            />
            <button
              type="submit"
              className="px-3 sm:px-4 py-2 bg-blue-600 text-white rounded-r-lg hover:bg-blue-700 transition disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={sending || !newMessage.trim() || isStatus(conversation.status, 'ended')}
            >
              {sending ? (
                <div className="animate-spin h-4 w-4 sm:h-5 sm:w-5 border-2 border-white border-t-transparent rounded-full"></div>
              ) : (
                <Send size={18} className="sm:w-5 sm:h-5" />
              )}
            </button>
          </form>
        </div>
      )}
    </div>
  );
}
