const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceRoleKey) {
  console.error('Error: Missing Supabase environment variables.');
  console.error('Make sure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set in .env.local');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);

async function testDatabase() {
  try {
    console.log('Testing Supabase database setup...');

    // Test 1: Check if tables exist
    console.log('\n1. Checking if tables exist...');
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .in('table_name', ['profiles', 'skills', 'sessions', 'reviews', 'ledger']);

    if (tablesError) {
      throw tablesError;
    }

    const existingTables = tables.map(t => t.table_name);
    const requiredTables = ['profiles', 'skills', 'sessions', 'reviews', 'ledger'];
    const missingTables = requiredTables.filter(t => !existingTables.includes(t));

    if (missingTables.length > 0) {
      console.error(`Missing tables: ${missingTables.join(', ')}`);
    } else {
      console.log('All required tables exist.');
    }

    // Test 2: Check if RLS is enabled
    console.log('\n2. Checking if RLS is enabled...');
    for (const table of requiredTables) {
      const { data: rls, error: rlsError } = await supabase
        .from('pg_tables')
        .select('rowsecurity')
        .eq('schemaname', 'public')
        .eq('tablename', table)
        .single();

      if (rlsError) {
        console.error(`Error checking RLS for ${table}: ${rlsError.message}`);
        continue;
      }

      console.log(`${table}: RLS ${rls.rowsecurity ? 'enabled' : 'disabled'}`);
    }

    // Test 3: Check if triggers exist
    console.log('\n3. Checking if triggers exist...');
    const { data: triggers, error: triggersError } = await supabase
      .from('information_schema.triggers')
      .select('trigger_name, event_manipulation, action_statement')
      .eq('trigger_schema', 'public');

    if (triggersError) {
      throw triggersError;
    }

    const requiredTriggers = ['trg_review_positive', 'on_auth_user_created'];
    const existingTriggers = triggers.map(t => t.trigger_name);
    const missingTriggers = requiredTriggers.filter(t => !existingTriggers.includes(t));

    if (missingTriggers.length > 0) {
      console.error(`Missing triggers: ${missingTriggers.join(', ')}`);
    } else {
      console.log('All required triggers exist.');
    }

    // Test 4: Check if functions exist
    console.log('\n4. Checking if functions exist...');
    const { data: functions, error: functionsError } = await supabase
      .from('information_schema.routines')
      .select('routine_name')
      .eq('routine_schema', 'public')
      .in('routine_name', ['apply_credits_on_positive', 'handle_new_user', 'resolve_disputes']);

    if (functionsError) {
      throw functionsError;
    }

    const existingFunctions = functions.map(f => f.routine_name);
    const requiredFunctions = ['apply_credits_on_positive', 'handle_new_user', 'resolve_disputes'];
    const missingFunctions = requiredFunctions.filter(f => !existingFunctions.includes(f));

    if (missingFunctions.length > 0) {
      console.error(`Missing functions: ${missingFunctions.join(', ')}`);
    } else {
      console.log('All required functions exist.');
    }

    console.log('\nDatabase testing completed!');
  } catch (error) {
    console.error('Error testing database:', error.message);
    process.exit(1);
  }
}

testDatabase();
