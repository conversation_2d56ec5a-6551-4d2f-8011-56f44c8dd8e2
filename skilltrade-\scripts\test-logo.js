/**
 * This script creates a simple HTML page to test the rounded logo and favicon.
 * 
 * To run this script:
 * 1. Run: node scripts/test-logo.js
 * 2. Open the generated test-logo.html file in a browser
 */

const fs = require('fs');
const path = require('path');

// Create a test HTML file
const testHtml = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Skilltrade Logo Test</title>
  
  <!-- Favicon links -->
  <link rel="icon" href="/favicon.ico" sizes="any">
  <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
  <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
  <link rel="apple-touch-icon" href="/apple-touch-icon.png">
  <link rel="manifest" href="/site.webmanifest">
  
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    
    h1 {
      text-align: center;
      margin-bottom: 30px;
    }
    
    .logo-container {
      display: flex;
      flex-wrap: wrap;
      gap: 30px;
      justify-content: center;
      margin-bottom: 40px;
    }
    
    .logo-item {
      text-align: center;
    }
    
    .logo-item img {
      display: block;
      margin-bottom: 10px;
      border: 1px solid #eee;
    }
    
    .favicon-container {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      justify-content: center;
      margin-bottom: 40px;
    }
    
    .favicon-item {
      text-align: center;
    }
    
    .favicon-item img {
      display: block;
      margin-bottom: 10px;
      border: 1px solid #eee;
    }
    
    .dark-bg {
      background-color: #111827;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 20px;
    }
    
    .light-bg {
      background-color: #f9fafb;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 20px;
    }
  </style>
</head>
<body>
  <h1>Skilltrade Logo Test</h1>
  
  <h2>Logo Versions</h2>
  <div class="logo-container">
    <div class="logo-item">
      <img src="/logo.png" alt="Original Logo" width="100">
      <p>Original Logo</p>
    </div>
    
    <div class="logo-item">
      <img src="/logo-rounded.png" alt="Rounded Logo" width="100">
      <p>Rounded Logo</p>
    </div>
    
    <div class="logo-item">
      <img src="/logo-circle.png" alt="Circular Logo" width="100">
      <p>Circular Logo</p>
    </div>
  </div>
  
  <h2>Logo on Dark Background</h2>
  <div class="dark-bg logo-container">
    <div class="logo-item">
      <img src="/logo.png" alt="Original Logo" width="100">
      <p style="color: white;">Original Logo</p>
    </div>
    
    <div class="logo-item">
      <img src="/logo-rounded.png" alt="Rounded Logo" width="100">
      <p style="color: white;">Rounded Logo</p>
    </div>
    
    <div class="logo-item">
      <img src="/logo-circle.png" alt="Circular Logo" width="100">
      <p style="color: white;">Circular Logo</p>
    </div>
  </div>
  
  <h2>Favicon Versions</h2>
  <div class="favicon-container">
    <div class="favicon-item">
      <img src="/favicon-16x16.png" alt="16x16 Favicon" width="16" height="16" style="width: 16px; height: 16px;">
      <p>16x16 Favicon</p>
    </div>
    
    <div class="favicon-item">
      <img src="/favicon-32x32.png" alt="32x32 Favicon" width="32" height="32" style="width: 32px; height: 32px;">
      <p>32x32 Favicon</p>
    </div>
    
    <div class="favicon-item">
      <img src="/apple-touch-icon.png" alt="Apple Touch Icon" width="60" height="60" style="width: 60px; height: 60px;">
      <p>Apple Touch Icon</p>
    </div>
    
    <div class="favicon-item">
      <img src="/android-chrome-192x192.png" alt="Android Chrome Icon" width="60" height="60" style="width: 60px; height: 60px;">
      <p>Android Chrome Icon</p>
    </div>
  </div>
  
  <h2>Header Preview</h2>
  <div class="light-bg">
    <div style="display: flex; align-items: center;">
      <img src="/logo-rounded.png" alt="Skilltrade Logo" width="40" height="40" style="margin-right: 10px;">
      <span style="font-size: 1.25rem; font-weight: bold;">Skilltrade</span>
    </div>
  </div>
  
  <div class="dark-bg">
    <div style="display: flex; align-items: center;">
      <img src="/logo-rounded.png" alt="Skilltrade Logo" width="40" height="40" style="margin-right: 10px;">
      <span style="font-size: 1.25rem; font-weight: bold; color: white;">Skilltrade</span>
    </div>
  </div>
  
  <p>
    <strong>Note:</strong> This page is for testing purposes only. The favicon should appear in your browser tab.
    Check that the rounded logo looks good in all contexts.
  </p>
</body>
</html>
`;

// Write the test HTML file
fs.writeFileSync(path.join(__dirname, '../public/test-logo.html'), testHtml);

console.log('✅ Created test HTML file: public/test-logo.html');
console.log('Open this file in your browser to test the logo and favicon');
console.log('You can access it at: http://localhost:3000/test-logo.html when your dev server is running');
