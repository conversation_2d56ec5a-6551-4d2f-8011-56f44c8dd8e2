import { Metadata } from 'next';
import AdminNav from '@/components/admin/AdminNav';
import { isAdmin } from '@/lib/admin-utils';
import { redirect } from 'next/navigation';

export const metadata: Metadata = {
  title: 'Admin Dashboard - Skilltrade',
  description: 'Admin dashboard for managing the Skilltrade platform',
};

export default async function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Check if the user is an admin
  const isUserAdmin = await isAdmin();
  
  // If not an admin, redirect to the dashboard
  if (!isUserAdmin) {
    redirect('/dashboard');
  }
  
  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900">
      <AdminNav />
      
      {/* Main content */}
      <div className="lg:ml-64 min-h-screen">
        <main className="p-6">{children}</main>
      </div>
    </div>
  );
}
