import { NextRequest, NextResponse } from 'next/server';
import { createServerSide } from '@/lib/supabase-server';
import { createAdminClient } from '@/lib/supabase-admin';

export const dynamic = 'force-dynamic';

// GET /api/conversations/[id] - Get a specific conversation and its messages
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createServerSide();

    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const conversationId = params.id;

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const limit = parseInt(searchParams.get('limit') || '50');
    const page = parseInt(searchParams.get('page') || '1');
    const offset = (page - 1) * limit;

    // Use the admin client to bypass RLS
    const adminSupabase = createAdminClient();

    // Check if the user is a participant in the conversation
    const { data: participant, error: participantError } = await adminSupabase
      .from('conversation_participants')
      .select('*')
      .eq('conversation_id', conversationId)
      .eq('user_id', user.id)
      .single();

    if (participantError) {
      console.error('Participant check error:', participantError);
      console.log('User ID:', user.id);
      console.log('Conversation ID:', conversationId);

      // Log all participants for this conversation to debug
      const { data: allParticipants } = await adminSupabase
        .from('conversation_participants')
        .select('user_id')
        .eq('conversation_id', conversationId);

      console.log('All participants:', allParticipants);

      return NextResponse.json(
        { error: 'Conversation not found or you are not a participant' },
        { status: 404 }
      );
    }

    // Get conversation details
    const { data: conversation, error: conversationError } = await adminSupabase
      .from('conversations')
      .select(`
        *,
        participants:conversation_participants(
          user_id,
          last_read_at,
          user:profiles(
            id,
            display_name,
            avatar_url
          )
        )
      `)
      .eq('id', conversationId)
      .single();

    if (conversationError) {
      throw conversationError;
    }

    // Get messages for the conversation
    const { data: messages, error: messagesError, count } = await adminSupabase
      .from('direct_messages')
      .select(`
        *,
        sender:profiles(
          id,
          display_name,
          avatar_url
        )
      `, { count: 'exact' })
      .eq('conversation_id', conversationId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (messagesError) {
      throw messagesError;
    }

    // Process the conversation to include other participant info
    const otherParticipants = conversation.participants
      .filter((p: any) => p.user_id !== user.id)
      .map((p: any) => p.user);

    console.log('Conversation data:', conversation);
    console.log('Other participants:', otherParticipants);

    // Determine who initiated the conversation
    // In our system, user1_id in get_or_create_conversation is the initiator
    // We can determine this by checking who created the notification
    const { data: notifications, error: notificationsError } = await adminSupabase
      .from('notifications')
      .select('*')
      .eq('type', 'conversation_request')
      .eq('link', `/dashboard/messages?id=${conversationId}`)
      .order('created_at', { ascending: false })
      .limit(1);

    console.log('Conversation request notifications:', notifications);

    // If we found a notification, the recipient is the user who received it
    let isRecipient = false;
    if (notifications && notifications.length > 0) {
      isRecipient = notifications[0].user_id === user.id;
    }

    console.log('Current user is recipient:', isRecipient);

    const processedConversation = {
      id: conversation.id,
      last_message_preview: conversation.last_message_preview,
      last_message_at: conversation.last_message_at,
      created_at: conversation.created_at,
      updated_at: conversation.updated_at,
      status: conversation.status || 'pending', // Ensure status is always set
      other_participants: otherParticipants,
      is_recipient: isRecipient // Add this flag to indicate if the current user is the recipient
    };

    console.log('Processed conversation:', processedConversation);

    return NextResponse.json({
      conversation: processedConversation,
      messages: messages?.reverse() || [], // Reverse to get chronological order
      count: count || 0,
      page,
      limit
    });
  } catch (error: any) {
    console.error('Error fetching conversation:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch conversation' },
      { status: 500 }
    );
  }
}
