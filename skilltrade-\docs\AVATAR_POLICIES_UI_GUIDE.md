# Setting Up Avatar Storage Policies in Supabase UI

This guide provides detailed steps with screenshots for setting up avatar storage policies directly through the Supabase dashboard UI.

## 1. Access the Storage Section

1. Log in to the [Supabase Dashboard](https://app.supabase.com/)
2. Select your project
3. Click on "Storage" in the left sidebar

![Supabase Dashboard - Storage](https://i.imgur.com/JQZXhGY.png)

## 2. Create the Avatars Bucket (if not already created)

1. Click on "New Bucket"
2. Enter "avatars" as the bucket name
3. Check "Public bucket" to make it accessible to everyone
4. Click "Create bucket"

![Create Bucket](https://i.imgur.com/8FYZnJY.png)

## 3. Access the Avatars Bucket Policies

1. Click on the "avatars" bucket in the list
2. Click on the "Policies" tab

![Bucket Policies Tab](https://i.imgur.com/ZGYQmDJ.png)

## 4. Create Policy for Viewing Avatars (SELECT)

1. Click "Add Policy" or "New Policy"
2. In the policy creation form:
   - Select "GET" or "SELECT" as the operation
   - Enter "Avatars are viewable by everyone" as the policy name
   - Select "Custom" for the policy definition
   - Enter `true` in the definition field (or select "Allow access to everyone" if available)
   - Click "Save Policy"

![Create SELECT Policy](https://i.imgur.com/QZvjnLw.png)

## 5. Create Policy for Uploading Avatars (INSERT)

1. Click "Add Policy" or "New Policy" again
2. In the policy creation form:
   - Select "INSERT" as the operation
   - Enter "Users can upload their own avatars" as the policy name
   - Select "Custom" for the policy definition
   - Enter `auth.role() = 'authenticated'` in the definition field (or select "Allow authenticated users" if available)
   - Click "Save Policy"

![Create INSERT Policy](https://i.imgur.com/Y5gZQJL.png)

## 6. Create Policy for Updating Avatars (UPDATE)

1. Click "Add Policy" or "New Policy" again
2. In the policy creation form:
   - Select "UPDATE" as the operation
   - Enter "Users can update their own avatars" as the policy name
   - Select "Custom" for the policy definition
   - Enter `auth.role() = 'authenticated'` in the definition field
   - Click "Save Policy"

![Create UPDATE Policy](https://i.imgur.com/nXYZmFc.png)

## 7. Create Policy for Deleting Avatars (DELETE)

1. Click "Add Policy" or "New Policy" again
2. In the policy creation form:
   - Select "DELETE" as the operation
   - Enter "Users can delete their own avatars" as the policy name
   - Select "Custom" for the policy definition
   - Enter `auth.role() = 'authenticated'` in the definition field
   - Click "Save Policy"

![Create DELETE Policy](https://i.imgur.com/QZvjnLw.png)

## 8. Verify All Policies

After creating all four policies, you should see them listed in the Policies tab:

![All Policies](https://i.imgur.com/ZGYQmDJ.png)

## 9. Advanced Policies (Optional)

If you want to ensure users can only access their own folders, you can create more specific policies. For example, for the INSERT policy:

1. Edit the INSERT policy
2. Change the definition to:
   ```
   auth.uid()::text = storage.foldername(name)[1]
   ```
   or
   ```
   auth.uid()::text = (storage.foldername(name))[1]
   ```

This ensures users can only upload files to folders that match their user ID.

## 10. Testing the Policies

To test if the policies are working correctly:

1. Sign in to your application
2. Try to upload an avatar using the AvatarUpload component
3. Verify that the avatar is visible
4. Try to update or delete the avatar

If any of these operations fail, check the browser console for error messages and verify the policy definitions in the Supabase dashboard.

## Troubleshooting

If you encounter issues:

1. **Permission denied errors**: Check that the policies are correctly defined and that you're using the correct user authentication.

2. **Upload errors**: Make sure the bucket is created and that the INSERT policy is correctly defined.

3. **File not found errors**: Check that the SELECT policy is correctly defined and that the file path is correct.

4. **Update/Delete errors**: Verify that the UPDATE and DELETE policies are correctly defined and that you're trying to modify your own files.
