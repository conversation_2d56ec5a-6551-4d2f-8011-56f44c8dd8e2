-- Create a function to create notifications that bypasses <PERSON><PERSON>
CREATE OR <PERSON><PERSON>LACE FUNCTION create_notification(
  user_id_param UUID,
  type_param TEXT,
  title_param TEXT,
  message_param TEXT,
  link_param TEXT DEFAULT NULL
)
RETURNS void
SECURITY DEFINER -- This makes the function run with the privileges of the creator
AS $$
DECLARE
  has_message_column BOOLEAN;
  has_content_column BOOLEAN;
  has_link_column BOOLEAN;
BEGIN
  -- Check which columns exist in the notifications table
  SELECT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = 'notifications' 
    AND column_name = 'message'
  ) INTO has_message_column;
  
  SELECT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = 'notifications' 
    AND column_name = 'content'
  ) INTO has_content_column;
  
  SELECT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = 'notifications' 
    AND column_name = 'link'
  ) INTO has_link_column;
  
  -- Create notification based on available columns
  IF has_message_column AND has_link_column THEN
    -- Case 1: Both message and link columns exist
    INSERT INTO notifications (
      user_id,
      type,
      title,
      message,
      link,
      is_read
    ) VALUES (
      user_id_param,
      type_param,
      title_param,
      message_param,
      link_param,
      false
    );
  ELSIF has_content_column AND has_link_column THEN
    -- Case 2: Content and link columns exist
    INSERT INTO notifications (
      user_id,
      type,
      title,
      content,
      link,
      is_read
    ) VALUES (
      user_id_param,
      type_param,
      title_param,
      message_param,
      link_param,
      false
    );
  ELSIF has_message_column AND NOT has_link_column THEN
    -- Case 3: Message column exists but no link column
    INSERT INTO notifications (
      user_id,
      type,
      title,
      message,
      is_read
    ) VALUES (
      user_id_param,
      type_param,
      title_param,
      message_param,
      false
    );
  ELSIF has_content_column AND NOT has_link_column THEN
    -- Case 4: Content column exists but no link column
    INSERT INTO notifications (
      user_id,
      type,
      title,
      content,
      is_read
    ) VALUES (
      user_id_param,
      type_param,
      title_param,
      message_param,
      false
    );
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Ensure the notifications table has RLS enabled but with appropriate policies
DO $$
BEGIN
  -- Enable RLS on notifications table if it exists
  IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'notifications') THEN
    -- Enable RLS
    ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
    
    -- Drop existing policies if any
    DROP POLICY IF EXISTS "Users can view their own notifications" ON notifications;
    DROP POLICY IF EXISTS "Users can update their own notifications" ON notifications;
    
    -- Create policies
    CREATE POLICY "Users can view their own notifications" 
      ON notifications FOR SELECT 
      USING (auth.uid() = user_id);
      
    CREATE POLICY "Users can update their own notifications" 
      ON notifications FOR UPDATE 
      USING (auth.uid() = user_id);
  END IF;
END
$$;
