'use client';

import { MessageSquare } from 'lucide-react';

export default function EmptyState() {
  return (
    <div className="flex flex-col items-center justify-center h-full p-8 text-center">
      <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mb-4">
        <MessageSquare size={32} className="text-blue-600 dark:text-blue-400" />
      </div>
      <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
        Your Messages
      </h3>
      <p className="text-gray-500 dark:text-gray-400 max-w-md mb-6">
        Select a conversation from the list or start a new one to begin messaging with other users.
      </p>
    </div>
  );
}
