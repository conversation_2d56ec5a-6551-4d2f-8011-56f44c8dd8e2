import { NextRequest, NextResponse } from 'next/server';
import { createAdminClient } from '@/lib/supabase-admin';
import { isAdmin } from '@/lib/admin-utils';

export const dynamic = 'force-dynamic';

// GET /api/admin/sessions - Get all sessions
export async function GET(request: NextRequest) {
  try {
    // Check if the user is an admin
    const isUserAdmin = await isAdmin();
    if (!isUserAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    const supabase = createAdminClient();

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const status = searchParams.get('status') || 'all';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = (page - 1) * limit;

    // Build the query
    let sessionsQuery = supabase
      .from('sessions')
      .select(`
        *,
        skill:skills(id, title),
        teacher:profiles!sessions_teacher_id_fkey(id, display_name, email, avatar_url),
        learner:profiles!sessions_learner_id_fkey(id, display_name, email, avatar_url)
      `, { count: 'exact' });

    // Add status filter if provided
    if (status !== 'all') {
      sessionsQuery = sessionsQuery.eq('status', status);
    }

    // Add pagination
    sessionsQuery = sessionsQuery
      .order('scheduled_at', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data: sessions, count, error } = await sessionsQuery;

    if (error) {
      throw error;
    }

    return NextResponse.json({
      sessions,
      total: count || 0,
      page,
      limit,
      totalPages: count ? Math.ceil(count / limit) : 0,
    });
  } catch (error: any) {
    console.error('Error fetching sessions:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch sessions' },
      { status: 500 }
    );
  }
}
