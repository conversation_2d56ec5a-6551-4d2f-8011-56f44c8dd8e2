import { NextRequest, NextResponse } from 'next/server';
import { createServerSide } from '@/lib/supabase-server';
import { createAdminClient } from '@/lib/supabase-admin';
import { validateText } from '@/lib/bad-words-filter';

// POST /api/sessions/messages - Create a new session message with bad word filtering
export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerSide();

    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { session_id, content } = body;

    // Validate required fields
    if (!session_id || !content) {
      return NextResponse.json(
        { error: 'Session ID and content are required' },
        { status: 400 }
      );
    }

    // Check if the user is a participant in the session
    const { data: session, error: sessionError } = await supabase
      .from('sessions')
      .select('teacher_id, learner_id')
      .eq('id', session_id)
      .single();

    if (sessionError) {
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 404 }
      );
    }

    // Verify the user is either the teacher or learner
    if (session.teacher_id !== user.id && session.learner_id !== user.id) {
      return NextResponse.json(
        { error: 'You are not a participant in this session' },
        { status: 403 }
      );
    }

    // Use the admin client to bypass RLS
    const adminSupabase = createAdminClient();

    // Validate and censor content for bad words
    console.log('Validating session message content for bad words');
    const validation = await validateText(content);
    console.log('Validation result:', validation);

    let messageContent = content;
    let wasCensored = false;

    // If bad words are found, censor them
    if (!validation.isValid) {
      console.log('Bad words found, censoring content');
      messageContent = validation.censoredText;
      wasCensored = true;
    }

    // Make sure messageContent is never null or empty
    if (!messageContent || messageContent.trim() === '') {
      console.log('Message content is empty after censoring, using placeholder');
      messageContent = '[Message was censored due to inappropriate content]';
    }

    console.log('Final message content to insert:', messageContent);

    // Insert the message with censored content if needed
    const { data: message, error: messageError } = await adminSupabase
      .from('session_messages')
      .insert({
        session_id,
        sender_id: user.id,
        content: messageContent,
      })
      .select()
      .single();

    if (messageError) {
      throw messageError;
    }

    // Return the message with a flag indicating if it was censored
    return NextResponse.json({
      message,
      censored: wasCensored,
      badWords: wasCensored ? validation.badWords : [],
    });
  } catch (error: any) {
    console.error('Error creating session message:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to create message' },
      { status: 500 }
    );
  }
}

// GET /api/sessions/messages?session_id={session_id} - Get messages for a session
export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerSide();

    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const session_id = searchParams.get('session_id');

    if (!session_id) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      );
    }

    // Check if the user is a participant in the session
    const { data: session, error: sessionError } = await supabase
      .from('sessions')
      .select('teacher_id, learner_id')
      .eq('id', session_id)
      .single();

    if (sessionError) {
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 404 }
      );
    }

    // Verify the user is either the teacher or learner
    if (session.teacher_id !== user.id && session.learner_id !== user.id) {
      return NextResponse.json(
        { error: 'You are not a participant in this session' },
        { status: 403 }
      );
    }

    // Use the admin client to bypass RLS
    const adminSupabase = createAdminClient();

    // Get messages for the session
    const { data: messages, error: messagesError } = await adminSupabase
      .from('session_messages')
      .select(`
        *,
        sender:profiles(display_name, avatar_url)
      `)
      .eq('session_id', session_id)
      .order('created_at', { ascending: true });

    if (messagesError) {
      throw messagesError;
    }

    return NextResponse.json({
      messages: messages || [],
    });
  } catch (error: any) {
    console.error('Error fetching session messages:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch messages' },
      { status: 500 }
    );
  }
}
