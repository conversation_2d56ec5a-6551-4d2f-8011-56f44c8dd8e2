'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Pencil, Plus, X, ExternalLink, Image as ImageIcon } from 'lucide-react';
import Image from 'next/image';

interface PortfolioItem {
  id: string;
  title: string;
  description: string | null;
  imageUrl: string | null;
  linkUrl: string | null;
}

interface PortfolioProps {
  portfolioItems: PortfolioItem[];
  isEditable?: boolean;
  onSave?: (items: PortfolioItem[]) => Promise<void>;
  onUploadImage?: (file: File) => Promise<string>;
}

// Added animation wrapper
const AnimatedComponent = ({ children }: { children: React.ReactNode }) => (
  <div className="animate-fadeIn">
    {children}
  </div>
);

export default function Portfolio({
  portfolioItems = [],
  isEditable = false,
  onSave,
  onUploadImage,
}: PortfolioProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [items, setItems] = useState<PortfolioItem[]>(portfolioItems);
  const [newItem, setNewItem] = useState<Partial<PortfolioItem>>({
    title: '',
    description: '',
    imageUrl: null,
    linkUrl: '',
  });
  const [isAddingItem, setIsAddingItem] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [uploadingImage, setUploadingImage] = useState(false);

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = async () => {
    if (!onSave) return;

    try {
      setIsSaving(true);
      await onSave(items);
      setIsEditing(false);
    } catch (error) {
      console.error('Error saving portfolio items:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setItems(portfolioItems);
    setNewItem({
      title: '',
      description: '',
      imageUrl: null,
      linkUrl: '',
    });
    setIsAddingItem(false);
    setIsEditing(false);
  };

  const handleAddItem = () => {
    if (!newItem.title) return;

    const item: PortfolioItem = {
      id: `temp-${Date.now()}`, // Temporary ID, will be replaced on save
      title: newItem.title,
      description: newItem.description || null,
      imageUrl: newItem.imageUrl || null,
      linkUrl: newItem.linkUrl || null,
    };

    setItems([...items, item]);
    setNewItem({
      title: '',
      description: '',
      imageUrl: null,
      linkUrl: '',
    });
    setIsAddingItem(false);
  };

  const handleRemoveItem = (index: number) => {
    setItems(items.filter((_, i) => i !== index));
  };

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!onUploadImage || !e.target.files || e.target.files.length === 0) return;

    const file = e.target.files[0];

    try {
      setUploadingImage(true);
      const imageUrl = await onUploadImage(file);
      setNewItem({ ...newItem, imageUrl });
    } catch (error) {
      console.error('Error uploading image:', error);
    } finally {
      setUploadingImage(false);
    }
  };

  return (
    <AnimatedComponent>
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm transition-colors duration-200">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Portfolio</h3>
          {isEditable && !isEditing && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleEdit}
              className="text-gray-500 hover:text-gray-700 transition duration-200 dark:text-gray-400 dark:hover:text-gray-300">
              <Pencil className="h-4 w-4 mr-2" />
              Edit
            </Button>
          )}
        </div>

        {isEditing ? (
          <div className="space-y-4">
            {!isAddingItem ? (
              <Button
                onClick={() => setIsAddingItem(true)}
                className="w-full"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Portfolio Item
              </Button>
            ) : (
              <div className="bg-gray-50 dark:bg-gray-700 p-6 rounded-xl space-y-3 transition-colors duration-200">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">
                    Title *
                  </label>
                  <input
                    type="text"
                    value={newItem.title || ''}
                    onChange={(e) => setNewItem({ ...newItem, title: e.target.value })}
                    placeholder="Project or work sample title"
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">
                    Description
                  </label>
                  <textarea
                    value={newItem.description || ''}
                    onChange={(e) => setNewItem({ ...newItem, description: e.target.value })}
                    placeholder="Briefly describe this work sample..."
                    className="w-full h-20 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white resize-none"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">
                    External Link
                  </label>
                  <input
                    type="url"
                    value={newItem.linkUrl || ''}
                    onChange={(e) => setNewItem({ ...newItem, linkUrl: e.target.value })}
                    placeholder="https://example.com/your-project"
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">
                    Image
                  </label>
                  {newItem.imageUrl ? (
                    <div className="relative w-full h-40 bg-gray-200 dark:bg-gray-600 rounded-xl overflow-hidden transition-colors duration-200">
                      <Image
                        src={newItem.imageUrl}
                        alt={newItem.title || 'Portfolio item'}
                        fill
                        className="object-cover"
                      />
                      <button
                        onClick={() => setNewItem({ ...newItem, imageUrl: null })}
                        className="absolute top-2 right-2 bg-red-500 text-white p-6 rounded-xl"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center w-full">
                      <label className="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-xl cursor-pointer bg-gray-50 dark:hover:bg-gray-600 dark:bg-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:hover:border-gray-500">
                        <div className="flex flex-col items-center justify-center pt-5 pb-6">
                          <ImageIcon className="w-8 h-8 mb-4 text-gray-500 dark:text-gray-400" />
                          <p className="mb-4 text-sm text-gray-500 dark:text-gray-400">
                            <span className="font-semibold">Click to upload</span> or drag and drop
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            PNG, JPG or GIF (MAX. 2MB)
                          </p>
                        </div>
                        <input
                          type="file"
                          className="hidden"
                          accept="image/*"
                          onChange={handleImageUpload}
                          disabled={uploadingImage}
                        />
                      </label>
                    </div>
                  )}
                  {uploadingImage && (
                    <p className="text-sm text-blue-500 mt-1">Uploading image...</p>
                  )}
                </div>

                <div className="flex space-x-2">
                  <Button
                    onClick={handleAddItem}
                    disabled={!newItem.title || uploadingImage}
                  >
                    Add Item
                  </Button>
                  <Button
                    variant="outline"
                    className="transition duration-200"
                    onClick={() => {
                      setNewItem({
                        title: '',
                        description: '',
                        imageUrl: null,
                        linkUrl: '',
                      });
                      setIsAddingItem(false);
                    }}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            )}

            <div className="mt-6 space-y-4">
              <h4 className="font-medium text-gray-900 dark:text-white">Your Portfolio Items</h4>
              {items.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {items.map((item, index) => (
                    <div
                      key={index}
                      className="bg-gray-100 dark:bg-gray-700 rounded-xl overflow-hidden"
                    >
                      {item.imageUrl && (
                        <div className="relative w-full h-40">
                          <Image
                            src={item.imageUrl}
                            alt={item.title}
                            fill
                            className="object-cover"
                          />
                        </div>
                      )}
                      <div className="p-6">
                        <div className="flex justify-between items-start">
                          <h5 className="font-medium text-gray-800 dark:text-gray-200">
                            {item.title}
                          </h5>
                          <button
                            onClick={() => handleRemoveItem(index)}
                            className="text-gray-500 hover:text-red-500 transition duration-200 dark:text-gray-400 dark:hover:text-red-400"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        </div>
                        {item.description && (
                          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                            {item.description}
                          </p>
                        )}
                        {item.linkUrl && (
                          <a
                            href={item.linkUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center text-sm text-blue-600 dark:text-blue-400 mt-2 hover:underline"
                          >
                            <ExternalLink className="h-3 w-3 mr-1" />
                            View Project
                          </a>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 dark:text-gray-400 italic">
                  No portfolio items added yet. Add items to showcase your work.
                </p>
              )}
            </div>

            <div className="flex space-x-2">
              <Button onClick={handleSave} disabled={isSaving}>
                {isSaving ? 'Saving...' : 'Save'}
              </Button>
              <Button variant="outline" onClick={handleCancel} disabled={isSaving} className="transition duration-200">
                Cancel
              </Button>
            </div>
          </div>
        ) : (
          <div>
            {items.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {items.map((item, index) => (
                  <div
                    key={index}
                    className="bg-gray-100 dark:bg-gray-700 rounded-xl overflow-hidden"
                  >
                    {item.imageUrl && (
                      <div className="relative w-full h-40">
                        <Image
                          src={item.imageUrl}
                          alt={item.title}
                          fill
                          className="object-cover"
                        />
                      </div>
                    )}
                    <div className="p-6">
                      <h5 className="font-medium text-gray-800 dark:text-gray-200">
                        {item.title}
                      </h5>
                      {item.description && (
                        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                          {item.description}
                        </p>
                      )}
                      {item.linkUrl && (
                        <a
                          href={item.linkUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center text-sm text-blue-600 dark:text-blue-400 mt-2 hover:underline"
                        >
                          <ExternalLink className="h-3 w-3 mr-1" />
                          View Project
                        </a>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 dark:text-gray-400 italic">
                No portfolio items provided yet.
                {isEditable && ' Click Edit to add your work samples.'}
              </p>
            )}
          </div>
        )}
      </div>
    </AnimatedComponent>
  );
}
