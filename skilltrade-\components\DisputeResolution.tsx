'use client';

import { useState, useEffect } from 'react';
import { createClientSide } from '@/lib/supabase';
import { User } from '@supabase/supabase-js';

interface DisputeResolutionProps {
  sessionId: string;
  teacherId: string;
  learnerId: string;
  currentUser: User;
  onResolutionComplete: () => void;
}

interface Resolution {
  id: string;
  session_id: string;
  proposed_by: string;
  resolution_type: string;
  credit_adjustment: number | null;
  description: string;
  status: string;
  created_at: string;
}

export default function DisputeResolution({
  sessionId,
  teacherId,
  learnerId,
  currentUser,
  onResolutionComplete
}: DisputeResolutionProps) {
  const [resolutions, setResolutions] = useState<Resolution[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [message, setMessage] = useState<string | null>(null);
  const [resolutionType, setResolutionType] = useState<string>('credit_adjustment');
  const [creditAdjustment, setCreditAdjustment] = useState<number>(0);
  const [description, setDescription] = useState<string>('');
  const [showForm, setShowForm] = useState<boolean>(false);
  const supabase = createClientSide();

  const isTeacher = currentUser.id === teacherId;

  useEffect(() => {
    fetchResolutions();

    // Set up real-time subscription
    const channel = supabase
      .channel(`session_resolutions:${sessionId}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'dispute_resolutions',
        filter: `session_id=eq.${sessionId}`
      }, () => {
        fetchResolutions();
      })
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [sessionId]);

  const fetchResolutions = async () => {
    try {
      setLoading(true);

      const { data, error } = await supabase
        .from('dispute_resolutions')
        .select('*')
        .eq('session_id', sessionId)
        .order('created_at', { ascending: true });

      if (error) {
        throw error;
      }

      setResolutions(data || []);
    } catch (error: any) {
      setError(error.message || 'Failed to load resolutions');
    } finally {
      setLoading(false);
    }
  };

  const proposeResolution = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setSubmitting(true);
      setError(null);
      setMessage(null);

      // Validate inputs
      if (resolutionType === 'credit_adjustment' && creditAdjustment === 0) {
        throw new Error('Please specify a credit adjustment amount');
      }

      if (!description.trim()) {
        throw new Error('Please provide a description for the resolution');
      }

      // Insert resolution
      const { error } = await supabase
        .from('dispute_resolutions')
        .insert({
          session_id: sessionId,
          proposed_by: currentUser.id,
          resolution_type: resolutionType,
          credit_adjustment: resolutionType === 'credit_adjustment' ? creditAdjustment : null,
          description: description.trim(),
          status: 'proposed'
        });

      if (error) {
        throw error;
      }

      // Reset form
      setResolutionType('credit_adjustment');
      setCreditAdjustment(0);
      setDescription('');
      setShowForm(false);
      setMessage('Resolution proposed successfully');

      // Create notification for the other party
      const otherUserId = isTeacher ? learnerId : teacherId;

      try {
        // Use the RPC function to create notification
        await supabase.rpc('create_notification', {
          user_id_param: otherUserId,
          type_param: 'dispute',
          title_param: 'New Resolution Proposal',
          message_param: `A new resolution has been proposed for your disputed session.`,
          link_param: `/dashboard/sessions/${sessionId}`
        });
      } catch (error: any) {
        console.error('Failed to create notification:', error);
      }

    } catch (error: any) {
      setError(error.message || 'Failed to propose resolution');
    } finally {
      setSubmitting(false);
    }
  };

  const acceptResolution = async (resolutionId: string) => {
    try {
      setSubmitting(true);
      setError(null);
      setMessage(null);

      // Update resolution status
      const { error: updateError } = await supabase
        .from('dispute_resolutions')
        .update({ status: 'accepted' })
        .eq('id', resolutionId);

      if (updateError) {
        throw updateError;
      }

      // Update session status
      const { error: sessionError } = await supabase
        .from('sessions')
        .update({ status: 'resolved' })
        .eq('id', sessionId);

      if (sessionError) {
        throw sessionError;
      }

      // Get resolution details
      const { data: resolution, error: resolutionError } = await supabase
        .from('dispute_resolutions')
        .select('*')
        .eq('id', resolutionId)
        .single();

      if (resolutionError) {
        throw resolutionError;
      }

      // Apply credit adjustment if needed
      if (resolution && resolution.resolution_type === 'credit_adjustment' && resolution.credit_adjustment) {
        // Adjust teacher credits
        const { error: teacherError } = await supabase.rpc('adjust_user_credits', {
          user_id_param: teacherId,
          amount_param: resolution.credit_adjustment
        });

        if (teacherError) {
          throw teacherError;
        }

        // Adjust learner credits (opposite of teacher)
        const { error: learnerError } = await supabase.rpc('adjust_user_credits', {
          user_id_param: learnerId,
          amount_param: -resolution.credit_adjustment
        });

        if (learnerError) {
          throw learnerError;
        }
      }

      setMessage('Resolution accepted and applied successfully');

      // Create notification for the other party
      const otherUserId = isTeacher ? learnerId : teacherId;

      try {
        // Use the RPC function to create notification
        await supabase.rpc('create_notification', {
          user_id_param: otherUserId,
          type_param: 'dispute',
          title_param: 'Resolution Accepted',
          message_param: `Your resolution proposal has been accepted and the dispute is now resolved.`,
          link_param: `/dashboard/sessions/${sessionId}`
        });
      } catch (error: any) {
        console.error('Failed to create notification:', error);
      }

      // Call the completion callback
      onResolutionComplete();

    } catch (error: any) {
      setError(error.message || 'Failed to accept resolution');
    } finally {
      setSubmitting(false);
    }
  };

  const rejectResolution = async (resolutionId: string) => {
    try {
      setSubmitting(true);
      setError(null);
      setMessage(null);

      // Update resolution status
      const { error } = await supabase
        .from('dispute_resolutions')
        .update({ status: 'rejected' })
        .eq('id', resolutionId);

      if (error) {
        throw error;
      }

      setMessage('Resolution rejected');

      // Create notification for the other party
      const otherUserId = isTeacher ? learnerId : teacherId;

      try {
        // Use the RPC function to create notification
        await supabase.rpc('create_notification', {
          user_id_param: otherUserId,
          type_param: 'dispute',
          title_param: 'Resolution Rejected',
          message_param: `Your resolution proposal has been rejected. Please continue the discussion to find a solution.`,
          link_param: `/dashboard/sessions/${sessionId}`
        });
      } catch (error: any) {
        console.error('Failed to create notification:', error);
      }

    } catch (error: any) {
      setError(error.message || 'Failed to reject resolution');
    } finally {
      setSubmitting(false);
    }
  };

  const getResolutionStatusBadge = (status: string) => {
    switch (status) {
      case 'proposed':
        return (
          <span className="px-2 py-1 text-xs rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
            Proposed
          </span>
        );
      case 'accepted':
        return (
          <span className="px-2 py-1 text-xs rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
            Accepted
          </span>
        );
      case 'rejected':
        return (
          <span className="px-2 py-1 text-xs rounded-full bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300">
            Rejected
          </span>
        );
      default:
        return (
          <span className="px-2 py-1 text-xs rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300">
            {status}
          </span>
        );
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
      <div className="p-4 bg-red-100 dark:bg-red-900/20 border-b border-red-200 dark:border-red-800">
        <h3 className="text-lg font-semibold text-red-800 dark:text-red-300">Dispute Resolution</h3>
        <p className="text-sm text-red-700 dark:text-red-400">
          This session is in dispute. Work together to find a resolution.
        </p>
      </div>

      {error && (
        <div className="p-4 bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-300 text-sm">
          {error}
        </div>
      )}

      {message && (
        <div className="p-4 bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300 text-sm">
          {message}
        </div>
      )}

      <div className="p-4">
        {loading ? (
          <div className="flex justify-center items-center h-20">
            <div className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
            <p className="ml-2 text-gray-600 dark:text-gray-400">Loading resolutions...</p>
          </div>
        ) : (
          <>
            {resolutions.length > 0 ? (
              <div className="space-y-4 mb-4">
                <h4 className="font-medium text-gray-900 dark:text-white">Proposed Resolutions</h4>
                {resolutions.map((resolution) => (
                  <div
                    key={resolution.id}
                    className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4"
                  >
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <span className="font-medium text-gray-900 dark:text-white">
                          {resolution.proposed_by === teacherId ? 'Teacher' : 'Learner'} proposed:
                        </span>
                        {resolution.resolution_type === 'credit_adjustment' && resolution.credit_adjustment && (
                          <span className="ml-2 text-sm">
                            Credit adjustment:
                            <span className={resolution.credit_adjustment > 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}>
                              {resolution.credit_adjustment > 0 ? ' +' : ' '}
                              {resolution.credit_adjustment}
                            </span>
                            {resolution.credit_adjustment > 0
                              ? ' (to teacher)'
                              : ' (to learner)'}
                          </span>
                        )}
                      </div>
                      {getResolutionStatusBadge(resolution.status)}
                    </div>
                    <p className="text-gray-700 dark:text-gray-300 text-sm mb-3">
                      {resolution.description}
                    </p>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Proposed {new Date(resolution.created_at).toLocaleString()}
                    </div>

                    {resolution.status === 'proposed' && resolution.proposed_by !== currentUser.id && (
                      <div className="mt-3 flex space-x-3">
                        <button
                          onClick={() => acceptResolution(resolution.id)}
                          className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 transition disabled:opacity-50"
                          disabled={submitting}
                        >
                          Accept
                        </button>
                        <button
                          onClick={() => rejectResolution(resolution.id)}
                          className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 transition disabled:opacity-50"
                          disabled={submitting}
                        >
                          Reject
                        </button>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4 text-gray-600 dark:text-gray-400">
                <p>No resolutions have been proposed yet.</p>
                <p className="text-sm mt-1">Use the form below to propose a resolution.</p>
              </div>
            )}

            {!showForm ? (
              <button
                onClick={() => setShowForm(true)}
                className="w-full py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
              >
                Propose a Resolution
              </button>
            ) : (
              <form onSubmit={proposeResolution} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 dark:text-white mb-4">Propose a Resolution</h4>

                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Resolution Type
                  </label>
                  <select
                    value={resolutionType}
                    onChange={(e) => setResolutionType(e.target.value)}
                    className="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="credit_adjustment">Credit Adjustment</option>
                    <option value="other">Other Solution</option>
                  </select>
                </div>

                {resolutionType === 'credit_adjustment' && (
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Credit Adjustment (positive = to teacher, negative = to learner)
                    </label>
                    <input
                      type="number"
                      value={creditAdjustment}
                      onChange={(e) => setCreditAdjustment(parseInt(e.target.value))}
                      className="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      Example: +2 means the teacher gets 2 credits, -2 means the learner gets 2 credits
                    </p>
                  </div>
                )}

                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Description
                  </label>
                  <textarea
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    rows={4}
                    className="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Explain your proposed resolution..."
                    required
                  ></textarea>
                </div>

                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => setShowForm(false)}
                    className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition"
                    disabled={submitting}
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition disabled:opacity-50"
                    disabled={submitting}
                  >
                    {submitting ? 'Submitting...' : 'Submit Proposal'}
                  </button>
                </div>
              </form>
            )}
          </>
        )}
      </div>
    </div>
  );
}
