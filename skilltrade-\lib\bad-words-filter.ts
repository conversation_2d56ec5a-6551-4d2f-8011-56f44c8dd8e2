/**
 * Bad Word Filter Utility for Skilltrade
 *
 * This utility provides functions to detect and censor bad words in text.
 * It can be used for validating skill titles, descriptions, and chat messages.
 *
 * This version uses the database to store and retrieve bad words.
 */

import { createClientSide } from './supabase';
import { createAdminClient } from './supabase-admin';
import { createServerSide } from './supabase-server';

// Interface for bad words
export interface BadWord {
  id?: string;
  word: string;
  severity: number;
  active?: boolean;
  created_at?: string;
  created_by?: string;
}

// Interface for validation results
export interface ValidationResult {
  isValid: boolean;
  badWords: string[];
  censoredText: string;
}

// Cache for bad words to avoid frequent database calls
let badWordsCache: BadWord[] = [];
let lastCacheUpdate = 0;
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

/**
 * Get all bad words from the database or cache
 * @param forceRefresh Force a refresh of the cache
 * @returns Array of bad words
 */
export async function getBadWords(forceRefresh = false): Promise<BadWord[]> {
  const now = Date.now();

  // Use cache if available and not expired
  if (!forceRefresh && badWordsCache.length > 0 && (now - lastCacheUpdate) < CACHE_TTL) {
    console.log('Using cached bad words, count:', badWordsCache.length);
    return badWordsCache;
  }

  try {
    console.log('Fetching bad words from database...');

    // Try using the admin client first (most reliable)
    console.log('Trying to get bad words using admin client...');
    const adminSupabase = createAdminClient();
    const { data: adminData, error: adminError } = await adminSupabase
      .from('bad_words')
      .select('*')
      .eq('active', true)
      .order('severity', { ascending: false })
      .order('word');

    if (!adminError && adminData) {
      console.log('Successfully fetched bad words via admin client, count:', adminData.length);
      badWordsCache = adminData;
      lastCacheUpdate = now;
      return adminData;
    }

    if (adminError) {
      console.error('Error fetching bad words via admin client:', adminError);
    }

    // If we're in a browser context, try client-side Supabase
    if (typeof window !== 'undefined') {
      const supabase = createClientSide();

      // Try to get bad words using the function (for regular users)
      console.log('Trying to get bad words using RPC function...');
      const { data: functionData, error: functionError } = await supabase
        .rpc('get_bad_words');

      if (!functionError && functionData) {
        console.log('Successfully fetched bad words via RPC, count:', functionData.length);
        badWordsCache = functionData;
        lastCacheUpdate = now;
        return functionData;
      }

      if (functionError) {
        console.error('Error fetching bad words via RPC:', functionError);
      }

      // If that fails, try direct access
      console.log('Trying to get bad words via direct table access...');
      const { data, error } = await supabase
        .from('bad_words')
        .select('*')
        .eq('active', true)
        .order('severity', { ascending: false })
        .order('word');

      if (!error && data) {
        console.log('Successfully fetched bad words via direct access, count:', data.length);
        badWordsCache = data;
        lastCacheUpdate = now;
        return data;
      }
    } else {
      // If we're in a server context, try server-side Supabase
      try {
        const serverSupabase = await createServerSide();

        console.log('Trying to get bad words via server-side client...');
        const { data: serverData, error: serverError } = await serverSupabase
          .from('bad_words')
          .select('*')
          .eq('active', true)
          .order('severity', { ascending: false })
          .order('word');

        if (!serverError && serverData) {
          console.log('Successfully fetched bad words via server-side client, count:', serverData.length);
          badWordsCache = serverData;
          lastCacheUpdate = now;
          return serverData;
        }

        if (serverError) {
          console.error('Error fetching bad words via server-side client:', serverError);
        }
      } catch (serverError) {
        console.error('Error creating server-side client:', serverError);
      }
    }

    // If all methods failed, return an empty array
    console.log('All methods to fetch bad words failed, returning empty array');
    return [];
  } catch (error) {
    console.error('Error in getBadWords:', error);
    return [];
  }
}

/**
 * Check if text contains any bad words
 * @param text The text to check
 * @returns True if the text contains bad words, false otherwise
 */
export async function containsBadWords(text: string): Promise<boolean> {
  if (!text) return false;

  const badWords = await getBadWords();
  const lowerText = text.toLowerCase();

  // Check if any bad word is found in the text
  return badWords.some(({ word }) => {
    // Create a regex that matches the word as a whole word (not as part of another word)
    // This prevents false positives like 'class' matching 'ass'
    const regex = new RegExp(`\\b${word}\\b`, 'i');
    return regex.test(lowerText);
  });
}

/**
 * Find all bad words in the text
 * @param text The text to check
 * @returns Array of bad words found in the text
 */
export async function findBadWords(text: string): Promise<string[]> {
  if (!text) return [];

  const badWords = await getBadWords();
  const lowerText = text.toLowerCase();

  // Find all bad words in the text
  return badWords
    .filter(({ word }) => {
      const regex = new RegExp(`\\b${word}\\b`, 'i');
      return regex.test(lowerText);
    })
    .map(({ word }) => word);
}

/**
 * Censor bad words in text by replacing them with asterisks
 * @param text The text to censor
 * @returns The censored text
 */
export async function censorText(text: string): Promise<string> {
  if (!text) return text;

  const badWords = await getBadWords();
  let censoredText = text;

  // Replace each bad word with asterisks
  for (const { word } of badWords) {
    // Create a regex that matches the word as a whole word
    const regex = new RegExp(`\\b${word}\\b`, 'gi');

    // Replace the word with asterisks of the same length
    censoredText = censoredText.replace(regex, '*'.repeat(word.length));
  }

  return censoredText;
}

/**
 * Validate text for bad words and return validation results
 * @param text The text to validate
 * @returns Validation results including whether the text is valid,
 *          which bad words were found, and a censored version of the text
 */
export async function validateText(text: string): Promise<ValidationResult> {
  if (!text) {
    return {
      isValid: true,
      badWords: [],
      censoredText: text
    };
  }

  const badWords = await findBadWords(text);
  const isValid = badWords.length === 0;
  const censoredText = await censorText(text);

  return {
    isValid,
    badWords,
    censoredText
  };
}

// Admin functions for managing bad words

/**
 * Add a new bad word to the database
 * @param word The word to add
 * @param severity The severity level (1-3)
 * @returns The ID of the new bad word
 */
export async function addBadWord(word: string, severity: number = 1): Promise<string | null> {
  try {
    const supabase = createAdminClient();

    const { data, error } = await supabase
      .rpc('add_bad_word', {
        word_text: word.toLowerCase(),
        word_severity: severity
      });

    if (error) {
      console.error('Error adding bad word:', error);
      return null;
    }

    // Force refresh the cache
    await getBadWords(true);

    return data;
  } catch (error) {
    console.error('Error in addBadWord:', error);
    return null;
  }
}

/**
 * Update an existing bad word
 * @param id The ID of the bad word to update
 * @param updates The updates to apply
 * @returns True if the update was successful
 */
export async function updateBadWord(
  id: string,
  updates: { word?: string; severity?: number; active?: boolean }
): Promise<boolean> {
  try {
    const supabase = createAdminClient();

    const { data, error } = await supabase
      .rpc('update_bad_word', {
        word_id: id,
        word_text: updates.word?.toLowerCase(),
        word_severity: updates.severity,
        word_active: updates.active
      });

    if (error) {
      console.error('Error updating bad word:', error);
      return false;
    }

    // Force refresh the cache
    await getBadWords(true);

    return data;
  } catch (error) {
    console.error('Error in updateBadWord:', error);
    return false;
  }
}

/**
 * Delete a bad word from the database
 * @param id The ID of the bad word to delete
 * @returns True if the deletion was successful
 */
export async function deleteBadWord(id: string): Promise<boolean> {
  try {
    const supabase = createAdminClient();

    const { data, error } = await supabase
      .rpc('delete_bad_word', {
        word_id: id
      });

    if (error) {
      console.error('Error deleting bad word:', error);
      return false;
    }

    // Force refresh the cache
    await getBadWords(true);

    return data;
  } catch (error) {
    console.error('Error in deleteBadWord:', error);
    return false;
  }
}

/**
 * Get all bad words for admin purposes (including inactive ones)
 * @returns Array of all bad words
 */
export async function getAllBadWords(): Promise<BadWord[]> {
  try {
    const supabase = createAdminClient();

    const { data, error } = await supabase
      .from('bad_words')
      .select('*')
      .order('severity', { ascending: false })
      .order('word');

    if (error) {
      console.error('Error fetching all bad words:', error);
      return [];
    }

    return data;
  } catch (error) {
    console.error('Error in getAllBadWords:', error);
    return [];
  }
}
