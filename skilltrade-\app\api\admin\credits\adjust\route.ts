import { NextRequest, NextResponse } from 'next/server';
import { createAdminClient } from '@/lib/supabase-admin';
import { isAdmin } from '@/lib/admin-utils';

// POST /api/admin/credits/adjust - Adjust a user's credit balance
export async function POST(request: NextRequest) {
  try {
    // Check if the user is an admin
    const isUserAdmin = await isAdmin();
    if (!isUserAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    const supabase = createAdminClient();
    const body = await request.json();

    const { userId, hours, reason } = body;

    // Validate required fields
    if (!userId || hours === undefined || hours === null) {
      return NextResponse.json(
        { error: 'User ID and hours are required' },
        { status: 400 }
      );
    }

    try {
      // Use the adjust_credits function to handle the credit adjustment
      const { error: adjustmentError } = await supabase.rpc('adjust_credits', {
        user_id: userId,
        hours_delta: hours,
        reason: reason || 'Admin adjustment'
      });

      if (adjustmentError) {
        throw adjustmentError;
      }

      // Get the updated user profile
      const { data: profile, error: fetchError } = await supabase
        .from('profiles')
        .select('id, display_name, email, credit_balance')
        .eq('id', userId)
        .single();

      if (fetchError) {
        throw fetchError;
      }

      return NextResponse.json({
        profile,
        adjustment: {
          userId,
          hours,
          reason: reason || 'Admin adjustment',
        },
        message: `Credit balance adjusted successfully. New balance: ${profile.credit_balance}`,
      });
    } catch (error) {
      throw error;
    }
  } catch (error: any) {
    console.error('Error adjusting credit balance:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to adjust credit balance' },
      { status: 500 }
    );
  }
}
