import { createClient } from '@supabase/supabase-js';

// Create a Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabase = createClient(supabaseUrl, supabaseAnonKey);

/**
 * Upload an avatar image to Supabase Storage
 * @param userId The user ID (UUID)
 * @param file The file to upload
 * @returns Object containing the path and URL of the uploaded avatar
 */
export async function uploadAvatar(userId: string, file: File) {
  try {
    console.log('Uploading avatar for user:', userId);

    // Validate file type
    if (!file.type.startsWith('image/')) {
      throw new Error('File must be an image');
    }

    // Limit file size (2MB)
    const MAX_SIZE = 2 * 1024 * 1024; // 2MB
    if (file.size > MAX_SIZE) {
      throw new Error('File size must be less than 2MB');
    }

    // Create a unique file name
    const fileExt = file.name.split('.').pop();
    const fileName = `${userId}-${Date.now()}.${fileExt}`;
    const filePath = `${userId}/${fileName}`;

    console.log('Uploading to path:', filePath);

    // Upload the file
    const { data, error } = await supabase.storage
      .from('avatars')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: true
      });

    if (error) {
      console.error('Storage upload error:', error);
      throw error;
    }

    // Get the public URL
    const { data: { publicUrl } } = supabase.storage
      .from('avatars')
      .getPublicUrl(filePath);

    console.log('Public URL:', publicUrl);

    // Update the user's profile with the new avatar URL
    const { error: updateError } = await supabase
      .from('profiles')
      .update({ avatar_url: publicUrl })
      .eq('id', userId);

    if (updateError) {
      console.error('Profile update error:', updateError);
      throw updateError;
    }

    return {
      path: filePath,
      url: publicUrl
    };
  } catch (error) {
    console.error('Error uploading avatar:', error);
    throw error;
  }
}

/**
 * Delete an avatar image from Supabase Storage
 * @param userId The user ID (UUID)
 * @param filePath The path of the file to delete
 */
export async function deleteAvatar(userId: string, filePath: string) {
  try {
    console.log('Deleting avatar:', filePath);

    // Ensure the path is valid
    if (!filePath || filePath.trim() === '') {
      throw new Error('Invalid file path');
    }

    // Delete the file
    const { error } = await supabase.storage
      .from('avatars')
      .remove([filePath]);

    if (error) {
      console.error('Storage removal error:', error);
      throw error;
    }

    // Update the user's profile to remove the avatar URL
    const { error: updateError } = await supabase
      .from('profiles')
      .update({ avatar_url: null })
      .eq('id', userId);

    if (updateError) {
      console.error('Profile update error:', updateError);
      throw updateError;
    }

    return { success: true };
  } catch (error) {
    console.error('Error deleting avatar:', error);
    throw error;
  }
}

/**
 * Get the avatar URL for a user
 * @param userId The user ID (UUID)
 * @returns The avatar URL or null if not found
 */
export async function getAvatarUrl(userId: string) {
  try {
    // Get the user's profile
    const { data, error } = await supabase
      .from('profiles')
      .select('avatar_url')
      .eq('id', userId)
      .single();

    if (error) {
      throw error;
    }

    return data?.avatar_url || null;
  } catch (error) {
    console.error('Error getting avatar URL:', error);
    return null;
  }
}
