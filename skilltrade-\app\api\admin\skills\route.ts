import { NextRequest, NextResponse } from 'next/server';
import { createAdminClient } from '@/lib/supabase-admin';
import { isAdmin } from '@/lib/admin-utils';
import { validateText } from '@/lib/bad-words-filter';

// GET /api/admin/skills - Get all skills
export async function GET(request: NextRequest) {
  try {
    // Check if the user is an admin
    const isUserAdmin = await isAdmin();
    if (!isUserAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    const supabase = createAdminClient();

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const query = searchParams.get('query') || '';
    const status = searchParams.get('status') || 'all';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = (page - 1) * limit;

    // Build the query
    let skillsQuery = supabase
      .from('skills')
      .select(`
        *,
        owner:profiles!skills_owner_id_fkey(id, display_name, email, avatar_url)
      `, { count: 'exact' });

    // Add search filter if query is provided
    if (query) {
      skillsQuery = skillsQuery.or(
        `title.ilike.%${query}%,description.ilike.%${query}%`
      );
    }

    // Add status filter if provided
    if (status === 'active') {
      skillsQuery = skillsQuery.eq('is_active', true);
    } else if (status === 'inactive') {
      skillsQuery = skillsQuery.eq('is_active', false);
    }

    // Add pagination
    skillsQuery = skillsQuery
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data: skills, count, error } = await skillsQuery;

    if (error) {
      throw error;
    }

    return NextResponse.json({
      skills,
      total: count || 0,
      page,
      limit,
      totalPages: count ? Math.ceil(count / limit) : 0,
    });
  } catch (error: any) {
    console.error('Error fetching skills:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch skills' },
      { status: 500 }
    );
  }
}

// POST /api/admin/skills - Create a new skill
export async function POST(request: NextRequest) {
  try {
    // Check if the user is an admin
    const isUserAdmin = await isAdmin();
    if (!isUserAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    const supabase = createAdminClient();
    const body = await request.json();

    const { title, description, tags, owner_id, is_active } = body;

    // Validate required fields
    if (!title || !owner_id) {
      return NextResponse.json(
        { error: 'Title and owner ID are required' },
        { status: 400 }
      );
    }

    // Validate title and description for bad words
    console.log('Validating skill title for bad words');
    const titleValidation = await validateText(title);
    if (!titleValidation.isValid) {
      return NextResponse.json(
        { error: `Skill title contains inappropriate language (${titleValidation.badWords.join(', ')})` },
        { status: 400 }
      );
    }

    if (description) {
      console.log('Validating skill description for bad words');
      const descriptionValidation = await validateText(description);
      if (!descriptionValidation.isValid) {
        return NextResponse.json(
          { error: `Skill description contains inappropriate language (${descriptionValidation.badWords.join(', ')})` },
          { status: 400 }
        );
      }
    }

    // Validate tags for bad words
    if (tags && Array.isArray(tags)) {
      console.log('Validating skill tags for bad words');
      for (const tag of tags) {
        const tagValidation = await validateText(tag);
        if (!tagValidation.isValid) {
          return NextResponse.json(
            { error: `Tag "${tag}" contains inappropriate language (${tagValidation.badWords.join(', ')})` },
            { status: 400 }
          );
        }
      }
    }

    // Create the skill
    const { data: skill, error } = await supabase
      .from('skills')
      .insert({
        title,
        description: description || null,
        tags: tags || [],
        owner_id,
        is_active: is_active !== undefined ? is_active : true,
        created_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    return NextResponse.json({
      skill,
      message: 'Skill created successfully',
    });
  } catch (error: any) {
    console.error('Error creating skill:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to create skill' },
      { status: 500 }
    );
  }
}
