# Bad Word Filter Implementation for Skilltrade

This document explains the implementation of the bad word filter for skill creation and session chat in the Skilltrade platform.

## Overview

The bad word filter prevents users from:
1. Creating skills with inappropriate language in titles, descriptions, or tags
2. Sending chat messages with inappropriate language

## Implementation Details

### 1. Bad Word Filter Utility

The core functionality is implemented in `lib/bad-word-filter.ts`, which provides:

- `containsBadWords(text: string)`: Checks if text contains any bad words
- `findBadWords(text: string)`: Returns all bad words found in text
- `censorText(text: string)`: Replaces bad words with asterisks
- `validateText(text: string)`: Comprehensive validation that returns validation results

### 2. Skill Creation Filter

The filter is implemented in:
- `app/skills/create/page.tsx`: Client-side validation for the main skill creation form
- `app/dashboard/skills/new/page.tsx`: Client-side validation for the dashboard skill creation form
- `app/api/admin/skills/route.ts`: Server-side validation for the admin API endpoint
- `app/api/admin/skills/[id]/route.ts`: Server-side validation for skill updates

When a user tries to create or update a skill with inappropriate language, they receive an error message indicating which words are problematic.

### 3. Session Chat Filter

The chat filter is implemented in:
- `components/SessionChat.tsx`: Client-side integration with the chat component
- `app/api/sessions/messages/route.ts`: Server-side API endpoint for message validation

Unlike the skill creation filter, the chat filter doesn't block messages entirely. Instead, it:
1. Detects inappropriate language
2. Automatically censors the bad words (replacing them with asterisks)
3. Shows a warning to the user that their message was censored
4. Sends the censored message to maintain conversation flow

### 4. Database Migration

A migration file has been created to add Row Level Security (RLS) policies for the session_messages table:
- `supabase/migrations/20240615_session_messages_rls.sql`

This migration needs to be applied to your Supabase instance to ensure proper security for session messages.

## How to Apply the Migration

1. Go to the Supabase dashboard for your project
2. Navigate to the SQL Editor
3. Copy the contents of `supabase/migrations/20240615_session_messages_rls.sql`
4. Paste into the SQL Editor and run the query

Alternatively, you can use the Supabase CLI to apply the migration:

```bash
supabase migration up
```

## Customizing the Bad Word List

The list of bad words is defined in `lib/bad-word-filter.ts`. You can modify this list to add or remove words as needed.

## Security Considerations

The implementation includes both client-side and server-side validation to ensure that:
1. Users get immediate feedback when they enter inappropriate content
2. The system remains secure even if client-side validation is bypassed

The server-side API endpoints use the Supabase service role key to bypass RLS when necessary, while still performing proper authorization checks to ensure users can only access their own sessions.
