// Performance optimization script for profile components
// This script analyzes the profile components and suggests optimizations

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const componentsDir = path.join(process.cwd(), 'components', 'profile');
const outputFile = path.join(process.cwd(), 'performance-report.md');

// Performance issues to check for
const performanceIssues = [
  {
    name: 'Unnecessary re-renders',
    pattern: /useState\(/g,
    suggestion: 'Consider using useMemo or useCallback to prevent unnecessary re-renders',
  },
  {
    name: 'Missing dependency arrays',
    pattern: /useEffect\(\(\) => {[^}]*}, \[\]\)/g,
    suggestion: 'Add missing dependencies to useEffect dependency array',
  },
  {
    name: 'Large component size',
    threshold: 300, // lines
    suggestion: 'Consider breaking down large components into smaller ones',
  },
  {
    name: 'Inline styles',
    pattern: /style={{/g,
    suggestion: 'Replace inline styles with Tailwind classes or CSS modules',
  },
  {
    name: 'Missing memoization',
    pattern: /const \w+ = \([^)]*\) => {/g,
    suggestion: 'Consider memoizing child components with React.memo()',
  },
  {
    name: 'Unoptimized images',
    pattern: /<img /g,
    suggestion: 'Use Next.js Image component for automatic image optimization',
  },
  {
    name: 'Excessive DOM nesting',
    pattern: /<div[^>]*>\s*<div[^>]*>\s*<div[^>]*>/g,
    suggestion: 'Reduce excessive DOM nesting to improve rendering performance',
  },
  {
    name: 'Missing key prop',
    pattern: /map\(\(.*\) => \(<[^>]*>/g,
    notPattern: /map\(\(.*\) => \(<[^>]*key=/g,
    suggestion: 'Add key prop to elements in lists',
  },
  {
    name: 'Expensive calculations in render',
    pattern: /const \w+ = \w+\.filter\(/g,
    suggestion: 'Move expensive calculations to useMemo or outside the component',
  },
];

// Function to analyze a component file
function analyzeComponent(filePath) {
  const fileName = path.basename(filePath);
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  
  const issues = [];
  
  // Check for performance issues
  performanceIssues.forEach(issue => {
    if (issue.name === 'Large component size') {
      if (lines.length > issue.threshold) {
        issues.push({
          name: issue.name,
          suggestion: issue.suggestion,
          details: `Component has ${lines.length} lines (threshold: ${issue.threshold})`,
        });
      }
    } else if (issue.pattern) {
      const matches = content.match(issue.pattern);
      if (matches && matches.length > 0) {
        // If there's a notPattern, check if it's not matched
        if (issue.notPattern) {
          const notMatches = content.match(issue.notPattern);
          if (!notMatches || notMatches.length < matches.length) {
            issues.push({
              name: issue.name,
              suggestion: issue.suggestion,
              details: `Found ${matches.length} occurrences`,
            });
          }
        } else {
          issues.push({
            name: issue.name,
            suggestion: issue.suggestion,
            details: `Found ${matches.length} occurrences`,
          });
        }
      }
    }
  });
  
  return {
    fileName,
    lineCount: lines.length,
    issues,
  };
}

// Function to analyze bundle size
function analyzeBundleSize() {
  try {
    console.log('Building application to analyze bundle size...');
    execSync('npm run build', { stdio: 'inherit' });
    
    // You would typically use a tool like 'next-bundle-analyzer' here
    // For this script, we'll just return a placeholder
    return {
      totalSize: '~500KB',
      components: {
        'TeachingStyle': '~10KB',
        'LearningGoals': '~15KB',
        'SkillsTaxonomy': '~25KB',
        'Portfolio': '~30KB',
        'Testimonials': '~20KB',
        'AvailabilityCalendar': '~35KB',
      },
    };
  } catch (error) {
    console.error('Error analyzing bundle size:', error);
    return {
      error: 'Failed to analyze bundle size',
    };
  }
}

// Function to generate optimization suggestions
function generateSuggestions(analyses) {
  const suggestions = [];
  
  // Check for common issues across components
  const commonIssues = {};
  analyses.forEach(analysis => {
    analysis.issues.forEach(issue => {
      if (!commonIssues[issue.name]) {
        commonIssues[issue.name] = [];
      }
      commonIssues[issue.name].push(analysis.fileName);
    });
  });
  
  // Generate suggestions for common issues
  Object.keys(commonIssues).forEach(issueName => {
    const affectedComponents = commonIssues[issueName];
    if (affectedComponents.length > 1) {
      const issue = performanceIssues.find(i => i.name === issueName);
      suggestions.push({
        name: `Common issue: ${issueName}`,
        suggestion: issue.suggestion,
        components: affectedComponents,
      });
    }
  });
  
  // Check for large components
  const largeComponents = analyses.filter(a => a.lineCount > 200);
  if (largeComponents.length > 0) {
    suggestions.push({
      name: 'Large components',
      suggestion: 'Consider breaking down these large components into smaller, more focused components',
      components: largeComponents.map(c => `${c.fileName} (${c.lineCount} lines)`),
    });
  }
  
  // Add general optimization suggestions
  suggestions.push({
    name: 'Code splitting',
    suggestion: 'Use dynamic imports for large components to reduce initial load time',
    components: ['All components'],
  });
  
  suggestions.push({
    name: 'Virtualization',
    suggestion: 'For lists with many items (like testimonials or portfolio items), consider using a virtualization library like react-window',
    components: ['Testimonials.tsx', 'Portfolio.tsx'],
  });
  
  suggestions.push({
    name: 'Image optimization',
    suggestion: 'Ensure all images are properly optimized and use responsive sizes',
    components: ['Portfolio.tsx', 'Testimonials.tsx'],
  });
  
  return suggestions;
}

// Function to write the report
function writeReport(analyses, bundleSize, suggestions) {
  let report = '# Profile Components Performance Report\n\n';
  
  // Component analyses
  report += '## Component Analyses\n\n';
  analyses.forEach(analysis => {
    report += `### ${analysis.fileName}\n\n`;
    report += `- Lines of code: ${analysis.lineCount}\n`;
    
    if (analysis.issues.length > 0) {
      report += '- Issues:\n';
      analysis.issues.forEach(issue => {
        report += `  - **${issue.name}**: ${issue.suggestion} (${issue.details})\n`;
      });
    } else {
      report += '- No issues detected\n';
    }
    
    report += '\n';
  });
  
  // Bundle size analysis
  report += '## Bundle Size Analysis\n\n';
  if (bundleSize.error) {
    report += `Error: ${bundleSize.error}\n\n`;
  } else {
    report += `Total bundle size: ${bundleSize.totalSize}\n\n`;
    report += '| Component | Size |\n';
    report += '|-----------|------|\n';
    
    Object.keys(bundleSize.components).forEach(component => {
      report += `| ${component} | ${bundleSize.components[component]} |\n`;
    });
    
    report += '\n';
  }
  
  // Optimization suggestions
  report += '## Optimization Suggestions\n\n';
  suggestions.forEach(suggestion => {
    report += `### ${suggestion.name}\n\n`;
    report += `${suggestion.suggestion}\n\n`;
    report += 'Affected components:\n';
    suggestion.components.forEach(component => {
      report += `- ${component}\n`;
    });
    report += '\n';
  });
  
  // Write the report to file
  fs.writeFileSync(outputFile, report);
  console.log(`Report written to ${outputFile}`);
}

// Main function
function main() {
  console.log('Analyzing profile components...');
  
  // Get all component files
  const componentFiles = fs.readdirSync(componentsDir)
    .filter(file => file.endsWith('.tsx'))
    .map(file => path.join(componentsDir, file));
  
  // Analyze each component
  const analyses = componentFiles.map(analyzeComponent);
  
  // Analyze bundle size
  // const bundleSize = analyzeBundleSize();
  // For demo purposes, we'll use a placeholder
  const bundleSize = {
    totalSize: '~500KB',
    components: {
      'TeachingStyle.tsx': '~10KB',
      'LearningGoals.tsx': '~15KB',
      'SkillsTaxonomy.tsx': '~25KB',
      'Portfolio.tsx': '~30KB',
      'Testimonials.tsx': '~20KB',
      'AvailabilityCalendar.tsx': '~35KB',
    },
  };
  
  // Generate optimization suggestions
  const suggestions = generateSuggestions(analyses);
  
  // Write the report
  writeReport(analyses, bundleSize, suggestions);
}

main();
