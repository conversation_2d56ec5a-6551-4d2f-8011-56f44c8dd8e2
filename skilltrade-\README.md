# Skilltrade

A platform for trading skills using time credits.

## Session Enhancements

The following enhancements have been implemented for the session system:

1. **Dual Completion Mechanism**
   - Both teacher and learner need to mark a session as complete
   - Visual indicators showing who has marked the session as complete
   - Auto-completion after 24 hours if only one party marks it

2. **5-Star Review System**
   - Replaced positive/negative with a 5-star rating system
   - Ratings below 3 stars trigger disputes

3. **Session Chat**
   - Real-time chat between teacher and learner
   - Available for pending, accepted, completed, or disputed sessions

4. **Notification System**
   - Notification bell in the dashboard navigation
   - Real-time updates for new messages, session status changes, etc.

5. **Dispute Resolution**
   - Self-service dispute resolution through chat
   - Credit adjustment system for financial resolutions

## Database Migrations

The following SQL migrations need to be applied to your Supabase instance:

1. `supabase/migrations/20240602_dispute_resolution.sql` - Creates the dispute_resolutions table and credit adjustment function
2. `supabase/migrations/20240603_fix_notifications.sql` - Fixes the notifications table structure if it already exists
3. `supabase/migrations/20240603_notification_functions.sql` - Creates functions to handle notifications with RLS
4. `supabase/migrations/20240603_notification_mark_read.sql` - Creates functions to mark notifications as read
5. `supabase/migrations/20240603_session_messages.sql` - Creates the session_messages table for chat functionality
6. `supabase/migrations/20240603_notifications.sql` - Creates the notifications table for the notification system (if it doesn't exist)
7. `supabase/migrations/20240603_fix_session_complete.sql` - Updates the mark_session_complete function to use the create_notification function
8. `supabase/migrations/20240610_fix_review_trigger.sql` - Fixes database functions to handle both 'content' and 'message' column names in notifications table
9. `supabase/migrations/20240611_fix_session_completion.sql` - Adds a trigger to mark available dates as booked when a session is completed
10. `supabase/migrations/20240612_admin_reset_database.sql` - Adds admin functions to safely reset the database while preserving user accounts

**Important**: Apply these migrations in the order listed above.

You can apply these migrations through the Supabase dashboard SQL editor or using the Supabase CLI.

**Important Notes**:
1. The code has been updated to handle different database structures and row-level security (RLS) policies. It should work even if your notifications table has different column names (message/content, with or without link column).
2. Real-time notifications are implemented using Supabase's realtime subscriptions.
3. The chat functionality is available for all session statuses (pending, accepted, completed, disputed) to allow communication between teacher and learner at all stages.
4. Server-side API endpoints are provided for marking notifications as read to bypass RLS policies.
5. Star ratings are consistently displayed across the application using SVG stars (1-5 rating system).
6. Notifications are automatically marked as read when clicked, and the "Mark all as read" functionality works properly.
7. Reviews display the correct star rating on all pages (profile, skills, explore).
8. The rating system has been updated to properly handle both numeric ratings (1-5) and legacy positive/negative ratings, converting them to a consistent 5-star display.

## Required Packages

Make sure to install the following packages:

```bash
npm install date-fns lucide-react
```

## Getting Started

First, run the development server:

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result. The production site is available at [https://skilltrade.xyz](https://skilltrade.xyz).

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
