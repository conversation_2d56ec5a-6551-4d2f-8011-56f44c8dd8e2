'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { PlusCircle, User, CreditCard } from 'lucide-react';
import AdminPageHeader from '@/components/admin/AdminPageHeader';
import AdminDataTable from '@/components/admin/AdminDataTable';

interface Profile {
  id: string;
  display_name: string | null;
  email: string | null;
  avatar_url: string | null;
}

interface Transaction {
  id: string;
  user_id: string;
  hours_delta: number;
  reason: string;
  created_at: string;
  user: Profile;
}

export default function AdminCreditsPage() {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [total, setTotal] = useState(0);
  const [systemTotalCredits, setSystemTotalCredits] = useState(0);
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null);
  
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    // Get user filter from URL if present
    const userId = searchParams.get('user_id');
    if (userId) {
      setSelectedUserId(userId);
    }
  }, [searchParams]);

  const fetchTransactions = async () => {
    try {
      setLoading(true);
      
      const queryParams = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
      });
      
      if (selectedUserId) {
        queryParams.set('user_id', selectedUserId);
      }
      
      const response = await fetch(`/api/admin/credits?${queryParams.toString()}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch credit transactions');
      }
      
      const data = await response.json();
      setTransactions(data.transactions);
      setTotal(data.total);
      setSystemTotalCredits(data.systemTotalCredits);
    } catch (error: any) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTransactions();
  }, [page, limit, selectedUserId]);

  const updateUrlParams = (userId: string | null) => {
    const params = new URLSearchParams(searchParams.toString());
    
    if (userId) {
      params.set('user_id', userId);
    } else {
      params.delete('user_id');
    }
    
    router.push(`/admin/credits${params.toString() ? `?${params.toString()}` : ''}`);
  };

  const clearUserFilter = () => {
    setSelectedUserId(null);
    updateUrlParams(null);
  };

  const columns = [
    {
      key: 'user',
      label: 'User',
      render: (user: Profile) => (
        <div className="flex items-center">
          <div className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 flex-shrink-0 overflow-hidden mr-3">
            {user?.avatar_url ? (
              <img
                src={user.avatar_url}
                alt={user?.display_name || 'User'}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {user?.display_name?.charAt(0) || user?.email?.charAt(0) || '?'}
                </span>
              </div>
            )}
          </div>
          <div>
            <div className="text-sm font-medium text-gray-900 dark:text-white">
              {user?.display_name || 'Unnamed User'}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              {user?.email}
            </div>
          </div>
        </div>
      ),
    },
    {
      key: 'hours_delta',
      label: 'Amount',
      sortable: true,
      render: (amount: number) => (
        <div className={`font-medium ${amount > 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
          {amount > 0 ? `+${amount}` : amount}
        </div>
      ),
    },
    {
      key: 'reason',
      label: 'Reason',
      render: (reason: string) => (
        <div className="text-gray-700 dark:text-gray-300">
          {reason}
        </div>
      ),
    },
    {
      key: 'created_at',
      label: 'Date',
      sortable: true,
      render: (date: string) => (
        <div className="text-gray-500 dark:text-gray-400">
          {new Date(date).toLocaleDateString()} {new Date(date).toLocaleTimeString()}
        </div>
      ),
    },
  ];

  const renderActions = (transaction: Transaction) => (
    <div className="flex items-center space-x-3">
      <Link
        href={`/admin/users/${transaction.user_id}`}
        className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
        title="View user profile"
      >
        <User className="h-5 w-5" />
      </Link>
    </div>
  );

  return (
    <div>
      <AdminPageHeader
        title="Credit Management"
        description="View and manage time-credit transactions"
        actions={
          <Link
            href="/admin/credits/adjust"
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <PlusCircle className="h-5 w-5 mr-2" />
            Adjust Credits
          </Link>
        }
      />
      
      {error && (
        <div className="bg-red-100 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 px-4 py-3 rounded-lg mb-6">
          {error}
        </div>
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 mr-4">
              <CreditCard className="h-6 w-6" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Credits in System</p>
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white">{systemTotalCredits}</h3>
            </div>
          </div>
        </div>
        
        {selectedUserId && (
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6">
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 mr-4">
                  <User className="h-6 w-6" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Filtered by User</p>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                    {transactions[0]?.user?.display_name || transactions[0]?.user?.email || 'Selected User'}
                  </h3>
                </div>
              </div>
              <button
                onClick={clearUserFilter}
                className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
              >
                Clear Filter
              </button>
            </div>
          </div>
        )}
      </div>
      
      <AdminDataTable
        columns={columns}
        data={transactions}
        searchable={true}
        searchKeys={['reason', 'user.display_name', 'user.email']}
        actions={renderActions}
        loading={loading}
        emptyState={
          <div className="text-center">
            <p className="text-gray-500 dark:text-gray-400 mb-4">No credit transactions found</p>
            <Link
              href="/admin/credits/adjust"
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <PlusCircle className="h-5 w-5 mr-2" />
              Adjust Credits
            </Link>
          </div>
        }
      />
      
      {/* Pagination */}
      {total > 0 && (
        <div className="mt-6 flex items-center justify-between">
          <div className="text-sm text-gray-700 dark:text-gray-300">
            Showing <span className="font-medium">{(page - 1) * limit + 1}</span> to{' '}
            <span className="font-medium">{Math.min(page * limit, total)}</span> of{' '}
            <span className="font-medium">{total}</span> transactions
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => setPage(page - 1)}
              disabled={page === 1}
              className={`px-3 py-1 rounded-md ${
                page === 1
                  ? 'bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-600 cursor-not-allowed'
                  : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600'
              }`}
            >
              Previous
            </button>
            <button
              onClick={() => setPage(page + 1)}
              disabled={page * limit >= total}
              className={`px-3 py-1 rounded-md ${
                page * limit >= total
                  ? 'bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-600 cursor-not-allowed'
                  : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600'
              }`}
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
