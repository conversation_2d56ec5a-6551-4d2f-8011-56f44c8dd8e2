import { NextRequest, NextResponse } from 'next/server';
import { createAdminClient } from '@/lib/supabase-admin';
import { isAdmin } from '@/lib/admin-utils';
import { validateText } from '@/lib/bad-words-filter';

// GET /api/admin/skills/[id] - Get a specific skill
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if the user is an admin
    const isUserAdmin = await isAdmin();
    if (!isUserAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    const skillId = params.id;
    const supabase = createAdminClient();

    // Get skill details
    const { data: skill, error: skillError } = await supabase
      .from('skills')
      .select(`
        *,
        owner:profiles!skills_owner_id_fkey(id, display_name, email, avatar_url)
      `)
      .eq('id', skillId)
      .single();

    if (skillError) {
      throw skillError;
    }

    // Get sessions for this skill
    const { data: sessions, error: sessionsError } = await supabase
      .from('sessions')
      .select(`
        *,
        teacher:profiles!sessions_teacher_id_fkey(display_name, email),
        learner:profiles!sessions_learner_id_fkey(display_name, email)
      `)
      .eq('skill_id', skillId)
      .order('scheduled_at', { ascending: false });

    if (sessionsError) {
      throw sessionsError;
    }

    return NextResponse.json({
      skill,
      sessions: sessions || [],
    });
  } catch (error: any) {
    console.error(`Error fetching skill ${params.id}:`, error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch skill' },
      { status: 500 }
    );
  }
}

// PATCH /api/admin/skills/[id] - Update a skill
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if the user is an admin
    const isUserAdmin = await isAdmin();
    if (!isUserAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    const skillId = params.id;
    const supabase = createAdminClient();
    const body = await request.json();

    const { title, description, tags, is_active, owner_id } = body;

    // Validate title and description for bad words
    if (title) {
      console.log('Validating skill title for bad words');
      const titleValidation = await validateText(title);
      if (!titleValidation.isValid) {
        return NextResponse.json(
          { error: `Skill title contains inappropriate language (${titleValidation.badWords.join(', ')})` },
          { status: 400 }
        );
      }
    }

    if (description) {
      console.log('Validating skill description for bad words');
      const descriptionValidation = await validateText(description);
      if (!descriptionValidation.isValid) {
        return NextResponse.json(
          { error: `Skill description contains inappropriate language (${descriptionValidation.badWords.join(', ')})` },
          { status: 400 }
        );
      }
    }

    // Validate tags for bad words
    if (tags && Array.isArray(tags)) {
      console.log('Validating skill tags for bad words');
      for (const tag of tags) {
        const tagValidation = await validateText(tag);
        if (!tagValidation.isValid) {
          return NextResponse.json(
            { error: `Tag "${tag}" contains inappropriate language (${tagValidation.badWords.join(', ')})` },
            { status: 400 }
          );
        }
      }
    }

    // Update skill
    const { data: skill, error } = await supabase
      .from('skills')
      .update({
        title,
        description,
        tags,
        is_active,
        owner_id,
      })
      .eq('id', skillId)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return NextResponse.json({
      skill,
      message: 'Skill updated successfully',
    });
  } catch (error: any) {
    console.error(`Error updating skill ${params.id}:`, error);
    return NextResponse.json(
      { error: error.message || 'Failed to update skill' },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/skills/[id] - Delete a skill
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if the user is an admin
    const isUserAdmin = await isAdmin();
    if (!isUserAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    const skillId = params.id;
    const supabase = createAdminClient();

    // Delete the skill
    const { error } = await supabase
      .from('skills')
      .delete()
      .eq('id', skillId);

    if (error) {
      throw error;
    }

    return NextResponse.json({
      message: 'Skill deleted successfully',
    });
  } catch (error: any) {
    console.error(`Error deleting skill ${params.id}:`, error);
    return NextResponse.json(
      { error: error.message || 'Failed to delete skill' },
      { status: 500 }
    );
  }
}
