import { NextRequest, NextResponse } from 'next/server';
import { createServerSide } from '@/lib/supabase-server';
import { createAdminClient } from '@/lib/supabase-admin';
import { validateText } from '@/lib/bad-words-filter';

// POST /api/messages - Send a direct message
export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerSide();

    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { conversation_id, content } = body;

    // Validate required fields
    if (!conversation_id || !content) {
      return NextResponse.json(
        { error: 'Conversation ID and content are required' },
        { status: 400 }
      );
    }

    // Use the admin client to bypass RLS
    const adminSupabase = createAdminClient();

    console.log('Creating message in conversation:', conversation_id);
    console.log('User ID:', user.id);
    console.log('Content:', content);

    // Check if the user is a participant in the conversation
    const { data: participant, error: participantError } = await adminSupabase
      .from('conversation_participants')
      .select('*')
      .eq('conversation_id', conversation_id)
      .eq('user_id', user.id)
      .single();

    if (participantError) {
      console.error('Participant check error:', participantError);
      return NextResponse.json(
        { error: 'Conversation not found or you are not a participant' },
        { status: 404 }
      );
    }

    // Check if the conversation is accepted
    const { data: conversation, error: conversationError } = await adminSupabase
      .from('conversations')
      .select('status')
      .eq('id', conversation_id)
      .single();

    if (conversationError) {
      console.error('Conversation check error:', conversationError);
      return NextResponse.json(
        { error: 'Conversation not found' },
        { status: 404 }
      );
    }

    if (conversation.status !== 'accepted') {
      return NextResponse.json(
        { error: 'Cannot send messages in a conversation that has not been accepted' },
        { status: 403 }
      );
    }

    // Validate and censor content for bad words
    console.log('Validating message content for bad words');
    const validation = await validateText(content);
    console.log('Validation result:', validation);

    let messageContent = content;
    let wasCensored = false;

    // If bad words are found, censor them
    if (!validation.isValid) {
      console.log('Bad words found, censoring content');
      messageContent = validation.censoredText;
      wasCensored = true;
    }

    // Make sure messageContent is never null or empty
    if (!messageContent || messageContent.trim() === '') {
      console.log('Message content is empty after censoring, using placeholder');
      messageContent = '[Message was censored due to inappropriate content]';
    }

    console.log('Final message content to insert:', messageContent);

    // Insert the message with censored content if needed
    const { data: message, error: messageError } = await adminSupabase
      .from('direct_messages')
      .insert({
        conversation_id,
        sender_id: user.id,
        content: messageContent,
      })
      .select(`
        *,
        sender:profiles(
          id,
          display_name,
          avatar_url
        )
      `)
      .single();

    if (messageError) {
      console.error('Error creating message:', messageError);
      throw messageError;
    }

    console.log('Message created successfully:', message);

    // Update the last_read_at timestamp for the sender
    const now = new Date().toISOString();

    await adminSupabase
      .from('conversation_participants')
      .update({
        last_read_at: now
      })
      .eq('conversation_id', conversation_id)
      .eq('user_id', user.id);

    // Update the conversation's last_message_preview and last_message_at
    const { error: updateError } = await adminSupabase
      .from('conversations')
      .update({
        last_message_preview: messageContent.substring(0, 100),
        last_message_at: now
      })
      .eq('id', conversation_id);

    if (updateError) {
      console.error('Error updating conversation with last message:', updateError);
      // Don't throw here, as the message was already created
    }

    // Return the message with a flag indicating if it was censored
    return NextResponse.json({
      message,
      censored: wasCensored,
      badWords: wasCensored ? validation.badWords : [],
    });
  } catch (error: any) {
    console.error('Error sending message:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to send message' },
      { status: 500 }
    );
  }
}
