import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import TeachingStyle from '@/components/profile/TeachingStyle';
import LearningGoals from '@/components/profile/LearningGoals';
import SkillsTaxonomy from '@/components/profile/SkillsTaxonomy';
import Portfolio from '@/components/profile/Portfolio';
import Testimonials from '@/components/profile/Testimonials';
import AvailabilityCalendar from '@/components/profile/AvailabilityCalendar';

// Mock next/image
jest.mock('next/image', () => ({
  __esModule: true,
  default: (props: any) => {
    // eslint-disable-next-line @next/next/no-img-element
    return <img {...props} src={props.src} alt={props.alt} />;
  },
}));

// Mock data
const mockTeachingStyle = 'I focus on hands-on learning with practical examples.';
const mockLearningGoals = ['JavaScript', 'React', 'Node.js'];
const mockSkillLevels = [
  {
    id: '1',
    taxonomyId: '101',
    taxonomyName: 'React',
    proficiencyLevel: 5,
    description: 'Expert in React development',
  },
];
const mockSkillCategories = [
  {
    id: '101',
    name: 'React',
    parentId: null,
  },
  {
    id: '102',
    name: 'Node.js',
    parentId: null,
  },
];
const mockPortfolioItems = [
  {
    id: '1',
    title: 'Project 1',
    description: 'A sample project',
    imageUrl: '/sample.jpg',
    linkUrl: 'https://example.com',
  },
];
const mockTestimonials = [
  {
    id: '1',
    rating: 5,
    comment: 'Great teacher!',
    createdAt: '2023-01-01',
    skillTitle: 'React',
    learnerName: 'John Doe',
    learnerAvatar: null,
    sessionId: '123',
    featured: true,
  },
];
const mockAvailabilitySlots = [
  {
    id: '1',
    dayOfWeek: 1,
    startTime: '09:00',
    endTime: '12:00',
    isRecurring: true,
  },
];

// Mock save functions
const mockSaveTeachingStyle = jest.fn();
const mockSaveLearningGoals = jest.fn();
const mockSaveSkillLevels = jest.fn();
const mockSavePortfolioItems = jest.fn();
const mockUploadImage = jest.fn().mockResolvedValue('/uploaded.jpg');
const mockUpdateFeaturedTestimonials = jest.fn();
const mockSaveAvailabilitySlots = jest.fn();

describe('Profile Components', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('TeachingStyle Component', () => {
    it('renders in view mode', () => {
      render(<TeachingStyle teachingStyle={mockTeachingStyle} />);
      expect(screen.getByText(mockTeachingStyle)).toBeInTheDocument();
    });

    it('renders in edit mode', () => {
      render(
        <TeachingStyle
          teachingStyle={mockTeachingStyle}
          isEditable={true}
          onSave={mockSaveTeachingStyle}
        />
      );
      
      // Click edit button
      fireEvent.click(screen.getByText('Edit'));
      
      // Check if textarea is visible with correct value
      const textarea = screen.getByRole('textbox');
      expect(textarea).toBeInTheDocument();
      expect(textarea).toHaveValue(mockTeachingStyle);
      
      // Edit the text
      fireEvent.change(textarea, { target: { value: 'Updated teaching style' } });
      
      // Save the changes
      fireEvent.click(screen.getByText('Save'));
      
      // Check if save function was called with correct value
      expect(mockSaveTeachingStyle).toHaveBeenCalledWith('Updated teaching style');
    });
  });

  describe('LearningGoals Component', () => {
    it('renders in view mode', () => {
      render(<LearningGoals learningGoals={mockLearningGoals} />);
      
      mockLearningGoals.forEach(goal => {
        expect(screen.getByText(goal)).toBeInTheDocument();
      });
    });

    it('renders in edit mode and allows adding/removing goals', async () => {
      render(
        <LearningGoals
          learningGoals={mockLearningGoals}
          isEditable={true}
          onSave={mockSaveLearningGoals}
        />
      );
      
      // Click edit button
      fireEvent.click(screen.getByText('Edit'));
      
      // Add a new goal
      const input = screen.getByPlaceholderText(/add a skill/i);
      fireEvent.change(input, { target: { value: 'TypeScript' } });
      fireEvent.click(screen.getByText('Add'));
      
      // Check if the new goal is added
      expect(screen.getByText('TypeScript')).toBeInTheDocument();
      
      // Remove a goal
      const removeButtons = screen.getAllByRole('button', { name: '' });
      fireEvent.click(removeButtons[0]); // Remove the first goal
      
      // Save the changes
      fireEvent.click(screen.getByText('Save'));
      
      // Check if save function was called with correct values
      await waitFor(() => {
        expect(mockSaveLearningGoals).toHaveBeenCalledWith(['React', 'Node.js', 'TypeScript']);
      });
    });
  });

  describe('SkillsTaxonomy Component', () => {
    it('renders in view mode', () => {
      render(
        <SkillsTaxonomy
          skillLevels={mockSkillLevels}
          categories={mockSkillCategories}
        />
      );
      
      expect(screen.getByText('React')).toBeInTheDocument();
      expect(screen.getByText('Level 5')).toBeInTheDocument();
      expect(screen.getByText('Expert in React development')).toBeInTheDocument();
    });

    it('renders in edit mode and allows adding skills', async () => {
      render(
        <SkillsTaxonomy
          skillLevels={mockSkillLevels}
          categories={mockSkillCategories}
          isEditable={true}
          onSave={mockSaveSkillLevels}
        />
      );
      
      // Click edit button
      fireEvent.click(screen.getByText('Edit'));
      
      // Select a skill category
      const select = screen.getByRole('combobox');
      fireEvent.change(select, { target: { value: '102' } });
      
      // Add a description
      const textarea = screen.getByPlaceholderText(/briefly describe/i);
      fireEvent.change(textarea, { target: { value: 'Intermediate Node.js developer' } });
      
      // Add the skill
      fireEvent.click(screen.getByText('Add Skill'));
      
      // Save the changes
      fireEvent.click(screen.getByText('Save'));
      
      // Check if save function was called
      await waitFor(() => {
        expect(mockSaveSkillLevels).toHaveBeenCalled();
      });
    });
  });

  describe('Portfolio Component', () => {
    it('renders in view mode', () => {
      render(<Portfolio portfolioItems={mockPortfolioItems} />);
      
      expect(screen.getByText('Project 1')).toBeInTheDocument();
      expect(screen.getByText('A sample project')).toBeInTheDocument();
      expect(screen.getByText('View Project')).toBeInTheDocument();
    });

    it('renders in edit mode and allows adding items', async () => {
      render(
        <Portfolio
          portfolioItems={mockPortfolioItems}
          isEditable={true}
          onSave={mockSavePortfolioItems}
          onUploadImage={mockUploadImage}
        />
      );
      
      // Click edit button
      fireEvent.click(screen.getByText('Edit'));
      
      // Click add portfolio item
      fireEvent.click(screen.getByText('Add Portfolio Item'));
      
      // Fill in the form
      const titleInput = screen.getByPlaceholderText(/project or work sample title/i);
      fireEvent.change(titleInput, { target: { value: 'New Project' } });
      
      const descInput = screen.getByPlaceholderText(/briefly describe this work sample/i);
      fireEvent.change(descInput, { target: { value: 'A new project description' } });
      
      // Add the item
      fireEvent.click(screen.getByText('Add Item'));
      
      // Save the changes
      fireEvent.click(screen.getByText('Save'));
      
      // Check if save function was called
      await waitFor(() => {
        expect(mockSavePortfolioItems).toHaveBeenCalled();
      });
    });
  });

  describe('Testimonials Component', () => {
    it('renders testimonials correctly', () => {
      render(
        <Testimonials
          testimonials={mockTestimonials}
          featuredTestimonials={mockTestimonials}
        />
      );
      
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('"Great teacher!"')).toBeInTheDocument();
      expect(screen.getByText('for React')).toBeInTheDocument();
    });

    it('allows filtering testimonials', () => {
      render(
        <Testimonials
          testimonials={[
            ...mockTestimonials,
            {
              id: '2',
              rating: 3,
              comment: 'Good teacher',
              createdAt: '2023-01-02',
              skillTitle: 'JavaScript',
              learnerName: 'Jane Smith',
              learnerAvatar: null,
              sessionId: '456',
              featured: false,
            },
          ]}
          featuredTestimonials={mockTestimonials}
        />
      );
      
      // Filter to show only 5-star reviews
      const select = screen.getByRole('combobox');
      fireEvent.change(select, { target: { value: '5' } });
      
      // Check that only the 5-star review is shown
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument();
    });
  });

  describe('AvailabilityCalendar Component', () => {
    it('renders availability slots correctly', () => {
      render(<AvailabilityCalendar availabilitySlots={mockAvailabilitySlots} />);
      
      // Check if the day is shown
      expect(screen.getByText('Mon')).toBeInTheDocument();
      
      // Check if the time slot is shown (9:00 AM - 12:00 PM)
      const timeSlots = screen.getAllByText(/9:00 AM - 12:00 PM/i);
      expect(timeSlots.length).toBeGreaterThan(0);
    });

    it('allows adding availability slots in edit mode', async () => {
      render(
        <AvailabilityCalendar
          availabilitySlots={mockAvailabilitySlots}
          isEditable={true}
          onSave={mockSaveAvailabilitySlots}
        />
      );
      
      // Click edit button
      fireEvent.click(screen.getByText('Edit'));
      
      // Click add availability slot
      fireEvent.click(screen.getByText('Add Availability Slot'));
      
      // Select Wednesday
      const daySelect = screen.getByRole('combobox');
      fireEvent.change(daySelect, { target: { value: '3' } });
      
      // Set time range
      const timeInputs = screen.getAllByRole('textbox');
      fireEvent.change(timeInputs[0], { target: { value: '14:00' } });
      fireEvent.change(timeInputs[1], { target: { value: '16:00' } });
      
      // Add the slot
      fireEvent.click(screen.getByText('Add Slot'));
      
      // Save the changes
      fireEvent.click(screen.getByText('Save'));
      
      // Check if save function was called
      await waitFor(() => {
        expect(mockSaveAvailabilitySlots).toHaveBeenCalled();
      });
    });
  });
});
