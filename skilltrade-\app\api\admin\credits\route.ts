import { NextRequest, NextResponse } from 'next/server';
import { createAdminClient } from '@/lib/supabase-admin';
import { isAdmin } from '@/lib/admin-utils';

export const dynamic = 'force-dynamic';

// GET /api/admin/credits - Get all credit transactions
export async function GET(request: NextRequest) {
  try {
    // Check if the user is an admin
    const isUserAdmin = await isAdmin();
    if (!isUserAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    const supabase = createAdminClient();

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const userId = searchParams.get('user_id');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = (page - 1) * limit;

    // Build the query
    let transactionsQuery = supabase
      .from('ledger')
      .select(`
        *,
        user:profiles!ledger_user_id_fkey(id, display_name, email, avatar_url)
      `, { count: 'exact' });

    // Add user filter if provided
    if (userId) {
      transactionsQuery = transactionsQuery.eq('user_id', userId);
    }

    // Add pagination
    transactionsQuery = transactionsQuery
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data: transactions, count, error } = await transactionsQuery;

    if (error) {
      throw error;
    }

    // Get total credit balance in the system
    const { data: totalCredits, error: totalCreditsError } = await supabase
      .from('profiles')
      .select('credit_balance')
      .gt('credit_balance', 0);

    if (totalCreditsError) {
      throw totalCreditsError;
    }

    const systemTotalCredits = totalCredits?.reduce((sum, profile) => sum + profile.credit_balance, 0) || 0;

    return NextResponse.json({
      transactions,
      total: count || 0,
      page,
      limit,
      totalPages: count ? Math.ceil(count / limit) : 0,
      systemTotalCredits,
    });
  } catch (error: any) {
    console.error('Error fetching credit transactions:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch credit transactions' },
      { status: 500 }
    );
  }
}
