'use client';

import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { MessageSquareOff } from 'lucide-react';
import Link from 'next/link';

export default function MessagesPage() {
  const { user, isLoading: authLoading } = useAuth();

  // Check authentication
  if (authLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect to login
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
        <div className="flex flex-col items-center justify-center p-8 text-center">
          <div className="w-20 h-20 bg-amber-100 dark:bg-amber-900/30 rounded-full flex items-center justify-center mb-6">
            <MessageSquareOff size={40} className="text-amber-600 dark:text-amber-400" />
          </div>

          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Messaging System Temporarily Disabled
          </h2>

          <p className="text-gray-600 dark:text-gray-300 max-w-md mb-6">
            The global messaging system is currently disabled while we make improvements.
            You can still communicate with other users through session-based messaging.
          </p>

          <div className="flex flex-col sm:flex-row gap-4">
            <Link
              href="/dashboard/sessions"
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
            >
              Go to Sessions
            </Link>

            <Link
              href="/dashboard"
              className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition"
            >
              Back to Dashboard
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
