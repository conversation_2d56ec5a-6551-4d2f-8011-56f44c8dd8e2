-- 1. Users & Profiles
create table profiles (
  id             uuid       primary key references auth.users on delete cascade,
  email          text       not null unique,
  display_name   text,
  avatar_url     text,
  bio            text,
  hobbies        text[],
  credit_balance numeric    not null default 1,
  created_at     timestamptz not null default now(),
  updated_at     timestamptz
);

-- Set up Row Level Security (RLS)
alter table profiles
  enable row level security;

create policy "Public profiles are viewable by everyone." on profiles
  for select using (true);

create policy "Users can insert their own profile." on profiles
  for insert with check ((select auth.uid()) = id);

create policy "Users can update own profile." on profiles
  for update using ((select auth.uid()) = id);

-- 2. Skill listings
create table skills (
  id          uuid       primary key default gen_random_uuid(),
  owner_id    uuid       not null references profiles(id) on delete cascade,
  title       text       not null,
  description text,
  tags        text[]     not null default '{}',
  is_active   boolean    not null default true,
  created_at  timestamptz not null default now()
);

-- Set up Row Level Security (RLS)
alter table skills
  enable row level security;

create policy "Skills are viewable by everyone." on skills
  for select using (true);

create policy "Users can insert their own skills." on skills
  for insert with check ((select auth.uid()) = owner_id);

create policy "Users can update own skills." on skills
  for update using ((select auth.uid()) = owner_id);

create policy "Users can delete own skills." on skills
  for delete using ((select auth.uid()) = owner_id);

-- 3. Booking sessions
create table sessions (
  id             uuid       primary key default gen_random_uuid(),
  skill_id       uuid       not null references skills(id) on delete cascade,
  teacher_id     uuid       not null references profiles(id),
  learner_id     uuid       not null references profiles(id),
  scheduled_at   timestamptz not null,
  duration_hours integer    not null default 1,
  status         text       not null default 'pending',  -- pending, accepted, completed, reviewed, disputed
  created_at     timestamptz not null default now()
);

-- Set up Row Level Security (RLS)
alter table sessions
  enable row level security;

create policy "Sessions are viewable by participants." on sessions
  for select using ((select auth.uid()) = teacher_id or (select auth.uid()) = learner_id);

create policy "Learners can insert sessions." on sessions
  for insert with check ((select auth.uid()) = learner_id);

create policy "Participants can update sessions." on sessions
  for update using ((select auth.uid()) = teacher_id or (select auth.uid()) = learner_id);

-- 4. Reviews
create table reviews (
  id           uuid       primary key default gen_random_uuid(),
  session_id   uuid       not null references sessions(id) on delete cascade,
  reviewer_id  uuid       not null references profiles(id),
  rating       text       not null check (rating in ('positive','negative')),
  comment      text,
  created_at   timestamptz not null default now()
);

-- Set up Row Level Security (RLS)
alter table reviews
  enable row level security;

create policy "Reviews are viewable by everyone." on reviews
  for select using (true);

create policy "Learners can insert reviews." on reviews
  for insert with check (
    (select auth.uid()) = reviewer_id and 
    (select learner_id from sessions where id = session_id) = reviewer_id
  );

-- 5. Ledger for time-credits
create table ledger (
  id           uuid       primary key default gen_random_uuid(),
  user_id      uuid       not null references profiles(id),
  session_id   uuid       not null references sessions(id),
  hours_delta  numeric    not null,      -- +1 for teaching, –1 for learning
  reason       text       not null,      -- teach | learn
  created_at   timestamptz not null default now()
);

-- Set up Row Level Security (RLS)
alter table ledger
  enable row level security;

create policy "Ledger entries are viewable by the user." on ledger
  for select using ((select auth.uid()) = user_id);

-- Function to apply credits after a positive review
create or replace function apply_credits_on_positive() returns trigger as $$
begin
  if new.rating = 'positive' then
    update sessions set status = 'reviewed' where id = new.session_id;

    -- credit teacher
    insert into ledger(user_id, session_id, hours_delta, reason)
    select teacher_id, new.session_id, s.duration_hours, 'teach'
    from sessions s where s.id = new.session_id;

    -- debit learner
    insert into ledger(user_id, session_id, hours_delta, reason)
    select learner_id, new.session_id, -s.duration_hours, 'learn'
    from sessions s where s.id = new.session_id;

    -- update balances
    update profiles p
    set credit_balance = p.credit_balance + l.hours_delta
    from ledger l 
    where l.user_id = p.id and l.session_id = new.session_id;
  else
    -- If negative review, mark as disputed
    update sessions set status = 'disputed' where id = new.session_id;
  end if;
  return new;
end;
$$ language plpgsql security definer;

-- Attach trigger
create trigger trg_review_positive
after insert on reviews
for each row execute procedure apply_credits_on_positive();

-- Function to handle new user registration
create or replace function handle_new_user() 
returns trigger as $$
begin
  insert into public.profiles (id, email, credit_balance)
  values (new.id, new.email, 1);
  return new;
end;
$$ language plpgsql security definer;

-- Trigger for new user registration
create trigger on_auth_user_created
  after insert on auth.users
  for each row execute procedure public.handle_new_user();

-- Function to resolve disputes after 24 hours
create or replace function resolve_disputes() returns void as $$
begin
  update sessions
  set status = 'reviewed'
  where status = 'disputed'
  and created_at < now() - interval '24 hours';
end;
$$ language plpgsql security definer;
