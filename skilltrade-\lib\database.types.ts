export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          display_name: string | null
          avatar_url: string | null
          bio: string | null
          hobbies: string[] | null
          credit_balance: number
          is_admin: boolean
          teaching_style: string | null
          learning_goals: string[] | null
          created_at: string
          updated_at: string | null
        }
        Insert: {
          id: string
          email: string
          display_name?: string | null
          avatar_url?: string | null
          bio?: string | null
          hobbies?: string[] | null
          credit_balance?: number
          is_admin?: boolean
          teaching_style?: string | null
          learning_goals?: string[] | null
          created_at?: string
          updated_at?: string | null
        }
        Update: {
          id?: string
          email?: string
          display_name?: string | null
          avatar_url?: string | null
          bio?: string | null
          hobbies?: string[] | null
          credit_balance?: number
          is_admin?: boolean
          teaching_style?: string | null
          learning_goals?: string[] | null
          created_at?: string
          updated_at?: string | null
        }
      }
      skills: {
        Row: {
          id: string
          owner_id: string
          title: string
          description: string | null
          tags: string[]
          is_active: boolean
          created_at: string
        }
        Insert: {
          id?: string
          owner_id: string
          title: string
          description?: string | null
          tags?: string[]
          is_active?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          owner_id?: string
          title?: string
          description?: string | null
          tags?: string[]
          is_active?: boolean
          created_at?: string
        }
      }
      sessions: {
        Row: {
          id: string
          skill_id: string
          teacher_id: string
          learner_id: string
          scheduled_at: string
          duration_hours: number
          status: string
          created_at: string
          teacher_marked_complete: boolean
          learner_marked_complete: boolean
          teacher_marked_at: string | null
          learner_marked_at: string | null
          notes: string | null
        }
        Insert: {
          id?: string
          skill_id: string
          teacher_id: string
          learner_id: string
          scheduled_at: string
          duration_hours?: number
          status?: string
          created_at?: string
          teacher_marked_complete?: boolean
          learner_marked_complete?: boolean
          teacher_marked_at?: string | null
          learner_marked_at?: string | null
          notes?: string | null
        }
        Update: {
          id?: string
          skill_id?: string
          teacher_id?: string
          learner_id?: string
          scheduled_at?: string
          duration_hours?: number
          status?: string
          created_at?: string
          teacher_marked_complete?: boolean
          learner_marked_complete?: boolean
          teacher_marked_at?: string | null
          learner_marked_at?: string | null
          notes?: string | null
        }
      }
      reviews: {
        Row: {
          id: string
          session_id: string
          reviewer_id: string
          rating: number
          comment: string | null
          created_at: string
        }
        Insert: {
          id?: string
          session_id: string
          reviewer_id: string
          rating: number
          comment?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          session_id?: string
          reviewer_id?: string
          rating?: number
          comment?: string | null
          created_at?: string
        }
      }
      ledger: {
        Row: {
          id: string
          user_id: string
          session_id: string
          hours_delta: number
          reason: string
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          session_id: string
          hours_delta: number
          reason: string
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          session_id?: string
          hours_delta?: number
          reason?: string
          created_at?: string
        }
      }
      messages: {
        Row: {
          id: string
          session_id: string
          sender_id: string
          content: string
          is_read: boolean
          created_at: string
        }
        Insert: {
          id?: string
          session_id: string
          sender_id: string
          content: string
          is_read?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          session_id?: string
          sender_id?: string
          content?: string
          is_read?: boolean
          created_at?: string
        }
      }
      notifications: {
        Row: {
          id: string
          user_id: string
          type: string
          title: string
          content: string
          related_id: string | null
          is_read: boolean
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          type: string
          title: string
          content: string
          related_id?: string | null
          is_read?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          type?: string
          title?: string
          content?: string
          related_id?: string | null
          is_read?: boolean
          created_at?: string
        }
      }
      skill_taxonomies: {
        Row: {
          id: string
          name: string
          parent_id: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          parent_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          parent_id?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      user_skill_levels: {
        Row: {
          id: string
          user_id: string
          taxonomy_id: string
          proficiency_level: number
          description: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          taxonomy_id: string
          proficiency_level: number
          description?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          taxonomy_id?: string
          proficiency_level?: number
          description?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      portfolio_items: {
        Row: {
          id: string
          user_id: string
          title: string
          description: string | null
          image_url: string | null
          link_url: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          description?: string | null
          image_url?: string | null
          link_url?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          description?: string | null
          image_url?: string | null
          link_url?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      user_availability: {
        Row: {
          id: string
          user_id: string
          day_of_week: number
          start_time: string
          end_time: string
          is_recurring: boolean
          specific_date: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          day_of_week: number
          start_time: string
          end_time: string
          is_recurring?: boolean
          specific_date?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          day_of_week?: number
          start_time?: string
          end_time?: string
          is_recurring?: boolean
          specific_date?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      conversations: {
        Row: {
          id: string
          last_message_preview: string | null
          last_message_at: string | null
          created_at: string
          updated_at: string
          status: string
        }
        Insert: {
          id?: string
          last_message_preview?: string | null
          last_message_at?: string | null
          created_at?: string
          updated_at?: string
          status?: string
        }
        Update: {
          id?: string
          last_message_preview?: string | null
          last_message_at?: string | null
          created_at?: string
          updated_at?: string
          status?: string
        }
      }
      conversation_participants: {
        Row: {
          conversation_id: string
          user_id: string
          last_read_at: string | null
        }
        Insert: {
          conversation_id: string
          user_id: string
          last_read_at?: string | null
        }
        Update: {
          conversation_id?: string
          user_id?: string
          last_read_at?: string | null
        }
      }
      direct_messages: {
        Row: {
          id: string
          conversation_id: string
          sender_id: string
          content: string
          created_at: string
        }
        Insert: {
          id?: string
          conversation_id: string
          sender_id: string
          content: string
          created_at?: string
        }
        Update: {
          id?: string
          conversation_id?: string
          sender_id?: string
          content?: string
          created_at?: string
        }
      }
    }
  }
}
