import { NextRequest, NextResponse } from 'next/server';
import { createServerSide } from '@/lib/supabase-server';
import { createAdminClient } from '@/lib/supabase-admin';

// POST /api/conversations/[id]/delete - Delete a conversation for the current user
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createServerSide();

    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const conversationId = params.id;
    console.log('Deleting conversation:', conversationId);
    console.log('User ID:', user.id);

    // Use the admin client to bypass RLS
    const adminSupabase = createAdminClient();

    // Check if the user is a participant in the conversation
    const { data: participant, error: participantError } = await adminSupabase
      .from('conversation_participants')
      .select('*')
      .eq('conversation_id', conversationId)
      .eq('user_id', user.id)
      .single();

    if (participantError) {
      console.error('Participant check error in delete endpoint:', participantError);
      return NextResponse.json(
        { error: 'Conversation not found or you are not a participant' },
        { status: 404 }
      );
    }

    // Call the delete_conversation function
    console.log('Calling delete_conversation with:', {
      conversation_id_param: conversationId,
      user_id_param: user.id
    });

    const { data: result, error: functionError } = await adminSupabase
      .rpc('delete_conversation', {
        conversation_id_param: conversationId,
        user_id_param: user.id
      });

    if (functionError) {
      console.error('Error deleting conversation:', functionError);
      throw functionError;
    }

    return NextResponse.json({
      success: true,
      message: 'Conversation deleted'
    });
  } catch (error: any) {
    console.error('Error deleting conversation:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to delete conversation' },
      { status: 500 }
    );
  }
}
