'use client';

import { useState, useEffect } from 'react';
import { createClientSide } from '@/lib/supabase';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { MessageCircle } from 'lucide-react';

// Define interfaces for our data
interface Session {
  id: string;
  skill_id: string;
  teacher_id: string;
  learner_id: string;
  scheduled_at: string;
  duration_hours: number;
  status: string;
  skills?: {
    title: string;
  };
  teacher?: {
    display_name: string | null;
    email: string | null;
  };
  learner?: {
    display_name: string | null;
    email: string | null;
  };
}

export default function SessionsPage() {
  const router = useRouter();
  const supabase = createClientSide();

  // State for sessions
  const [upcomingTeachingSessions, setUpcomingTeachingSessions] = useState<Session[]>([]);
  const [pastTeachingSessions, setPastTeachingSessions] = useState<Session[]>([]);
  const [upcomingLearningSessions, setUpcomingLearningSessions] = useState<Session[]>([]);
  const [pastLearningSessions, setPastLearningSessions] = useState<Session[]>([]);

  // State for loading and error
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // State for message counts
  const [messageCounts, setMessageCounts] = useState<Record<string, number>>({});

  // Fetch sessions and message counts
  useEffect(() => {
    const fetchSessions = async () => {
      try {
        setLoading(true);
        setError(null);

        // Get the current user
        const { data: { user } } = await supabase.auth.getUser();

        if (!user) {
          router.push('/login');
          return;
        }

        // Get upcoming teaching sessions (pending and accepted)
        const { data: upcomingTeaching, error: upcomingTeachingError } = await supabase
          .from('sessions')
          .select(`
            *,
            skills:skill_id (title),
            learner:learner_id (display_name, email)
          `)
          .eq('teacher_id', user.id)
          .in('status', ['pending', 'accepted'])
          .order('scheduled_at', { ascending: true });

        if (upcomingTeachingError) throw upcomingTeachingError;
        setUpcomingTeachingSessions(upcomingTeaching || []);

        // Get past teaching sessions
        const { data: pastTeaching, error: pastTeachingError } = await supabase
          .from('sessions')
          .select(`
            *,
            skills:skill_id (title),
            learner:learner_id (display_name, email)
          `)
          .eq('teacher_id', user.id)
          .in('status', ['completed', 'cancelled', 'reviewed', 'disputed'])
          .order('scheduled_at', { ascending: false });

        if (pastTeachingError) throw pastTeachingError;
        setPastTeachingSessions(pastTeaching || []);

        // Get upcoming learning sessions
        const { data: upcomingLearning, error: upcomingLearningError } = await supabase
          .from('sessions')
          .select(`
            *,
            skills:skill_id (title),
            teacher:teacher_id (display_name, email)
          `)
          .eq('learner_id', user.id)
          .in('status', ['pending', 'accepted'])
          .order('scheduled_at', { ascending: true });

        if (upcomingLearningError) throw upcomingLearningError;
        setUpcomingLearningSessions(upcomingLearning || []);

        // Get past learning sessions
        const { data: pastLearning, error: pastLearningError } = await supabase
          .from('sessions')
          .select(`
            *,
            skills:skill_id (title),
            teacher:teacher_id (display_name, email)
          `)
          .eq('learner_id', user.id)
          .in('status', ['completed', 'cancelled', 'reviewed', 'disputed'])
          .order('scheduled_at', { ascending: false });

        if (pastLearningError) throw pastLearningError;
        setPastLearningSessions(pastLearning || []);

        // Fetch message counts for active sessions (pending and accepted)
        await fetchMessageCounts([
          ...(upcomingTeaching || []),
          ...(upcomingLearning || [])
        ].filter(session => ['pending', 'accepted'].includes(session.status)).map(session => session.id));

      } catch (error: any) {
        console.error('Error fetching sessions:', error);
        setError(error.message || 'Failed to load sessions');
      } finally {
        setLoading(false);
      }
    };

    fetchSessions();
  }, [router, supabase]);

  // Set up real-time subscription for new messages
  useEffect(() => {
    // Get all active session IDs (pending and accepted)
    const activeSessionIds = [
      ...upcomingTeachingSessions,
      ...upcomingLearningSessions
    ]
      .filter(session => ['pending', 'accepted'].includes(session.status))
      .map(session => session.id);

    if (activeSessionIds.length === 0) return;

    // Create a channel for each active session
    const channels = activeSessionIds.map(sessionId => {
      return supabase
        .channel(`session_messages:${sessionId}`)
        .on('postgres_changes', {
          event: 'INSERT',
          schema: 'public',
          table: 'session_messages',
          filter: `session_id=eq.${sessionId}`
        }, (payload) => {
          // Get the current user
          supabase.auth.getUser().then(({ data: { user } }) => {
            if (!user) return;

            // Only update if the message is from the other party
            if (payload.new.sender_id !== user.id) {
              // Update message counts
              setMessageCounts(prev => ({
                ...prev,
                [sessionId]: (prev[sessionId] || 0) + 1
              }));
            }
          });
        })
        .subscribe();
    });

    // Clean up subscriptions
    return () => {
      channels.forEach(channel => {
        supabase.removeChannel(channel);
      });
    };
  }, [upcomingTeachingSessions, upcomingLearningSessions, supabase]);

  // Fetch message counts for sessions
  const fetchMessageCounts = async (sessionIds: string[]) => {
    if (!sessionIds.length) return;

    try {
      const response = await fetch('/api/sessions/message-counts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ sessionIds }),
      });

      if (!response.ok) {
        throw new Error('Failed to fetch message counts');
      }

      const data = await response.json();
      setMessageCounts(data.messageCounts || {});
    } catch (error: any) {
      console.error('Error fetching message counts:', error);
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
    });
  };

  // Get status badge color
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-900/50 text-yellow-300';
      case 'accepted':
        return 'bg-blue-900/50 text-blue-300';
      case 'completed':
        return 'bg-green-900/50 text-green-300';
      case 'reviewed':
        return 'bg-purple-900/50 text-purple-300';
      case 'disputed':
        return 'bg-red-900/50 text-red-300';
      default:
        return 'bg-gray-700 text-gray-300';
    }
  };

  return (
    <main className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-8">My Sessions</h1>

      {/* Upcoming Sessions Section */}
      <div className="mb-12">
        <h2 className="text-xl font-bold mb-6 pb-2 border-b border-gray-700">Upcoming Sessions</h2>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Upcoming Teaching Sessions */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Teaching</h3>

            {upcomingTeachingSessions && upcomingTeachingSessions.length > 0 ? (
              <div className="space-y-4">
                {upcomingTeachingSessions.map((session) => (
                  <div key={session.id} className="bg-gray-800 rounded-lg p-4 shadow-lg">
                    <div className="flex justify-between items-start mb-2">
                      <div className="flex items-center">
                        <h3 className="font-medium">{session.skills?.title}</h3>
                        {['pending', 'accepted'].includes(session.status) && messageCounts[session.id] > 0 && (
                          <div className="ml-2 flex items-center text-blue-400">
                            <MessageCircle size={16} className="mr-1" />
                            <span className="text-xs">{messageCounts[session.id]}</span>
                          </div>
                        )}
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs ${getStatusBadge(session.status)}`}>
                        {session.status.charAt(0).toUpperCase() + session.status.slice(1)}
                      </span>
                    </div>
                    <p className="text-sm text-gray-400 mb-3">
                      <span className="font-medium text-gray-300">Learner:</span>{' '}
                      {session.learner?.display_name || session.learner?.email || 'Unknown'}
                    </p>
                    <p className="text-sm text-gray-400 mb-3">
                      <span className="font-medium text-gray-300">When:</span>{' '}
                      {formatDate(session.scheduled_at)}
                    </p>
                    <p className="text-sm text-gray-400 mb-4">
                      <span className="font-medium text-gray-300">Duration:</span>{' '}
                      {session.duration_hours} hour{session.duration_hours !== 1 ? 's' : ''}
                    </p>
                    <div className="flex justify-end">
                      <Link
                        href={`/dashboard/sessions/${session.id}`}
                        className="text-blue-400 hover:text-blue-300 transition text-sm"
                      >
                        View Details
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="bg-gray-800 rounded-lg p-6 text-center">
                <p className="text-gray-400 mb-4">You don't have any upcoming teaching sessions.</p>
                <Link
                  href="/dashboard/skills/new"
                  className="text-blue-400 hover:text-blue-300 transition"
                >
                  Add a skill to start teaching
                </Link>
              </div>
            )}
          </div>

          {/* Upcoming Learning Sessions */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Learning</h3>

            {upcomingLearningSessions && upcomingLearningSessions.length > 0 ? (
              <div className="space-y-4">
                {upcomingLearningSessions.map((session) => (
                  <div key={session.id} className="bg-gray-800 rounded-lg p-4 shadow-lg">
                    <div className="flex justify-between items-start mb-2">
                      <div className="flex items-center">
                        <h3 className="font-medium">{session.skills?.title}</h3>
                        {['pending', 'accepted'].includes(session.status) && messageCounts[session.id] > 0 && (
                          <div className="ml-2 flex items-center text-blue-400">
                            <MessageCircle size={16} className="mr-1" />
                            <span className="text-xs">{messageCounts[session.id]}</span>
                          </div>
                        )}
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs ${getStatusBadge(session.status)}`}>
                        {session.status.charAt(0).toUpperCase() + session.status.slice(1)}
                      </span>
                    </div>
                    <p className="text-sm text-gray-400 mb-3">
                      <span className="font-medium text-gray-300">Teacher:</span>{' '}
                      {session.teacher?.display_name || session.teacher?.email || 'Unknown'}
                    </p>
                    <p className="text-sm text-gray-400 mb-3">
                      <span className="font-medium text-gray-300">When:</span>{' '}
                      {formatDate(session.scheduled_at)}
                    </p>
                    <p className="text-sm text-gray-400 mb-4">
                      <span className="font-medium text-gray-300">Duration:</span>{' '}
                      {session.duration_hours} hour{session.duration_hours !== 1 ? 's' : ''}
                    </p>
                    <div className="flex justify-end">
                      <Link
                        href={`/dashboard/sessions/${session.id}`}
                        className="text-blue-400 hover:text-blue-300 transition text-sm"
                      >
                        View Details
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="bg-gray-800 rounded-lg p-6 text-center">
                <p className="text-gray-400 mb-4">You don't have any upcoming learning sessions.</p>
                <Link
                  href="/explore"
                  className="text-blue-400 hover:text-blue-300 transition"
                >
                  Browse skills to start learning
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Past Sessions Section */}
      <div>
        <h2 className="text-xl font-bold mb-6 pb-2 border-b border-gray-700">Past Sessions</h2>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Past Teaching Sessions */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Teaching</h3>

            {pastTeachingSessions && pastTeachingSessions.length > 0 ? (
              <div className="space-y-4">
                {pastTeachingSessions.map((session) => (
                  <div key={session.id} className="bg-gray-800 rounded-lg p-4 shadow-lg">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="font-medium">{session.skills?.title}</h3>
                      <span className={`px-2 py-1 rounded-full text-xs ${getStatusBadge(session.status)}`}>
                        {session.status.charAt(0).toUpperCase() + session.status.slice(1)}
                      </span>
                    </div>
                    <p className="text-sm text-gray-400 mb-3">
                      <span className="font-medium text-gray-300">Learner:</span>{' '}
                      {session.learner?.display_name || session.learner?.email || 'Unknown'}
                    </p>
                    <p className="text-sm text-gray-400 mb-3">
                      <span className="font-medium text-gray-300">When:</span>{' '}
                      {formatDate(session.scheduled_at)}
                    </p>
                    <p className="text-sm text-gray-400 mb-4">
                      <span className="font-medium text-gray-300">Duration:</span>{' '}
                      {session.duration_hours} hour{session.duration_hours !== 1 ? 's' : ''}
                    </p>
                    <div className="flex justify-end">
                      <Link
                        href={`/dashboard/sessions/${session.id}`}
                        className="text-blue-400 hover:text-blue-300 transition text-sm"
                      >
                        View Details
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="bg-gray-800 rounded-lg p-6 text-center">
                <p className="text-gray-400">You don't have any past teaching sessions.</p>
              </div>
            )}
          </div>

          {/* Past Learning Sessions */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Learning</h3>

            {pastLearningSessions && pastLearningSessions.length > 0 ? (
              <div className="space-y-4">
                {pastLearningSessions.map((session) => (
                  <div key={session.id} className="bg-gray-800 rounded-lg p-4 shadow-lg">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="font-medium">{session.skills?.title}</h3>
                      <span className={`px-2 py-1 rounded-full text-xs ${getStatusBadge(session.status)}`}>
                        {session.status.charAt(0).toUpperCase() + session.status.slice(1)}
                      </span>
                    </div>
                    <p className="text-sm text-gray-400 mb-3">
                      <span className="font-medium text-gray-300">Teacher:</span>{' '}
                      {session.teacher?.display_name || session.teacher?.email || 'Unknown'}
                    </p>
                    <p className="text-sm text-gray-400 mb-3">
                      <span className="font-medium text-gray-300">When:</span>{' '}
                      {formatDate(session.scheduled_at)}
                    </p>
                    <p className="text-sm text-gray-400 mb-4">
                      <span className="font-medium text-gray-300">Duration:</span>{' '}
                      {session.duration_hours} hour{session.duration_hours !== 1 ? 's' : ''}
                    </p>
                    <div className="flex justify-end space-x-4">
                      {session.status === 'completed' && (
                        <Link
                          href={`/dashboard/sessions/${session.id}/review`}
                          className="text-green-400 hover:text-green-300 transition text-sm"
                        >
                          Leave Review
                        </Link>
                      )}
                      <Link
                        href={`/dashboard/sessions/${session.id}`}
                        className="text-blue-400 hover:text-blue-300 transition text-sm"
                      >
                        View Details
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="bg-gray-800 rounded-lg p-6 text-center">
                <p className="text-gray-400">You don't have any past learning sessions.</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </main>
  );
}
