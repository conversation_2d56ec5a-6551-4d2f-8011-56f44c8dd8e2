# Supabase Setup Guide for Skilltrade

This guide will walk you through setting up the Supabase database for the Skilltrade platform.

## Prerequisites

- A Supabase account
- Access to the Supabase project with the following credentials:
  - URL: https://iidqtbyxltqnhgyrpofd.supabase.co
  - Anon Key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.vOdnIxU4kZqCvCg8KGndTwoqNFUUwQZ56BkIs70Ot5M

## Database Schema Setup

1. Log in to the Supabase dashboard at https://app.supabase.com/
2. Select your project
3. Navigate to the SQL Editor in the left sidebar
4. Create a new query
5. Copy and paste the contents of the `supabase/schema.sql` file into the SQL editor
6. Execute the query to create all the necessary tables, policies, functions, and triggers

## Database Schema Overview

The Skilltrade database consists of the following tables:

### 1. Profiles

Stores user profile information, linked to Supabase Auth users.

```sql
create table profiles (
  id             uuid       primary key references auth.users on delete cascade,
  email          text       not null unique,
  display_name   text,
  avatar_url     text,
  bio            text,
  hobbies        text[],
  credit_balance numeric    not null default 1,
  created_at     timestamptz not null default now(),
  updated_at     timestamptz
);
```

### 2. Skills

Stores skills that users can teach.

```sql
create table skills (
  id          uuid       primary key default gen_random_uuid(),
  owner_id    uuid       not null references profiles(id) on delete cascade,
  title       text       not null,
  description text,
  tags        text[]     not null default '{}',
  is_active   boolean    not null default true,
  created_at  timestamptz not null default now()
);
```

### 3. Sessions

Stores booking sessions between teachers and learners.

```sql
create table sessions (
  id             uuid       primary key default gen_random_uuid(),
  skill_id       uuid       not null references skills(id) on delete cascade,
  teacher_id     uuid       not null references profiles(id),
  learner_id     uuid       not null references profiles(id),
  scheduled_at   timestamptz not null,
  duration_hours integer    not null default 1,
  status         text       not null default 'pending',  -- pending, accepted, completed, reviewed, disputed
  created_at     timestamptz not null default now()
);
```

### 4. Reviews

Stores reviews for completed sessions.

```sql
create table reviews (
  id           uuid       primary key default gen_random_uuid(),
  session_id   uuid       not null references sessions(id) on delete cascade,
  reviewer_id  uuid       not null references profiles(id),
  rating       text       not null check (rating in ('positive','negative')),
  comment      text,
  created_at   timestamptz not null default now()
);
```

### 5. Ledger

Stores time-credit transactions.

```sql
create table ledger (
  id           uuid       primary key default gen_random_uuid(),
  user_id      uuid       not null references profiles(id),
  session_id   uuid       not null references sessions(id),
  hours_delta  numeric    not null,      -- +1 for teaching, –1 for learning
  reason       text       not null,      -- teach | learn
  created_at   timestamptz not null default now()
);
```

## Row Level Security (RLS) Policies

Each table has Row Level Security enabled with appropriate policies to ensure data security:

- Profiles: Public profiles are viewable by everyone, but users can only insert/update their own profile
- Skills: Skills are viewable by everyone, but users can only insert/update/delete their own skills
- Sessions: Sessions are viewable only by participants (teacher and learner), learners can insert sessions, and participants can update sessions
- Reviews: Reviews are viewable by everyone, but only learners can insert reviews for their own sessions
- Ledger: Ledger entries are viewable only by the user they belong to

## Functions and Triggers

The schema includes several functions and triggers:

1. `apply_credits_on_positive()`: Applies credits after a positive review
   - Updates session status to 'reviewed'
   - Credits the teacher with time credits
   - Debits the learner with time credits
   - Updates the credit balances of both users

2. `handle_new_user()`: Creates a profile for new users
   - Automatically creates a profile when a new user signs up
   - Sets initial credit balance to 1

3. `resolve_disputes()`: Resolves disputed sessions after 24 hours
   - Updates disputed sessions to 'reviewed' status after 24 hours

## Storage Setup

1. In the Supabase dashboard, navigate to Storage in the left sidebar
2. Create a new bucket called 'avatars' for storing user profile pictures
3. Set the bucket to public (for easy access to profile pictures)
4. Create appropriate policies to allow users to upload and manage their own avatars

## Authentication Setup

1. In the Supabase dashboard, navigate to Authentication in the left sidebar
2. Configure Email Auth:
   - Enable Email confirmations
   - Set up redirect URLs for your application (e.g., https://skilltrade.xyz/auth/callback)
3. Configure any additional auth providers as needed (Google, GitHub, etc.)

## Environment Variables

Make sure your application has the following environment variables set:

```
NEXT_PUBLIC_SUPABASE_URL=https://iidqtbyxltqnhgyrpofd.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.vOdnIxU4kZqCvCg8KGndTwoqNFUUwQZ56BkIs70Ot5M
```

These are already set up in the `.env.local` file in the project root.

## Testing the Setup

After setting up the database schema, you can test it by:

1. Creating a new user through the Supabase Auth API
2. Verifying that a profile is automatically created for the user
3. Creating a skill for the user
4. Creating a session between two users
5. Completing the session and leaving a review
6. Verifying that the credit balance is updated correctly

## Maintenance

Regularly check the Supabase dashboard for:

- Database health and performance
- Storage usage
- Authentication logs
- Function logs for any errors

## Backup

Supabase provides automatic backups, but you can also:

1. Export your data regularly using the SQL Editor
2. Set up custom backup solutions for critical data
