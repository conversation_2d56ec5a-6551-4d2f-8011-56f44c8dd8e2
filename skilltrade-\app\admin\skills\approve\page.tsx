'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { CheckCircle, XCircle, Eye } from 'lucide-react';
import AdminPageHeader from '@/components/admin/AdminPageHeader';
import AdminDataTable from '@/components/admin/AdminDataTable';

interface Owner {
  id: string;
  display_name: string | null;
  email: string | null;
  avatar_url: string | null;
}

interface Skill {
  id: string;
  title: string;
  description: string | null;
  tags: string[];
  is_active: boolean;
  created_at: string;
  owner: Owner;
}

export default function AdminApproveSkillsPage() {
  const [skills, setSkills] = useState<Skill[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Fetch inactive skills
  const fetchInactiveSkills = async () => {
    try {
      setLoading(true);
      
      const response = await fetch('/api/admin/skills?status=inactive&limit=100');
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch skills');
      }
      
      const data = await response.json();
      setSkills(data.skills);
    } catch (error: any) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };
  
  useEffect(() => {
    fetchInactiveSkills();
  }, []);
  
  // Handle skill approval/rejection
  const handleStatusChange = async (skillId: string, action: 'approve' | 'reject') => {
    try {
      const response = await fetch(`/api/admin/skills/${skillId}/status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: action }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to ${action} skill`);
      }
      
      // Refresh the skill list
      fetchInactiveSkills();
    } catch (error: any) {
      setError(error.message);
    }
  };
  
  const columns = [
    {
      key: 'title',
      label: 'Title',
      sortable: true,
      render: (value: string, skill: Skill) => (
        <div>
          <div className="font-medium text-gray-900 dark:text-white">{value}</div>
          <div className="text-sm text-gray-500 dark:text-gray-400 line-clamp-2">
            {skill.description || 'No description'}
          </div>
        </div>
      ),
    },
    {
      key: 'owner',
      label: 'Owner',
      render: (owner: Owner) => (
        <div className="flex items-center">
          <div className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 flex-shrink-0 overflow-hidden mr-3">
            {owner?.avatar_url ? (
              <img
                src={owner.avatar_url}
                alt={owner?.display_name || 'User'}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {owner?.display_name?.charAt(0) || owner?.email?.charAt(0) || '?'}
                </span>
              </div>
            )}
          </div>
          <div className="text-sm text-gray-900 dark:text-white">
            {owner?.display_name || owner?.email || 'Unknown User'}
          </div>
        </div>
      ),
    },
    {
      key: 'tags',
      label: 'Tags',
      render: (tags: string[]) => (
        <div className="flex flex-wrap gap-1">
          {tags && tags.length > 0 ? (
            tags.map((tag, index) => (
              <span
                key={index}
                className="px-2 py-1 text-xs rounded-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300"
              >
                {tag}
              </span>
            ))
          ) : (
            <span className="text-gray-500 dark:text-gray-400 text-sm">No tags</span>
          )}
        </div>
      ),
    },
    {
      key: 'created_at',
      label: 'Submitted',
      sortable: true,
      render: (value: string) => (
        <div className="text-gray-500 dark:text-gray-400">
          {new Date(value).toLocaleDateString()}
        </div>
      ),
    },
  ];
  
  const renderActions = (skill: Skill) => (
    <div className="flex items-center space-x-3">
      <Link
        href={`/admin/skills/${skill.id}`}
        className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
        title="View details"
      >
        <Eye className="h-5 w-5" />
      </Link>
      
      <button
        onClick={() => handleStatusChange(skill.id, 'approve')}
        className="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300"
        title="Approve skill"
      >
        <CheckCircle className="h-5 w-5" />
      </button>
      
      <button
        onClick={() => handleStatusChange(skill.id, 'reject')}
        className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300"
        title="Reject skill"
      >
        <XCircle className="h-5 w-5" />
      </button>
    </div>
  );
  
  return (
    <div>
      <AdminPageHeader
        title="Approve Skills"
        description="Review and approve pending skills"
        backHref="/admin/skills"
      />
      
      {error && (
        <div className="bg-red-100 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 px-4 py-3 rounded-lg mb-6">
          {error}
        </div>
      )}
      
      <AdminDataTable
        columns={columns}
        data={skills}
        searchable={true}
        searchKeys={['title', 'description', 'tags']}
        actions={renderActions}
        loading={loading}
        emptyState={
          <div className="text-center">
            <p className="text-gray-500 dark:text-gray-400 mb-4">No skills pending approval</p>
            <Link
              href="/admin/skills"
              className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Back to Skills
            </Link>
          </div>
        }
      />
    </div>
  );
}
