'use client';

import { useState, useEffect } from 'react';
import { createClientSide } from '@/lib/supabase';
import {
  Users,
  BookOpen,
  Calendar,
  CreditCard,
  TrendingUp,
  Star,
  Database,
  Shield
} from 'lucide-react';
import Link from 'next/link';

interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  href: string;
  color: string;
}

function StatCard({ title, value, icon, href, color }: StatCardProps) {
  return (
    <Link href={href}>
      <div className={`bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 hover:shadow-lg transition-shadow`}>
        <div className="flex items-center">
          <div className={`p-3 rounded-full ${color} text-white mr-4`}>
            {icon}
          </div>
          <div>
            <p className="text-gray-500 dark:text-gray-400 text-sm font-medium">{title}</p>
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white">{value}</h3>
          </div>
        </div>
      </div>
    </Link>
  );
}

export default function AdminDashboard() {
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalSkills: 0,
    totalSessions: 0,
    totalCredits: 0,
    activeUsers: 0,
    pendingSessions: 0,
  });
  const [loading, setLoading] = useState(true);
  const supabase = createClientSide();

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);

        // Fetch total users
        const { count: userCount } = await supabase
          .from('profiles')
          .select('*', { count: 'exact', head: true });

        // Fetch total skills
        const { count: skillCount } = await supabase
          .from('skills')
          .select('*', { count: 'exact', head: true });

        // Fetch total sessions
        const { count: sessionCount } = await supabase
          .from('sessions')
          .select('*', { count: 'exact', head: true });

        // Fetch total credits in the system
        const { data: creditData } = await supabase
          .from('profiles')
          .select('credit_balance');

        const totalCredits = creditData?.reduce((sum, profile) => sum + profile.credit_balance, 0) || 0;

        // Fetch active users (users with at least one session)
        const { data: activeUserData } = await supabase
          .from('sessions')
          .select('teacher_id, learner_id');

        const activeUserIds = new Set();
        activeUserData?.forEach(session => {
          activeUserIds.add(session.teacher_id);
          activeUserIds.add(session.learner_id);
        });

        // Fetch pending sessions
        const { count: pendingSessionCount } = await supabase
          .from('sessions')
          .select('*', { count: 'exact', head: true })
          .eq('status', 'pending');

        setStats({
          totalUsers: userCount || 0,
          totalSkills: skillCount || 0,
          totalSessions: sessionCount || 0,
          totalCredits,
          activeUsers: activeUserIds.size,
          pendingSessions: pendingSessionCount || 0,
        });
      } catch (error) {
        console.error('Error fetching admin stats:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, [supabase]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p className="ml-4 text-gray-700 dark:text-gray-300">Loading dashboard...</p>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Admin Dashboard</h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          Welcome to the Skilltrade admin dashboard. Manage users, skills, sessions, and more.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <StatCard
          title="Total Users"
          value={stats.totalUsers}
          icon={<Users size={24} />}
          href="/admin/users"
          color="bg-blue-600"
        />
        <StatCard
          title="Total Skills"
          value={stats.totalSkills}
          icon={<BookOpen size={24} />}
          href="/admin/skills"
          color="bg-green-600"
        />
        <StatCard
          title="Total Sessions"
          value={stats.totalSessions}
          icon={<Calendar size={24} />}
          href="/admin/sessions"
          color="bg-purple-600"
        />
        <StatCard
          title="Total Credits"
          value={stats.totalCredits}
          icon={<CreditCard size={24} />}
          href="/admin/credits"
          color="bg-yellow-600"
        />
        <StatCard
          title="Active Users"
          value={stats.activeUsers}
          icon={<TrendingUp size={24} />}
          href="/admin/analytics"
          color="bg-red-600"
        />
        <StatCard
          title="Content Filter"
          value="Manage"
          icon={<Shield size={24} />}
          href="/admin/content-filter"
          color="bg-indigo-600"
        />
        <StatCard
          title="Pending Sessions"
          value={stats.pendingSessions}
          icon={<Star size={24} />}
          href="/admin/sessions?status=pending"
          color="bg-indigo-600"
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6">
          <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Quick Actions</h2>
          <div className="space-y-4">
            <Link href="/admin/users/new" className="block p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition">
              <div className="flex items-center">
                <Users className="h-5 w-5 text-blue-600 dark:text-blue-400 mr-3" />
                <span className="text-gray-900 dark:text-white">Add New User</span>
              </div>
            </Link>
            <Link href="/admin/skills/approve" className="block p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition">
              <div className="flex items-center">
                <BookOpen className="h-5 w-5 text-green-600 dark:text-green-400 mr-3" />
                <span className="text-gray-900 dark:text-white">Approve Skills</span>
              </div>
            </Link>
            <Link href="/admin/content-filter" className="block p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition">
              <div className="flex items-center">
                <Shield className="h-5 w-5 text-indigo-600 dark:text-indigo-400 mr-3" />
                <span className="text-gray-900 dark:text-white">Manage Content Filter</span>
              </div>
            </Link>
            <Link href="/admin/sessions?status=disputed" className="block p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition">
              <div className="flex items-center">
                <Calendar className="h-5 w-5 text-purple-600 dark:text-purple-400 mr-3" />
                <span className="text-gray-900 dark:text-white">Resolve Disputed Sessions</span>
              </div>
            </Link>
            <Link href="/admin/credits/adjust" className="block p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition">
              <div className="flex items-center">
                <CreditCard className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mr-3" />
                <span className="text-gray-900 dark:text-white">Adjust User Credits</span>
              </div>
            </Link>
            <Link href="/admin/database" className="block p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition">
              <div className="flex items-center">
                <Database className="h-5 w-5 text-red-600 dark:text-red-400 mr-3" />
                <span className="text-gray-900 dark:text-white">Reset Database</span>
              </div>
            </Link>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6">
          <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">System Status</h2>
          <div className="space-y-4">
            <div className="flex justify-between items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="flex items-center">
                <div className="h-3 w-3 bg-green-500 rounded-full mr-3"></div>
                <span className="text-gray-900 dark:text-white">Database</span>
              </div>
              <span className="text-green-600 dark:text-green-400">Operational</span>
            </div>
            <div className="flex justify-between items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="flex items-center">
                <div className="h-3 w-3 bg-green-500 rounded-full mr-3"></div>
                <span className="text-gray-900 dark:text-white">Authentication</span>
              </div>
              <span className="text-green-600 dark:text-green-400">Operational</span>
            </div>
            <div className="flex justify-between items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="flex items-center">
                <div className="h-3 w-3 bg-green-500 rounded-full mr-3"></div>
                <span className="text-gray-900 dark:text-white">Storage</span>
              </div>
              <span className="text-green-600 dark:text-green-400">Operational</span>
            </div>
            <div className="flex justify-between items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="flex items-center">
                <div className="h-3 w-3 bg-green-500 rounded-full mr-3"></div>
                <span className="text-gray-900 dark:text-white">API</span>
              </div>
              <span className="text-green-600 dark:text-green-400">Operational</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
