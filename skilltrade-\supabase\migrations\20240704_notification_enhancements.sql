-- Migration for notification system enhancements
-- This adds support for direct message notifications and improves existing notification functionality

-- 1. Add a badge_count column to the notifications table to track unread notifications
ALTER TABLE notifications
ADD COLUMN IF NOT EXISTS badge_count INTEGER DEFAULT 1;

-- 2. Create a function to get unread notification count for a user
CREATE OR REPLACE FUNCTION get_unread_notification_count(user_id_param UUID)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  count_result INTEGER;
BEGIN
  SELECT COUNT(*) INTO count_result
  FROM notifications
  WHERE user_id = user_id_param AND is_read = false;
  
  RETURN count_result;
END;
$$;

-- 3. Create a function to get unread direct message count for a user
CREATE OR REPLACE FUNCTION get_unread_direct_message_count(user_id_param UUID)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  count_result INTEGER;
BEGIN
  SELECT COUNT(*) INTO count_result
  FROM conversation_participants cp
  JOIN conversations c ON cp.conversation_id = c.id
  WHERE cp.user_id = user_id_param
  AND c.last_message_at IS NOT NULL
  AND (cp.last_read_at IS NULL OR cp.last_read_at < c.last_message_at);
  
  RETURN count_result;
END;
$$;

-- 4. Create a function to get total unread count (notifications + direct messages)
CREATE OR REPLACE FUNCTION get_total_unread_count(user_id_param UUID)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  notification_count INTEGER;
  message_count INTEGER;
BEGIN
  SELECT get_unread_notification_count(user_id_param) INTO notification_count;
  SELECT get_unread_direct_message_count(user_id_param) INTO message_count;
  
  RETURN notification_count + message_count;
END;
$$;

-- 5. Enhance the create_notification function to support badge counts
CREATE OR REPLACE FUNCTION create_notification(
  user_id_param UUID,
  type_param TEXT,
  title_param TEXT,
  message_param TEXT,
  link_param TEXT DEFAULT NULL,
  badge_count_param INTEGER DEFAULT 1
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  has_message_column BOOLEAN;
  has_content_column BOOLEAN;
  has_link_column BOOLEAN;
  has_badge_count_column BOOLEAN;
  notification_id UUID;
BEGIN
  -- Check which columns exist in the notifications table
  SELECT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_name = 'notifications' AND column_name = 'message'
  ) INTO has_message_column;
  
  SELECT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_name = 'notifications' AND column_name = 'content'
  ) INTO has_content_column;
  
  SELECT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_name = 'notifications' AND column_name = 'link'
  ) INTO has_link_column;
  
  SELECT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_name = 'notifications' AND column_name = 'badge_count'
  ) INTO has_badge_count_column;
  
  -- Create notification based on available columns
  IF has_message_column AND has_link_column AND has_badge_count_column THEN
    -- Case 1: message, link, and badge_count columns exist
    INSERT INTO notifications (
      user_id,
      type,
      title,
      message,
      link,
      badge_count,
      is_read
    ) VALUES (
      user_id_param,
      type_param,
      title_param,
      message_param,
      link_param,
      badge_count_param,
      false
    ) RETURNING id INTO notification_id;
  ELSIF has_content_column AND has_link_column AND has_badge_count_column THEN
    -- Case 2: content, link, and badge_count columns exist
    INSERT INTO notifications (
      user_id,
      type,
      title,
      content,
      link,
      badge_count,
      is_read
    ) VALUES (
      user_id_param,
      type_param,
      title_param,
      message_param,
      link_param,
      badge_count_param,
      false
    ) RETURNING id INTO notification_id;
  ELSIF has_message_column AND has_link_column THEN
    -- Case 3: message and link columns exist, but no badge_count
    INSERT INTO notifications (
      user_id,
      type,
      title,
      message,
      link,
      is_read
    ) VALUES (
      user_id_param,
      type_param,
      title_param,
      message_param,
      link_param,
      false
    ) RETURNING id INTO notification_id;
  ELSIF has_content_column AND has_link_column THEN
    -- Case 4: content and link columns exist, but no badge_count
    INSERT INTO notifications (
      user_id,
      type,
      title,
      content,
      link,
      is_read
    ) VALUES (
      user_id_param,
      type_param,
      title_param,
      message_param,
      link_param,
      false
    ) RETURNING id INTO notification_id;
  ELSIF has_message_column THEN
    -- Case 5: Only message column exists
    INSERT INTO notifications (
      user_id,
      type,
      title,
      message,
      is_read
    ) VALUES (
      user_id_param,
      type_param,
      title_param,
      message_param,
      false
    ) RETURNING id INTO notification_id;
  ELSIF has_content_column THEN
    -- Case 6: Only content column exists
    INSERT INTO notifications (
      user_id,
      type,
      title,
      content,
      is_read
    ) VALUES (
      user_id_param,
      type_param,
      title_param,
      message_param,
      false
    ) RETURNING id INTO notification_id;
  END IF;
  
  RETURN notification_id;
END;
$$;

-- 6. Update the direct message notification trigger to include badge count
CREATE OR REPLACE FUNCTION create_direct_message_notification()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
DECLARE
  recipient_record RECORD;
  sender_name TEXT;
  unread_count INTEGER;
BEGIN
  -- Get sender's display name
  SELECT display_name INTO sender_name
  FROM profiles
  WHERE id = NEW.sender_id;
  
  -- If display_name is null, use 'A user' instead
  IF sender_name IS NULL THEN
    sender_name := 'A user';
  END IF;
  
  -- Create a notification for each participant except the sender
  FOR recipient_record IN
    SELECT user_id
    FROM conversation_participants
    WHERE conversation_id = NEW.conversation_id
    AND user_id != NEW.sender_id
  LOOP
    -- Get current unread count for this conversation
    SELECT COUNT(*) INTO unread_count
    FROM direct_messages
    WHERE conversation_id = NEW.conversation_id
    AND sender_id != recipient_record.user_id
    AND created_at > COALESCE(
      (SELECT last_read_at FROM conversation_participants 
       WHERE conversation_id = NEW.conversation_id AND user_id = recipient_record.user_id),
      '1970-01-01'::timestamp
    );
    
    -- Call the create_notification function with badge count
    PERFORM create_notification(
      recipient_record.user_id,
      'direct_message',
      'New message from ' || sender_name,
      substring(NEW.content from 1 for 100),
      '/dashboard/messages/' || NEW.conversation_id,
      unread_count
    );
  END LOOP;
  
  RETURN NEW;
END;
$$;

-- Recreate the trigger
DROP TRIGGER IF EXISTS on_new_direct_message_notification ON direct_messages;
CREATE TRIGGER on_new_direct_message_notification
AFTER INSERT ON direct_messages
FOR EACH ROW
EXECUTE FUNCTION create_direct_message_notification();
