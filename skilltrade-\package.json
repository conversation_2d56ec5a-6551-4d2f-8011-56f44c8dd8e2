{"name": "skilltrade", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "setup-db": "node scripts/setup-supabase.js", "test-db": "node scripts/test-supabase.js", "setup-avatar-policies": "node scripts/setup-avatar-policies.js", "setup-avatar-policies-fixed": "node scripts/setup-avatar-policies-fixed.js", "test-avatar-storage": "node scripts/test-avatar-storage.js"}, "dependencies": {"@radix-ui/react-label": "^2.1.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-toast": "^1.2.11", "@supabase/ssr": "^0.6.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.10.1", "lucide-react": "^0.507.0", "next": "^14.2.28", "react": "^18.2.0", "react-dom": "^18.2.0", "sharp": "^0.34.1", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@supabase/supabase-js": "^2.49.4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.21", "dotenv": "^16.5.0", "eslint": "^8", "eslint-config-next": "14.0.3", "jest": "^29.7.0", "playwright": "^1.52.0", "postcss": "^8.5.3", "tailwindcss": "^3.3.0", "typescript": "^5"}}