'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Save, ArrowLeft, Shield, ShieldOff, Trash2 } from 'lucide-react';
import AdminPageHeader from '@/components/admin/AdminPageHeader';
import AdminDataTable from '@/components/admin/AdminDataTable';
import Link from 'next/link';

interface User {
  id: string;
  email: string;
  display_name: string | null;
  bio: string | null;
  hobbies: string[] | null;
  credit_balance: number;
  is_admin: boolean;
  created_at: string;
  updated_at: string | null;
}

interface Skill {
  id: string;
  title: string;
  description: string | null;
  tags: string[];
  is_active: boolean;
  created_at: string;
}

interface Session {
  id: string;
  skill: { title: string };
  teacher: { display_name: string | null };
  learner: { display_name: string | null };
  teacher_id: string;
  learner_id: string;
  scheduled_at: string;
  duration_hours: number;
  status: string;
}

interface Transaction {
  id: string;
  hours_delta: number;
  reason: string;
  created_at: string;
}

export default function AdminUserDetailPage({
  params,
}: {
  params: { id: string };
}) {
  const userId = params.id;
  const router = useRouter();

  const [user, setUser] = useState<User | null>(null);
  const [skills, setSkills] = useState<Skill[]>([]);
  const [sessions, setSessions] = useState<Session[]>([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'profile' | 'skills' | 'sessions' | 'transactions'>('profile');

  // Form state
  const [formData, setFormData] = useState({
    display_name: '',
    email: '',
    bio: '',
    hobbies: [] as string[],
    credit_balance: 0,
    is_admin: false,
  });

  // Fetch user data
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        setLoading(true);

        const response = await fetch(`/api/admin/users/${userId}`);

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to fetch user');
        }

        const data = await response.json();
        setUser(data.profile);
        setSkills(data.skills);
        setSessions(data.sessions);
        setTransactions(data.transactions);

        // Initialize form data
        setFormData({
          display_name: data.profile.display_name || '',
          email: data.profile.email || '',
          bio: data.profile.bio || '',
          hobbies: data.profile.hobbies || [],
          credit_balance: data.profile.credit_balance || 0,
          is_admin: data.profile.is_admin || false,
        });
      } catch (error: any) {
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, [userId]);

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  // Handle checkbox changes
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData({
      ...formData,
      [name]: checked,
    });
  };

  // Handle number input changes
  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: parseFloat(value),
    });
  };

  // Handle hobbies input (comma-separated)
  const handleHobbiesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const hobbiesString = e.target.value;
    const hobbiesArray = hobbiesString.split(',').map(hobby => hobby.trim()).filter(Boolean);
    setFormData({
      ...formData,
      hobbies: hobbiesArray,
    });
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setSaving(true);

      const response = await fetch(`/api/admin/users/${userId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update user');
      }

      const data = await response.json();
      setUser(data.profile);

      // Show success message
      alert('User updated successfully');
    } catch (error: any) {
      setError(error.message);
    } finally {
      setSaving(false);
    }
  };

  // Handle role change
  const handleRoleChange = async (action: 'promote' | 'demote') => {
    try {
      const response = await fetch(`/api/admin/users/${userId}/role`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to ${action} user`);
      }

      const data = await response.json();
      setUser(data.profile);
      setFormData({
        ...formData,
        is_admin: data.profile.is_admin,
      });

      // Show success message
      alert(`User ${action === 'promote' ? 'promoted to' : 'demoted from'} admin successfully`);
    } catch (error: any) {
      setError(error.message);
    }
  };

  // Handle user deletion
  const handleDeleteUser = async () => {
    if (!confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/users/${userId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete user');
      }

      // Redirect to users list
      router.push('/admin/users');
    } catch (error: any) {
      setError(error.message);
    }
  };

  // Skill table columns
  const skillColumns = [
    {
      key: 'title',
      label: 'Title',
      sortable: true,
    },
    {
      key: 'tags',
      label: 'Tags',
      render: (tags: string[]) => (
        <div className="flex flex-wrap gap-1">
          {tags.map((tag, index) => (
            <span
              key={index}
              className="px-2 py-1 text-xs rounded-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300"
            >
              {tag}
            </span>
          ))}
        </div>
      ),
    },
    {
      key: 'is_active',
      label: 'Status',
      sortable: true,
      render: (isActive: boolean) => (
        <span
          className={`px-2 py-1 text-xs rounded-full ${
            isActive
              ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'
              : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
          }`}
        >
          {isActive ? 'Active' : 'Inactive'}
        </span>
      ),
    },
    {
      key: 'created_at',
      label: 'Created',
      sortable: true,
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
  ];

  // Session table columns
  const sessionColumns = [
    {
      key: 'skill',
      label: 'Skill',
      render: (skill: { title: string }) => skill.title,
    },
    {
      key: 'role',
      label: 'Role',
      render: (_: any, session: Session) => (
        session.teacher_id === userId ? 'Teacher' : 'Learner'
      ),
    },
    {
      key: 'with',
      label: 'With',
      render: (_: any, session: Session) => {
        const otherPerson = session.teacher_id === userId ? session.learner : session.teacher;
        return otherPerson?.display_name || 'Unknown User';
      },
    },
    {
      key: 'scheduled_at',
      label: 'Date',
      sortable: true,
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (status: string) => (
        <span
          className={`px-2 py-1 text-xs rounded-full ${
            status === 'completed'
              ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'
              : status === 'pending'
              ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300'
              : status === 'cancelled'
              ? 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'
              : 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300'
          }`}
        >
          {status}
        </span>
      ),
    },
  ];

  // Transaction table columns
  const transactionColumns = [
    {
      key: 'hours_delta',
      label: 'Amount',
      sortable: true,
      render: (amount: number) => (
        <span className={amount > 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}>
          {amount > 0 ? `+${amount}` : amount}
        </span>
      ),
    },
    {
      key: 'reason',
      label: 'Reason',
      sortable: true,
      render: (reason: string) => (
        <span className="capitalize">{reason}</span>
      ),
    },
    {
      key: 'created_at',
      label: 'Date',
      sortable: true,
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
  ];

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p className="ml-4 text-gray-700 dark:text-gray-300">Loading user data...</p>
      </div>
    );
  }

  return (
    <div>
      <AdminPageHeader
        title={user?.display_name || user?.email || 'User Details'}
        backHref="/admin/users"
        actions={
          <div className="flex space-x-3">
            {user?.is_admin ? (
              <button
                onClick={() => handleRoleChange('demote')}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
              >
                <ShieldOff className="h-5 w-5 mr-2" />
                Demote from Admin
              </button>
            ) : (
              <button
                onClick={() => handleRoleChange('promote')}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                <Shield className="h-5 w-5 mr-2" />
                Promote to Admin
              </button>
            )}
            <button
              onClick={handleDeleteUser}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              <Trash2 className="h-5 w-5 mr-2" />
              Delete User
            </button>
          </div>
        }
      />

      {error && (
        <div className="bg-red-100 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 px-4 py-3 rounded-lg mb-6">
          {error}
        </div>
      )}

      {/* Tabs */}
      <div className="mb-6 border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('profile')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'profile'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600'
            }`}
          >
            Profile
          </button>
          <button
            onClick={() => setActiveTab('skills')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'skills'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600'
            }`}
          >
            Skills ({skills.length})
          </button>
          <button
            onClick={() => setActiveTab('sessions')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'sessions'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600'
            }`}
          >
            Sessions ({sessions.length})
          </button>
          <button
            onClick={() => setActiveTab('transactions')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'transactions'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600'
            }`}
          >
            Transactions ({transactions.length})
          </button>
        </nav>
      </div>

      {/* Profile Tab */}
      {activeTab === 'profile' && (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6">
          <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="display_name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Display Name
                </label>
                <input
                  type="text"
                  id="display_name"
                  name="display_name"
                  value={formData.display_name}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Email
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>

              <div className="md:col-span-2">
                <label htmlFor="bio" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Bio
                </label>
                <textarea
                  id="bio"
                  name="bio"
                  value={formData.bio}
                  onChange={handleInputChange}
                  rows={4}
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>

              <div>
                <label htmlFor="hobbies" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Hobbies (comma-separated)
                </label>
                <input
                  type="text"
                  id="hobbies"
                  name="hobbies"
                  value={formData.hobbies.join(', ')}
                  onChange={handleHobbiesChange}
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>

              <div>
                <label htmlFor="credit_balance" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Credit Balance
                </label>
                <input
                  type="number"
                  id="credit_balance"
                  name="credit_balance"
                  value={formData.credit_balance}
                  onChange={handleNumberChange}
                  step="0.1"
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>

              <div className="md:col-span-2">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="is_admin"
                    name="is_admin"
                    checked={formData.is_admin}
                    onChange={handleCheckboxChange}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="is_admin" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                    Admin User
                  </label>
                </div>
              </div>
            </div>

            <div className="mt-6 flex justify-end">
              <button
                type="submit"
                disabled={saving}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {saving ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-5 w-5 mr-2" />
                    Save Changes
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Skills Tab */}
      {activeTab === 'skills' && (
        <AdminDataTable
          columns={skillColumns}
          data={skills}
          searchable={true}
          searchKeys={['title', 'tags']}
          actions={(skill) => (
            <Link
              href={`/admin/skills/${skill.id}`}
              className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
            >
              View
            </Link>
          )}
          emptyState={
            <div className="text-center">
              <p className="text-gray-500 dark:text-gray-400">This user has not created any skills yet.</p>
            </div>
          }
        />
      )}

      {/* Sessions Tab */}
      {activeTab === 'sessions' && (
        <AdminDataTable
          columns={sessionColumns}
          data={sessions}
          searchable={true}
          searchKeys={['skill.title', 'status']}
          actions={(session) => (
            <Link
              href={`/admin/sessions/${session.id}`}
              className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
            >
              View
            </Link>
          )}
          emptyState={
            <div className="text-center">
              <p className="text-gray-500 dark:text-gray-400">This user has not participated in any sessions yet.</p>
            </div>
          }
        />
      )}

      {/* Transactions Tab */}
      {activeTab === 'transactions' && (
        <AdminDataTable
          columns={transactionColumns}
          data={transactions}
          searchable={true}
          searchKeys={['reason']}
          emptyState={
            <div className="text-center">
              <p className="text-gray-500 dark:text-gray-400">This user has no credit transactions yet.</p>
            </div>
          }
        />
      )}
    </div>
  );
}
