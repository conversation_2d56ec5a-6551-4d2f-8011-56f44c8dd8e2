-- This SQL script promotes a user to admin status
-- Replace '<EMAIL>' with the email of the user you want to promote

-- First, make sure the is_admin column exists
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'profiles' AND column_name = 'is_admin'
    ) THEN
        ALTER TABLE profiles ADD COLUMN is_admin BOOLEAN NOT NULL DEFAULT false;
    END IF;
END
$$;

-- Promote the user to admin
UPDATE profiles
SET is_admin = true
WHERE email = '<EMAIL>';

-- Verify the update
SELECT id, email, display_name, is_admin
FROM profiles
WHERE email = '<EMAIL>';
