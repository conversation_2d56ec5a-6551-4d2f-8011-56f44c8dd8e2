import { createServerSide } from '@/lib/supabase-server';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get('code');

  if (code) {
    const supabase = await createServerSide();
    const { data, error } = await supabase.auth.exchangeCodeForSession(code);

    if (!error && data.user) {
      // Check if the user has a display name in their profile
      const { data: profile } = await supabase
        .from('profiles')
        .select('display_name')
        .eq('id', data.user.id)
        .single();

      // If the profile doesn't have a display name, update it with the one from metadata
      if (profile && !profile.display_name && data.user.user_metadata?.display_name) {
        await supabase
          .from('profiles')
          .update({ display_name: data.user.user_metadata.display_name })
          .eq('id', data.user.id);
      }
    }
  }

  // URL to redirect to after sign in process completes
  return NextResponse.redirect(new URL('/dashboard', request.url));
}
