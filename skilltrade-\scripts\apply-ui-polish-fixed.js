// <PERSON>ript to apply UI polish to the application (fixed version)
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const originalDir = path.join(process.cwd(), 'components', 'profile');
const polishedDir = path.join(process.cwd(), 'components', 'profile', 'polished');
const fixedDir = path.join(process.cwd(), 'components', 'profile', 'fixed');
const backupDir = path.join(process.cwd(), 'components', 'profile', 'backup');

// Create directories if they don't exist
if (!fs.existsSync(backupDir)) {
  fs.mkdirSync(backupDir, { recursive: true });
}

if (!fs.existsSync(fixedDir)) {
  fs.mkdirSync(fixedDir, { recursive: true });
}

// Get all component files
const componentFiles = fs.readdirSync(originalDir)
  .filter(file => file.endsWith('.tsx') && file !== 'index.tsx');

// Function to backup original components
function backupComponents() {
  console.log('Backing up original components...');
  
  componentFiles.forEach(file => {
    const originalPath = path.join(originalDir, file);
    const backupPath = path.join(backupDir, file);
    
    fs.copyFileSync(originalPath, backupPath);
    console.log(`Backed up ${file}`);
  });
  
  console.log('Backup complete!');
}

// Function to fix polished components
function fixPolishedComponents() {
  console.log('Fixing polished components...');
  
  componentFiles.forEach(file => {
    const polishedPath = path.join(polishedDir, file);
    const fixedPath = path.join(fixedDir, file);
    
    if (!fs.existsSync(polishedPath)) {
      console.log(`Polished version of ${file} not found, skipping...`);
      return;
    }
    
    let content = fs.readFileSync(polishedPath, 'utf8');
    
    // Remove AnimatedComponent wrapper
    content = content.replace(/\/\/ Added animation wrapper[\s\S]*?<\/AnimatedComponent>/g, '');
    content = content.replace(/<AnimatedComponent>[\s\S]*?return \(/g, 'return (');
    content = content.replace(/<\/AnimatedComponent>(\s*)\);(\s*)}/g, ');$1}');
    
    // Add animate-fadeIn class directly to the main div
    content = content.replace(/className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm/g, 'className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm animate-fadeIn');
    
    fs.writeFileSync(fixedPath, content);
    console.log(`Fixed ${file}`);
  });
  
  // Copy animations.css to styles directory
  const animationsSource = path.join(polishedDir, 'animations.css');
  const animationsTarget = path.join(process.cwd(), 'styles', 'animations.css');
  
  if (fs.existsSync(animationsSource)) {
    fs.copyFileSync(animationsSource, animationsTarget);
    console.log('Copied animations.css to styles directory');
  }
  
  console.log('Fixing complete!');
}

// Function to apply fixed components
function applyFixedComponents() {
  console.log('Applying fixed components...');
  
  componentFiles.forEach(file => {
    const originalPath = path.join(originalDir, file);
    const fixedPath = path.join(fixedDir, file);
    
    if (!fs.existsSync(fixedPath)) {
      console.log(`Fixed version of ${file} not found, skipping...`);
      return;
    }
    
    fs.copyFileSync(fixedPath, originalPath);
    console.log(`Applied fixed version of ${file}`);
  });
  
  console.log('UI polish applied successfully!');
}

// Function to restore original components
function restoreComponents() {
  console.log('Restoring original components...');
  
  componentFiles.forEach(file => {
    const originalPath = path.join(originalDir, file);
    const backupPath = path.join(backupDir, file);
    
    if (!fs.existsSync(backupPath)) {
      console.log(`Backup of ${file} not found, skipping...`);
      return;
    }
    
    fs.copyFileSync(backupPath, originalPath);
    console.log(`Restored original version of ${file}`);
  });
  
  console.log('Restoration complete!');
}

// Main function
function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  switch (command) {
    case 'backup':
      backupComponents();
      break;
      
    case 'fix':
      fixPolishedComponents();
      break;
      
    case 'apply':
      backupComponents();
      fixPolishedComponents();
      applyFixedComponents();
      break;
      
    case 'restore':
      restoreComponents();
      break;
      
    default:
      console.log('Usage: node apply-ui-polish-fixed.js [backup|fix|apply|restore]');
      console.log('  backup: Backup original components');
      console.log('  fix: Fix polished components (remove AnimatedComponent wrapper)');
      console.log('  apply: Apply fixed components (includes backup and fix)');
      console.log('  restore: Restore original components from backup');
      break;
  }
}

main();
