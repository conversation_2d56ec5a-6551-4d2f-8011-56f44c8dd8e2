-- Fix the issue where available dates still show up after a session is completed
-- This migration adds a trigger to mark dates as booked when a session is completed or reviewed

-- Create a function to mark dates as booked when a session status changes
CREATE OR REPLACE FUNCTION mark_date_booked_on_session_update()
RETURNS TRIGGER AS $$
DECLARE
  v_scheduled_at TIMESTAMPTZ;
  v_skill_id UUID;
BEGIN
  -- Get the scheduled time and skill ID
  SELECT scheduled_at, skill_id INTO v_scheduled_at, v_skill_id FROM sessions WHERE id = NEW.id;
  
  -- If the session status is changing to 'accepted', 'completed', 'reviewed', or 'disputed'
  -- Mark any matching available dates as booked
  IF NEW.status IN ('accepted', 'completed', 'reviewed', 'disputed') THEN
    -- Find and mark any matching available dates as booked
    UPDATE skill_available_dates
    SET is_booked = TRUE
    WHERE skill_id = v_skill_id
    AND date_time = v_scheduled_at
    AND is_booked = FALSE;
    
    -- Log the update for debugging
    RAISE NOTICE 'Marked date as booked for session % with status %', NEW.id, NEW.status;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to run the function when a session is updated
DROP TRIGGER IF EXISTS trg_mark_date_booked_on_session_update ON sessions;
CREATE TRIGGER trg_mark_date_booked_on_session_update
AFTER UPDATE OF status ON sessions
FOR EACH ROW
EXECUTE FUNCTION mark_date_booked_on_session_update();

-- Also run a one-time update to fix any existing sessions
DO $$
DECLARE
  v_session RECORD;
BEGIN
  -- Find all sessions that are accepted, completed, reviewed, or disputed
  FOR v_session IN 
    SELECT id, skill_id, scheduled_at, status 
    FROM sessions 
    WHERE status IN ('accepted', 'completed', 'reviewed', 'disputed')
  LOOP
    -- Mark any matching available dates as booked
    UPDATE skill_available_dates
    SET is_booked = TRUE
    WHERE skill_id = v_session.skill_id
    AND date_time = v_session.scheduled_at
    AND is_booked = FALSE;
    
    -- Log the update for debugging
    RAISE NOTICE 'Fixed date for existing session % with status %', v_session.id, v_session.status;
  END LOOP;
END;
$$;
