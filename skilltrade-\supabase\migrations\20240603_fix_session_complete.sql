-- Update the mark_session_complete function to use the create_notification function
CREATE OR REPLACE FUNCTION mark_session_complete(p_session_id UUID, p_user_id UUID)
RETURNS void AS $$
DECLARE
  session_record RECORD;
  other_user_id UUID;
  is_teacher BOOLEAN;
  both_marked BOOLEAN;
BEGIN
  -- Get the session details
  SELECT * INTO session_record FROM sessions WHERE id = p_session_id;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Session not found';
  END IF;
  
  -- Check if the user is a participant
  IF p_user_id != session_record.teacher_id AND p_user_id != session_record.learner_id THEN
    RAISE EXCEPTION 'User is not a participant in this session';
  END IF;
  
  -- Determine if the user is the teacher
  is_teacher := (p_user_id = session_record.teacher_id);
  
  -- Update the appropriate marked_complete field
  IF is_teacher THEN
    UPDATE sessions 
    SET teacher_marked_complete = TRUE, 
        teacher_marked_at = NOW()
    WHERE id = p_session_id;
    
    other_user_id := session_record.learner_id;
  ELSE
    UPDATE sessions 
    SET learner_marked_complete = TRUE,
        learner_marked_at = NOW()
    WHERE id = p_session_id;
    
    other_user_id := session_record.teacher_id;
  END IF;
  
  -- Check if both parties have marked complete
  SELECT 
    teacher_marked_complete AND learner_marked_complete INTO both_marked 
  FROM sessions 
  WHERE id = p_session_id;
  
  -- If both have marked complete, update the status
  IF both_marked THEN
    UPDATE sessions 
    SET status = 'completed' 
    WHERE id = p_session_id;
  END IF;
  
  -- Create a notification for the other party
  PERFORM create_notification(
    other_user_id,
    'session',
    'Session Completion Update',
    CASE 
      WHEN both_marked THEN 'The session has been marked as completed by both parties.'
      ELSE 'The other party has marked the session as complete. Please confirm completion if you agree.'
    END,
    '/dashboard/sessions/' || p_session_id
  );
END;
$$ LANGUAGE plpgsql;
