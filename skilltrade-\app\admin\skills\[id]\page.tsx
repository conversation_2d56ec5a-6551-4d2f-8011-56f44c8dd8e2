'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Save, ArrowLeft, CheckCircle, XCircle, Trash2 } from 'lucide-react';
import AdminPageHeader from '@/components/admin/AdminPageHeader';
import AdminDataTable from '@/components/admin/AdminDataTable';
import Link from 'next/link';

interface Owner {
  id: string;
  display_name: string | null;
  email: string | null;
  avatar_url: string | null;
}

interface Skill {
  id: string;
  title: string;
  description: string | null;
  tags: string[];
  is_active: boolean;
  created_at: string;
  owner: Owner;
}

interface Session {
  id: string;
  teacher: { display_name: string | null };
  learner: { display_name: string | null };
  scheduled_at: string;
  duration_hours: number;
  status: string;
}

export default function AdminSkillDetailPage({
  params,
}: {
  params: { id: string };
}) {
  const skillId = params.id;
  const router = useRouter();
  
  const [skill, setSkill] = useState<Skill | null>(null);
  const [sessions, setSessions] = useState<Session[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Form state
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    tags: [] as string[],
    is_active: false,
  });
  
  // Fetch skill data
  useEffect(() => {
    const fetchSkillData = async () => {
      try {
        setLoading(true);
        
        const response = await fetch(`/api/admin/skills/${skillId}`);
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to fetch skill');
        }
        
        const data = await response.json();
        setSkill(data.skill);
        setSessions(data.sessions);
        
        // Initialize form data
        setFormData({
          title: data.skill.title || '',
          description: data.skill.description || '',
          tags: data.skill.tags || [],
          is_active: data.skill.is_active || false,
        });
      } catch (error: any) {
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };
    
    fetchSkillData();
  }, [skillId]);
  
  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };
  
  // Handle checkbox changes
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData({
      ...formData,
      [name]: checked,
    });
  };
  
  // Handle tags input (comma-separated)
  const handleTagsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const tagsString = e.target.value;
    const tagsArray = tagsString.split(',').map(tag => tag.trim()).filter(Boolean);
    setFormData({
      ...formData,
      tags: tagsArray,
    });
  };
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setSaving(true);
      
      const response = await fetch(`/api/admin/skills/${skillId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update skill');
      }
      
      const data = await response.json();
      setSkill(data.skill);
      
      // Show success message
      alert('Skill updated successfully');
    } catch (error: any) {
      setError(error.message);
    } finally {
      setSaving(false);
    }
  };
  
  // Handle status change
  const handleStatusChange = async (action: 'approve' | 'reject') => {
    try {
      const response = await fetch(`/api/admin/skills/${skillId}/status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: action }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to ${action} skill`);
      }
      
      const data = await response.json();
      setSkill(data.skill);
      setFormData({
        ...formData,
        is_active: data.skill.is_active,
      });
      
      // Show success message
      alert(`Skill ${action === 'approve' ? 'approved' : 'rejected'} successfully`);
    } catch (error: any) {
      setError(error.message);
    }
  };
  
  // Handle skill deletion
  const handleDeleteSkill = async () => {
    if (!confirm('Are you sure you want to delete this skill? This action cannot be undone.')) {
      return;
    }
    
    try {
      const response = await fetch(`/api/admin/skills/${skillId}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete skill');
      }
      
      // Redirect to skills list
      router.push('/admin/skills');
    } catch (error: any) {
      setError(error.message);
    }
  };
  
  // Session table columns
  const sessionColumns = [
    {
      key: 'teacher',
      label: 'Teacher',
      render: (teacher: { display_name: string | null }) => (
        teacher?.display_name || 'Unknown User'
      ),
    },
    {
      key: 'learner',
      label: 'Learner',
      render: (learner: { display_name: string | null }) => (
        learner?.display_name || 'Unknown User'
      ),
    },
    {
      key: 'scheduled_at',
      label: 'Date',
      sortable: true,
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
    {
      key: 'duration_hours',
      label: 'Duration',
      render: (hours: number) => `${hours} hour${hours !== 1 ? 's' : ''}`,
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (status: string) => (
        <span
          className={`px-2 py-1 text-xs rounded-full ${
            status === 'completed'
              ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'
              : status === 'pending'
              ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300'
              : status === 'cancelled'
              ? 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'
              : 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300'
          }`}
        >
          {status}
        </span>
      ),
    },
  ];
  
  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p className="ml-4 text-gray-700 dark:text-gray-300">Loading skill data...</p>
      </div>
    );
  }
  
  return (
    <div>
      <AdminPageHeader
        title={skill?.title || 'Skill Details'}
        backHref="/admin/skills"
        actions={
          <div className="flex space-x-3">
            {skill?.is_active ? (
              <button
                onClick={() => handleStatusChange('reject')}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                <XCircle className="h-5 w-5 mr-2" />
                Reject Skill
              </button>
            ) : (
              <button
                onClick={() => handleStatusChange('approve')}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                <CheckCircle className="h-5 w-5 mr-2" />
                Approve Skill
              </button>
            )}
            <button
              onClick={handleDeleteSkill}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              <Trash2 className="h-5 w-5 mr-2" />
              Delete Skill
            </button>
          </div>
        }
      />
      
      {error && (
        <div className="bg-red-100 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 px-4 py-3 rounded-lg mb-6">
          {error}
        </div>
      )}
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <div className="lg:col-span-2">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Skill Information</h2>
            <form onSubmit={handleSubmit}>
              <div className="space-y-6">
                <div>
                  <label htmlFor="title" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Title
                  </label>
                  <input
                    type="text"
                    id="title"
                    name="title"
                    value={formData.title}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
                
                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Description
                  </label>
                  <textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    rows={4}
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
                
                <div>
                  <label htmlFor="tags" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Tags (comma-separated)
                  </label>
                  <input
                    type="text"
                    id="tags"
                    name="tags"
                    value={formData.tags.join(', ')}
                    onChange={handleTagsChange}
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="is_active"
                    name="is_active"
                    checked={formData.is_active}
                    onChange={handleCheckboxChange}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="is_active" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                    Active
                  </label>
                </div>
                
                <div className="flex justify-end">
                  <button
                    type="submit"
                    disabled={saving}
                    className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {saving ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="h-5 w-5 mr-2" />
                        Save Changes
                      </>
                    )}
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
        
        <div>
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Owner Information</h2>
            {skill?.owner ? (
              <div>
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 rounded-full bg-gray-200 dark:bg-gray-700 flex-shrink-0 overflow-hidden mr-4">
                    {skill.owner.avatar_url ? (
                      <img
                        src={skill.owner.avatar_url}
                        alt={skill.owner.display_name || 'User'}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <span className="text-lg text-gray-500 dark:text-gray-400">
                          {skill.owner.display_name?.charAt(0) || skill.owner.email?.charAt(0) || '?'}
                        </span>
                      </div>
                    )}
                  </div>
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                      {skill.owner.display_name || 'Unnamed User'}
                    </h3>
                    <p className="text-gray-500 dark:text-gray-400">{skill.owner.email}</p>
                  </div>
                </div>
                <Link
                  href={`/admin/users/${skill.owner.id}`}
                  className="inline-block px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  View Profile
                </Link>
              </div>
            ) : (
              <p className="text-gray-500 dark:text-gray-400">Owner information not available</p>
            )}
          </div>
          
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mt-6">
            <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Skill Details</h2>
            <div className="space-y-4">
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Status</p>
                <p className="mt-1 text-gray-900 dark:text-white">
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      skill?.is_active
                        ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'
                        : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'
                    }`}
                  >
                    {skill?.is_active ? 'Active' : 'Inactive'}
                  </span>
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Created</p>
                <p className="mt-1 text-gray-900 dark:text-white">
                  {skill?.created_at ? new Date(skill.created_at).toLocaleDateString() : 'Unknown'}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Sessions</p>
                <p className="mt-1 text-gray-900 dark:text-white">
                  {sessions.length}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Sessions</h2>
        <AdminDataTable
          columns={sessionColumns}
          data={sessions}
          searchable={false}
          actions={(session) => (
            <Link
              href={`/admin/sessions/${session.id}`}
              className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
            >
              View
            </Link>
          )}
          emptyState={
            <div className="text-center">
              <p className="text-gray-500 dark:text-gray-400">No sessions for this skill yet</p>
            </div>
          }
        />
      </div>
    </div>
  );
}
