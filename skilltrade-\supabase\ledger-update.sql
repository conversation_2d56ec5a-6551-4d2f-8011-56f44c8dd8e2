-- Update the ledger table to allow null session_id for admin adjustments
-- Run this SQL in the Supabase SQL Editor

-- First, modify the ledger table to allow null session_id
ALTER TABLE ledger 
ALTER COLUMN session_id DROP NOT NULL;

-- Add a comment to explain the change
COMMENT ON COLUMN ledger.session_id IS 'Session ID (can be null for admin adjustments)';

-- Create a dummy session ID function for admin adjustments
-- This will be used when we need to reference a session but don't have one
CREATE OR REPLACE FUNCTION get_admin_adjustment_session()
RETURNS UUID AS $$
DECLARE
  admin_session_id UUID;
BEGIN
  -- Check if we already have a dummy session for admin adjustments
  SELECT id INTO admin_session_id
  FROM sessions
  WHERE skill_id = '00000000-0000-0000-0000-000000000000'
  LIMIT 1;
  
  -- If not, create one
  IF admin_session_id IS NULL THEN
    -- First, check if we have the dummy skill
    DECLARE
      dummy_skill_id UUID;
    BEGIN
      -- Try to find the dummy skill
      SELECT id INTO dummy_skill_id
      FROM skills
      WHERE title = 'Admin Adjustment'
      LIMIT 1;
      
      -- If not found, create it
      IF dummy_skill_id IS NULL THEN
        -- Find an admin user
        DECLARE
          admin_user_id UUID;
        BEGIN
          SELECT id INTO admin_user_id
          FROM profiles
          WHERE is_admin = true
          LIMIT 1;
          
          -- If no admin found, use the first user
          IF admin_user_id IS NULL THEN
            SELECT id INTO admin_user_id
            FROM profiles
            LIMIT 1;
          END IF;
          
          -- Create the dummy skill
          INSERT INTO skills (id, owner_id, title, description, is_active)
          VALUES (
            '00000000-0000-0000-0000-000000000000',
            admin_user_id,
            'Admin Adjustment',
            'System skill for admin credit adjustments',
            false
          )
          ON CONFLICT (id) DO NOTHING
          RETURNING id INTO dummy_skill_id;
        END;
      END IF;
      
      -- Create the dummy session
      INSERT INTO sessions (
        skill_id,
        teacher_id,
        learner_id,
        scheduled_at,
        duration_hours,
        status
      )
      SELECT
        dummy_skill_id,
        owner_id,
        owner_id,
        NOW(),
        0,
        'system'
      FROM skills
      WHERE id = dummy_skill_id
      RETURNING id INTO admin_session_id;
    END;
  END IF;
  
  RETURN admin_session_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the credit adjustment function to use the dummy session
CREATE OR REPLACE FUNCTION adjust_credits(
  user_id UUID,
  hours_delta NUMERIC,
  reason TEXT
) RETURNS VOID AS $$
DECLARE
  admin_session_id UUID;
BEGIN
  -- Get or create the admin adjustment session
  admin_session_id := get_admin_adjustment_session();
  
  -- Add the credit adjustment to the ledger
  INSERT INTO ledger(
    user_id,
    session_id,
    hours_delta,
    reason,
    created_at
  )
  VALUES (
    user_id,
    admin_session_id,
    hours_delta,
    reason,
    NOW()
  );
  
  -- Update the user's credit balance
  UPDATE profiles
  SET credit_balance = credit_balance + hours_delta
  WHERE id = user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
