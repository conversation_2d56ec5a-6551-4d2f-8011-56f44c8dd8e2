import { NextRequest, NextResponse } from 'next/server';
import { createServerSide } from '@/lib/supabase-server';
import { createAdminClient } from '@/lib/supabase-admin';

export const dynamic = 'force-dynamic';

// POST /api/sessions/message-counts - Get message counts for multiple sessions
export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerSide();

    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session IDs from the request body
    const { sessionIds } = await request.json();

    if (!sessionIds || !Array.isArray(sessionIds) || sessionIds.length === 0) {
      return NextResponse.json(
        { error: 'Session IDs array is required' },
        { status: 400 }
      );
    }

    // Use the admin client to bypass RLS
    const adminSupabase = createAdminClient();

    // Get all sessions to determine the other party for each session
    const { data: sessions, error: sessionsError } = await adminSupabase
      .from('sessions')
      .select('id, teacher_id, learner_id')
      .in('id', sessionIds);

    if (sessionsError) {
      throw sessionsError;
    }

    if (!sessions || sessions.length === 0) {
      return NextResponse.json({ messageCounts: {} });
    }

    // Create a map of session IDs to the other party's ID
    const otherPartyMap = sessions.reduce((map, session) => {
      const otherPartyId = session.teacher_id === user.id ? session.learner_id : session.teacher_id;
      map[session.id] = otherPartyId;
      return map;
    }, {} as Record<string, string>);

    // Get message counts for each session
    const messageCounts: Record<string, number> = {};

    // Process each session to get message counts
    await Promise.all(
      sessions.map(async (session) => {
        const otherPartyId = otherPartyMap[session.id];
        
        // Get count of messages from the other party
        const { count, error: countError } = await adminSupabase
          .from('session_messages')
          .select('*', { count: 'exact', head: true })
          .eq('session_id', session.id)
          .eq('sender_id', otherPartyId);

        if (countError) {
          console.error(`Error counting messages for session ${session.id}:`, countError);
          return;
        }

        messageCounts[session.id] = count || 0;
      })
    );

    return NextResponse.json({ messageCounts });
  } catch (error: any) {
    console.error('Error fetching message counts:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch message counts' },
      { status: 500 }
    );
  }
}

// GET /api/sessions/message-counts?session_id={session_id} - Get message count for a single session
export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerSide();

    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const sessionId = searchParams.get('session_id');

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      );
    }

    // Use the admin client to bypass RLS
    const adminSupabase = createAdminClient();

    // Get the session to determine the other party
    const { data: session, error: sessionError } = await adminSupabase
      .from('sessions')
      .select('teacher_id, learner_id')
      .eq('id', sessionId)
      .single();

    if (sessionError) {
      throw sessionError;
    }

    // Determine the other party
    const otherPartyId = session.teacher_id === user.id ? session.learner_id : session.teacher_id;

    // Get count of messages from the other party
    const { count, error: countError } = await adminSupabase
      .from('session_messages')
      .select('*', { count: 'exact', head: true })
      .eq('session_id', sessionId)
      .eq('sender_id', otherPartyId);

    if (countError) {
      throw countError;
    }

    return NextResponse.json({
      sessionId,
      messageCount: count || 0
    });
  } catch (error: any) {
    console.error('Error fetching message count:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch message count' },
      { status: 500 }
    );
  }
}
