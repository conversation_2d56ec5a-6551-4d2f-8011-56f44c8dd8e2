# Skilltrade Admin Dashboard Setup

This document provides instructions for setting up and using the Skilltrade admin dashboard.

## Database Setup

Before using the admin dashboard, you need to run the SQL script to add admin functionality to the database:

1. Go to the Supabase dashboard for your project
2. Navigate to the SQL Editor
3. Copy and paste the contents of `supabase/admin-schema.sql`
4. Run the SQL script

This will:
- Add an `is_admin` field to the profiles table
- Create admin-specific RLS policies
- Add functions for promoting/demoting users to/from admin

## Promoting the First Admin

To promote a user to admin status, you can use the provided script:

```bash
# Install ts-node if you don't have it
npm install -g ts-node

# Promote a specific user by email
npx ts-node scripts/promote-admin.ts <EMAIL>

# Or promote the first user in the database
npx ts-node scripts/promote-admin.ts
```

## Accessing the Admin Dashboard

Once you have admin privileges, you can access the admin dashboard at:

```
/admin
```

If you try to access the admin dashboard without admin privileges, you will be redirected to the dashboard.

## Admin Features

The admin dashboard provides the following features:

### User Management
- View all users
- Edit user details
- Promote/demote users to/from admin
- Adjust user credit balances

### Skill Management
- View all skills
- Approve/reject skills
- Edit skill details

### Session Management
- View all sessions
- Update session status
- Resolve disputed sessions

### Credit System Management
- View credit transactions
- Adjust user credit balances

### Analytics and Reporting
- View platform statistics
- Track user growth
- Monitor session completion rates
- Identify popular skills

## Security

The admin dashboard is protected by middleware that checks if the user has admin privileges. Only users with the `is_admin` field set to `true` in the profiles table can access the admin dashboard.

## Adding More Admins

To promote more users to admin status:

1. Log in as an admin
2. Go to the User Management section
3. Find the user you want to promote
4. Click the "Promote to Admin" button

Alternatively, you can use the script mentioned above to promote users programmatically.
