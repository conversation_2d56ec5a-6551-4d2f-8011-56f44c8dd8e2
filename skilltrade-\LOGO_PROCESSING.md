# Logo Processing for Skilltrade

This document provides instructions for processing the Skilltrade logo to round its edges and improve its appearance.

## Processing the Logo

The `scripts/process-logo.js` script will:

1. Create a version of the logo with rounded corners (`logo-rounded.png`)
2. Create a circular version of the logo (`logo-circle.png`)
3. Generate favicon files with rounded corners

### Running the Script

To process the logo:

1. Install the required dependency:
   ```bash
   npm install sharp
   ```

2. Run the logo processing script:
   ```bash
   node scripts/process-logo.js
   ```

3. The script will create the following files in the `public` directory:
   - `logo-rounded.png` - Logo with rounded corners
   - `logo-circle.png` - Circular version of the logo
   - Updated favicon files with rounded corners

## Updating the Code

After running the script, you'll need to update the code to use the new logo files:

### Main Header

Update the `MainHeader.tsx` component:

```jsx
<Image 
  src="/logo-rounded.png" // or "/logo-circle.png" if you prefer the circular version
  alt="Skilltrade Logo" 
  width={40} 
  height={40} 
  className="mr-2"
/>
```

### Admin Navigation

Update the `AdminNav.tsx` component:

```jsx
<Image
  src="/logo-rounded.png" // or "/logo-circle.png" if you prefer the circular version
  alt="Skilltrade Logo"
  width={36}
  height={36}
  className="mr-2"
/>
```

### Login and Signup Pages

Update the logo in the login and signup pages:

```jsx
<Image 
  src="/logo-rounded.png" // or "/logo-circle.png" if you prefer the circular version
  alt="Skilltrade Logo" 
  width={50} 
  height={50} 
  className="mr-2"
/>
```

## Favicon Configuration

The favicon files are already configured in the `app/layout.tsx` file. The script will update these files with rounded versions, so no code changes are needed for the favicons.

## Choosing Between Rounded and Circular

The script creates two versions of the logo:

1. **Rounded Corners (`logo-rounded.png`)**: Preserves the original shape but with rounded corners
2. **Circular (`logo-circle.png`)**: Creates a perfectly circular logo

Choose the version that best fits your design aesthetic and update the code accordingly.
