'use client';

import { useState, useEffect } from 'react';
import { createClientSide } from '@/lib/supabase';
import MainHeader from '@/components/MainHeader';
import MainFooter from '@/components/MainFooter';

export default function TestAvatarPage() {
  const [userId, setUserId] = useState<string | null>(null);
  const [file, setFile] = useState<File | null>(null);
  const [avatarUrl, setAvatarUrl] = useState<string | null>(null);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [logs, setLogs] = useState<string[]>([]);
  
  const supabase = createClientSide();
  
  useEffect(() => {
    const checkUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        setUserId(user.id);
        addLog(`User authenticated: ${user.id}`);
        
        // Get current avatar
        const { data: profile } = await supabase
          .from('profiles')
          .select('avatar_url')
          .eq('id', user.id)
          .single();
          
        if (profile?.avatar_url) {
          setAvatarUrl(profile.avatar_url);
          addLog(`Current avatar URL: ${profile.avatar_url}`);
        }
      }
    };
    
    checkUser();
  }, []);
  
  const addLog = (message: string) => {
    setLogs(prev => [...prev, `${new Date().toISOString().split('T')[1].split('.')[0]}: ${message}`]);
  };
  
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFile(e.target.files[0]);
      addLog(`File selected: ${e.target.files[0].name} (${e.target.files[0].size} bytes)`);
    }
  };
  
  const handleUpload = async () => {
    if (!userId || !file) {
      setError('User ID or file is missing');
      return;
    }
    
    try {
      setUploading(true);
      setError(null);
      setSuccess(null);
      addLog('Starting upload...');
      
      // Create a unique file name
      const fileExt = file.name.split('.').pop();
      const fileName = `${userId}-${Date.now()}.${fileExt}`;
      const filePath = `${userId}/${fileName}`;
      
      addLog(`Uploading to path: ${filePath}`);
      
      // Upload the file
      const { data, error: uploadError } = await supabase.storage
        .from('avatars')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: true
        });
      
      if (uploadError) {
        throw uploadError;
      }
      
      addLog('Upload successful');
      
      // Get the public URL
      const { data: { publicUrl } } = supabase.storage
        .from('avatars')
        .getPublicUrl(filePath);
      
      addLog(`Public URL: ${publicUrl}`);
      
      // Update the user's profile with the new avatar URL
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ avatar_url: publicUrl })
        .eq('id', userId);
      
      if (updateError) {
        throw updateError;
      }
      
      addLog('Profile updated successfully');
      setAvatarUrl(publicUrl);
      setSuccess('Avatar uploaded and profile updated successfully');
      
    } catch (error: any) {
      console.error('Error uploading avatar:', error);
      setError(error.message || 'An error occurred during upload');
      addLog(`Error: ${error.message || 'Unknown error'}`);
    } finally {
      setUploading(false);
    }
  };
  
  const handleRemove = async () => {
    if (!userId || !avatarUrl) {
      setError('User ID or avatar URL is missing');
      return;
    }
    
    try {
      setUploading(true);
      setError(null);
      setSuccess(null);
      addLog('Starting avatar removal...');
      
      // Extract the path from the URL
      addLog(`Avatar URL: ${avatarUrl}`);
      const urlParts = avatarUrl.split('/');
      const bucketIndex = urlParts.indexOf('public') + 1;
      
      if (bucketIndex > 0 && bucketIndex < urlParts.length) {
        const bucket = urlParts[bucketIndex];
        const path = urlParts.slice(bucketIndex + 1).join('/');
        
        addLog(`Extracted path: ${path}`);
        
        // Delete the file
        const { error: deleteError } = await supabase.storage
          .from('avatars')
          .remove([path]);
        
        if (deleteError) {
          throw deleteError;
        }
        
        addLog('File deleted successfully');
        
        // Update the user's profile to remove the avatar URL
        const { error: updateError } = await supabase
          .from('profiles')
          .update({ avatar_url: null })
          .eq('id', userId);
        
        if (updateError) {
          throw updateError;
        }
        
        addLog('Profile updated successfully');
        setAvatarUrl(null);
        setSuccess('Avatar removed successfully');
        
      } else {
        throw new Error('Could not extract path from URL');
      }
      
    } catch (error: any) {
      console.error('Error removing avatar:', error);
      setError(error.message || 'An error occurred during removal');
      addLog(`Error: ${error.message || 'Unknown error'}`);
    } finally {
      setUploading(false);
    }
  };
  
  const checkBucket = async () => {
    try {
      addLog('Checking avatars bucket...');
      
      // List buckets
      const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
      
      if (bucketsError) {
        throw bucketsError;
      }
      
      const avatarsBucket = buckets.find(bucket => bucket.name === 'avatars');
      
      if (avatarsBucket) {
        addLog(`Avatars bucket found: ${JSON.stringify(avatarsBucket)}`);
      } else {
        addLog('Avatars bucket not found!');
      }
      
    } catch (error: any) {
      console.error('Error checking bucket:', error);
      setError(error.message || 'An error occurred while checking bucket');
      addLog(`Error: ${error.message || 'Unknown error'}`);
    }
  };
  
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <MainHeader />
      
      <main className="container mx-auto py-8 px-4">
        <div className="max-w-3xl mx-auto">
          <h1 className="text-3xl font-bold mb-6 text-center">Avatar Upload Test</h1>
          
          {!userId ? (
            <div className="bg-yellow-100 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-300 p-4 rounded-lg mb-6">
              Please sign in to test avatar functionality
            </div>
          ) : (
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
              <div className="mb-6">
                <h2 className="text-xl font-semibold mb-2">Current Status</h2>
                <p><strong>User ID:</strong> {userId}</p>
                <p><strong>Avatar URL:</strong> {avatarUrl || 'None'}</p>
              </div>
              
              {avatarUrl && (
                <div className="mb-6">
                  <h2 className="text-xl font-semibold mb-2">Current Avatar</h2>
                  <div className="flex items-center space-x-4">
                    <div className="w-24 h-24 rounded-full overflow-hidden bg-gray-200 dark:bg-gray-700">
                      {/* Regular img tag for testing */}
                      <img 
                        src={avatarUrl} 
                        alt="Avatar" 
                        className="w-full h-full object-cover"
                        onError={() => addLog('Error loading avatar image')}
                        onLoad={() => addLog('Avatar image loaded successfully')}
                      />
                    </div>
                    <button
                      onClick={handleRemove}
                      disabled={uploading}
                      className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50"
                    >
                      {uploading ? 'Removing...' : 'Remove Avatar'}
                    </button>
                  </div>
                </div>
              )}
              
              <div className="mb-6">
                <h2 className="text-xl font-semibold mb-2">Upload New Avatar</h2>
                <div className="flex flex-col sm:flex-row sm:items-center gap-4">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleFileChange}
                    disabled={uploading}
                    className="flex-grow"
                  />
                  <button
                    onClick={handleUpload}
                    disabled={!file || uploading}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                  >
                    {uploading ? 'Uploading...' : 'Upload'}
                  </button>
                </div>
              </div>
              
              <div className="mb-6">
                <button
                  onClick={checkBucket}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                >
                  Check Avatars Bucket
                </button>
              </div>
              
              {error && (
                <div className="bg-red-100 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 p-4 rounded-lg mb-6">
                  {error}
                </div>
              )}
              
              {success && (
                <div className="bg-green-100 dark:bg-green-900/30 border border-green-200 dark:border-green-800 text-green-800 dark:text-green-300 p-4 rounded-lg mb-6">
                  {success}
                </div>
              )}
              
              <div className="mt-8">
                <h2 className="text-xl font-semibold mb-2">Logs</h2>
                <div className="bg-gray-100 dark:bg-gray-900 p-4 rounded-lg h-64 overflow-y-auto font-mono text-sm">
                  {logs.map((log, index) => (
                    <div key={index} className="mb-1">{log}</div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </main>
      
      <MainFooter />
    </div>
  );
}
