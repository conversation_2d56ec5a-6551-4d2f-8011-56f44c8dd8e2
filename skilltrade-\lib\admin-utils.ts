import { createServerSide } from './supabase-server';

// Check if the current user is an admin
export async function isAdmin() {
  console.log('Checking if user is admin');
  const supabase = await createServerSide();

  // Get the current user
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError) {
    console.error('Error getting user:', userError);
    return false;
  }

  if (!user) {
    console.log('No user found');
    return false;
  }

  console.log('User found, checking admin status for user ID:', user.id);

  // Check if the user is an admin
  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('is_admin')
    .eq('id', user.id)
    .single();

  if (profileError) {
    console.error('Error getting profile:', profileError);
    return false;
  }

  console.log('Admin status:', profile?.is_admin);
  return profile?.is_admin || false;
}

// Middleware function to check if a user is an admin
export async function adminGuard() {
  const isUserAdmin = await isAdmin();

  if (!isUserAdmin) {
    return {
      redirect: {
        destination: '/dashboard',
        permanent: false,
      },
    };
  }

  return {
    props: {},
  };
}
