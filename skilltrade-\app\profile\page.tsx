'use client';

import { useState, useEffect } from 'react';
import { createClientSide } from '@/lib/supabase';
import { useRouter } from 'next/navigation';
import ProfileAvatarUpload from '@/components/ProfileAvatarUpload';
import Link from 'next/link';
import MainHeader from '@/components/MainHeader';
import MainFooter from '@/components/MainFooter';
import LearningGoals from '@/components/profile/LearningGoals';
import AvailabilityCalendar from '@/components/profile/AvailabilityCalendar';

interface Profile {
  id: string;
  email: string;
  display_name: string | null;
  avatar_url: string | null;
  bio: string | null;
  hobbies: string[] | null;
  credit_balance: number;
  learning_goals: string[] | null;
}

interface Skill {
  id: string;
  title: string;
}

interface Review {
  id: string;
  comment: string;
  rating?: number | string;
  created_at: string;
  reviewer?: {
    display_name: string | null;
    avatar_url: string | null;
  };
}

interface Session {
  id: string;
  skill: { title: string };
  scheduled_at: string;
  status: string;
  teacher_id: string;
  learner_id: string;
}

export default function ProfilePage() {
  const [profile, setProfile] = useState<Profile | null>(null);
  const [skills, setSkills] = useState<Skill[]>([]);
  const [reviews, setReviews] = useState<Review[]>([]);
  const [sessions, setSessions] = useState<Session[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [displayName, setDisplayName] = useState('');
  const [bio, setBio] = useState('');
  const [learningGoals, setLearningGoals] = useState<string[]>([]);
  const [availabilitySlots, setAvailabilitySlots] = useState<any[]>([]);
  const [hobbies, setHobbies] = useState<string[]>([]);
  const [hobbiesInput, setHobbiesInput] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [message, setMessage] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const router = useRouter();
  const supabase = createClientSide();

  useEffect(() => {
    const fetchProfileData = async () => {
      try {
        setLoading(true);

        // Get the current user
        const { data: { user } } = await supabase.auth.getUser();

        if (!user) {
          router.push('/login');
          return;
        }

        // Get the user's profile
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();

        if (profileError) {
          throw profileError;
        }

        setProfile(profileData);
        setDisplayName(profileData.display_name || '');
        setBio(profileData.bio || '');
        setLearningGoals(profileData.learning_goals || []);
        const userHobbies = profileData.hobbies || [];
        setHobbies(userHobbies);
        setHobbiesInput(userHobbies.join(', '));

        // Fetch availability slots
        const { data: availabilityData, error: availabilityError } = await supabase
          .from('user_availability')
          .select('*')
          .eq('user_id', user.id)
          .order('day_of_week', { ascending: true });

        if (availabilityError) {
          console.error('Error fetching availability slots:', availabilityError);
        } else {
          // Transform the data to a more usable format
          const transformedSlots = (availabilityData || []).map(slot => ({
            id: slot.id,
            dayOfWeek: slot.day_of_week,
            startTime: slot.start_time,
            endTime: slot.end_time,
            isRecurring: slot.is_recurring
          }));

          setAvailabilitySlots(transformedSlots);
        }

        // Get user's skills
        const { data: skillsData, error: skillsError } = await supabase
          .from('skills')
          .select('id, title')
          .eq('owner_id', user.id)
          .order('created_at', { ascending: false });

        if (skillsError) {
          throw skillsError;
        }

        setSkills(skillsData || []);

        // Get reviews received as a teacher
        const { data: teacherReviewsData, error: teacherReviewsError } = await supabase
          .from('reviews')
          .select(`
            id,
            comment,
            rating,
            reviewer_id,
            created_at,
            session_id,
            sessions!inner(teacher_id)
          `)
          .eq('sessions.teacher_id', user.id)
          .order('created_at', { ascending: false })
          .limit(3);

        if (teacherReviewsError) {
          throw teacherReviewsError;
        }

        // Get all reviewer IDs
        const reviewerIds = teacherReviewsData?.map(review => review.reviewer_id) || [];

        // Fetch all reviewer profiles in a single query
        const { data: reviewerProfiles, error: profilesError } = await supabase
          .from('profiles')
          .select('id, display_name, avatar_url')
          .in('id', reviewerIds);

        if (profilesError) {
          console.error('Error fetching reviewer profiles:', profilesError);
        }

        // Create a map of reviewer IDs to profiles for quick lookup
        const reviewerMap = (reviewerProfiles || []).reduce<Record<string, any>>((map, profile) => {
          if (profile && profile.id) {
            map[profile.id] = profile;
          }
          return map;
        }, {});

        // Transform the data to match the Review interface
        const transformedReviews = (teacherReviewsData || []).map(review => {
          const reviewer = reviewerMap[review.reviewer_id] || {};

          return {
            id: review.id,
            comment: review.comment,
            rating: review.rating,
            created_at: review.created_at,
            reviewer: {
              display_name: reviewer.display_name,
              avatar_url: reviewer.avatar_url
            }
          };
        });

        setReviews(transformedReviews);

        // Get recent sessions
        const { data: sessionsData, error: sessionsError } = await supabase
          .from('sessions')
          .select(`
            id,
            skill:skills(title),
            scheduled_at,
            status,
            teacher_id,
            learner_id
          `)
          .or(`teacher_id.eq.${user.id},learner_id.eq.${user.id}`)
          .order('scheduled_at', { ascending: false })
          .limit(5);

        if (sessionsError) {
          throw sessionsError;
        }

        // Transform the data to match the Session interface
        const transformedSessions = (sessionsData || []).map(session => ({
          id: session.id,
          scheduled_at: session.scheduled_at,
          status: session.status,
          teacher_id: session.teacher_id,
          learner_id: session.learner_id,
          skill: {
            title: Array.isArray(session.skill) && session.skill.length > 0
              ? session.skill[0].title
              : 'Unknown Skill'
          }
        }));

        setSessions(transformedSessions);

      } catch (error: any) {
        setError(error.message || 'Failed to load profile');
      } finally {
        setLoading(false);
      }
    };

    fetchProfileData();
  }, [router, supabase]);

  const handleUpdateProfile = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);
    setError(null);
    setMessage(null);

    try {
      if (!profile) return;

      // Validate display name
      if (!displayName.trim()) {
        setError('Display name is required');
        setIsSaving(false);
        return;
      }

      // Process hobbies from input before saving
      const processedHobbies = hobbiesInput
        .split(',')
        .map(h => h.trim())
        .filter(h => h !== '');
      setHobbies(processedHobbies);

      const { error } = await supabase
        .from('profiles')
        .update({
          display_name: displayName.trim(),
          bio,
          learning_goals: learningGoals,
          hobbies: processedHobbies,
          updated_at: new Date().toISOString(),
        })
        .eq('id', profile.id);

      if (error) {
        throw error;
      }

      setMessage('Profile updated successfully');

      // Update the local profile state
      setProfile({
        ...profile,
        display_name: displayName.trim(),
        bio,
        learning_goals: learningGoals,
        hobbies: processedHobbies,
      });

      setIsEditing(false);
    } catch (error: any) {
      setError(error.message || 'Failed to update profile');
    } finally {
      setIsSaving(false);
    }
  };

  const handleSaveLearningGoals = async (goals: string[]) => {
    if (!profile) return;

    setIsSaving(true);
    setError(null);
    setMessage(null);

    try {
      const { error } = await supabase
        .from('profiles')
        .update({
          learning_goals: goals,
          updated_at: new Date().toISOString(),
        })
        .eq('id', profile.id);

      if (error) {
        throw error;
      }

      setLearningGoals(goals);
      setProfile({
        ...profile,
        learning_goals: goals,
      });

      setMessage('Learning goals updated successfully');

      // Hide message after 3 seconds
      setTimeout(() => {
        setMessage(null);
      }, 3000);

    } catch (error: any) {
      setError(error.message || 'Failed to update learning goals');
    } finally {
      setIsSaving(false);
    }
  };

  const handleSaveAvailabilitySlots = async (slots: any[]) => {
    if (!profile) return;

    setIsSaving(true);
    setError(null);
    setMessage(null);

    try {
      // First, delete all existing availability slots
      const { error: deleteError } = await supabase
        .from('user_availability')
        .delete()
        .eq('user_id', profile.id);

      if (deleteError) {
        throw deleteError;
      }

      // Then insert the new availability slots
      if (slots.length > 0) {
        const slotsToInsert = slots.map(slot => ({
          user_id: profile.id,
          day_of_week: slot.dayOfWeek,
          start_time: slot.startTime,
          end_time: slot.endTime,
          is_recurring: slot.isRecurring || true
        }));

        const { error: insertError } = await supabase
          .from('user_availability')
          .insert(slotsToInsert);

        if (insertError) {
          throw insertError;
        }
      }

      // Get the updated availability slots
      const { data: updatedSlots, error: fetchError } = await supabase
        .from('user_availability')
        .select('*')
        .eq('user_id', profile.id)
        .order('day_of_week', { ascending: true });

      if (fetchError) {
        throw fetchError;
      }

      // Transform the data to a more usable format
      const transformedSlots = (updatedSlots || []).map(slot => ({
        id: slot.id,
        dayOfWeek: slot.day_of_week,
        startTime: slot.start_time,
        endTime: slot.end_time,
        isRecurring: slot.is_recurring
      }));

      setAvailabilitySlots(transformedSlots);
      setMessage('Availability calendar updated successfully');

      // Hide message after 3 seconds
      setTimeout(() => {
        setMessage(null);
      }, 3000);

    } catch (error: any) {
      setError(error.message || 'Failed to update availability calendar');
    } finally {
      setIsSaving(false);
    }
  };

  const handleAvatarUpload = async (url: string) => {
    if (profile) {
      try {
        // Update the profile in the database
        if (url) {
          const { error } = await supabase
            .from('profiles')
            .update({ avatar_url: url })
            .eq('id', profile.id);

          if (error) {
            console.error('Error updating profile with avatar URL:', error);
            throw error;
          }
        }

        // Update the local state
        setProfile({
          ...profile,
          avatar_url: url,
        });

        console.log('Profile avatar updated successfully:', url);
      } catch (error) {
        console.error('Error in handleAvatarUpload:', error);
      }
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <MainHeader />
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto"></div>
            <p className="mt-4 text-lg text-gray-700 dark:text-gray-300">Loading profile...</p>
          </div>
        </div>
        <MainFooter />
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <MainHeader />
        <div className="flex items-center justify-center min-h-[60vh] px-4">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">Profile not found</h2>
            <p className="mb-6 text-gray-600 dark:text-gray-400">Please sign in to view your profile</p>
            <Link href="/login" className="px-6 py-3 bg-blue-600 rounded-lg hover:bg-blue-700 transition text-white">
              Sign in
            </Link>
          </div>
        </div>
        <MainFooter />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <MainHeader />

      <main className="container mx-auto py-8 px-4 max-w-4xl">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
          {/* Profile Header */}
          <div className="p-8">
            <div className="flex flex-col md:flex-row items-center md:items-start gap-8">
              <div className="flex-shrink-0">
                <ProfileAvatarUpload
                  userId={profile.id}
                  url={profile.avatar_url}
                  onUpload={handleAvatarUpload}
                  size={150}
                />
              </div>

              <div className="flex-grow text-center md:text-left">
                <h1 className="text-4xl font-bold mb-2">
                  {profile.display_name || profile.email?.split('@')[0] || 'Your Name'}
                </h1>

                {!profile.display_name && (
                  <div className="bg-yellow-100 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-300 px-4 py-3 rounded-lg mb-4">
                    Please set a display name so others can identify you on the platform.
                  </div>
                )}

                {!isEditing ? (
                  <>
                    <p className="text-gray-600 dark:text-gray-300 mb-4">
                      {profile.bio || 'Add a bio to tell others about yourself'}
                    </p>

                    <div className="mb-4">
                      <h3 className="text-lg font-semibold mb-2">Learning Goals</h3>
                      {profile.learning_goals && profile.learning_goals.length > 0 ? (
                        <div className="flex flex-wrap gap-2">
                          {profile.learning_goals.map((goal, index) => (
                            <span key={index} className="px-3 py-1 bg-gray-200 dark:bg-gray-700 rounded-full text-sm">
                              {goal}
                            </span>
                          ))}
                        </div>
                      ) : (
                        <p className="text-gray-500 dark:text-gray-400">
                          No learning goals added yet.
                        </p>
                      )}
                    </div>

                    <div className="mb-4">
                      <h3 className="text-lg font-semibold mb-2">Teaching Availability</h3>
                      {availabilitySlots.length > 0 ? (
                        <AvailabilityCalendar
                          availabilitySlots={availabilitySlots}
                          isEditable={false}
                        />
                      ) : (
                        <p className="text-gray-500 dark:text-gray-400">
                          No availability set yet.
                        </p>
                      )}
                    </div>
                  </>
                ) : (
                  <>
                    <div className="mb-4">
                      <label htmlFor="displayName" className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                        Display Name <span className="text-red-500">*</span>
                      </label>
                      <input
                        id="displayName"
                        type="text"
                        value={displayName}
                        onChange={(e) => setDisplayName(e.target.value)}
                        className="w-full px-4 py-2 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="How others will see you on the platform"
                        required
                      />
                    </div>

                    <div className="mb-4">
                      <label htmlFor="bio" className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                        Bio
                      </label>
                      <textarea
                        id="bio"
                        value={bio}
                        onChange={(e) => setBio(e.target.value)}
                        rows={3}
                        className="w-full px-4 py-2 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Tell us about yourself..."
                      ></textarea>
                    </div>

                    <div className="mb-4">
                      <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                        Learning Goals
                      </label>
                      <LearningGoals
                        learningGoals={learningGoals}
                        isEditable={true}
                        onSave={handleSaveLearningGoals}
                      />
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        Add skills you're interested in learning to help others find you for skill exchanges
                      </p>
                    </div>

                    <div className="mb-4">
                      <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                        Availability Calendar
                      </label>
                      <AvailabilityCalendar
                        availabilitySlots={availabilitySlots}
                        isEditable={true}
                        onSave={handleSaveAvailabilitySlots}
                      />
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        Set your regular teaching availability to help learners find suitable times
                      </p>
                    </div>
                  </>
                )}

                <div className="flex flex-wrap gap-4 mb-6">
                  <div className="flex items-center">
                    <svg className="w-5 h-5 mr-2 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span className="font-semibold">Time-Credits: {profile.credit_balance}h</span>
                  </div>

                  <div className="flex items-center">
                    <svg className="w-5 h-5 mr-2 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                    </svg>
                    <span className="font-semibold">Skills Offered: {skills.length}</span>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-4">
                  {!isEditing ? (
                    <button
                      onClick={() => setIsEditing(true)}
                      className="w-full sm:w-auto px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition font-medium"
                    >
                      Edit Profile
                    </button>
                  ) : (
                    <>
                      <button
                        onClick={handleUpdateProfile}
                        className="w-full sm:w-auto px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition font-medium"
                        disabled={isSaving}
                      >
                        {isSaving ? 'Saving...' : 'Save Changes'}
                      </button>

                      <button
                        onClick={() => setIsEditing(false)}
                        className="w-full sm:w-auto px-6 py-3 bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-white rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition font-medium"
                      >
                        Cancel
                      </button>
                    </>
                  )}

                  <Link
                    href="/skills/create"
                    className="w-full sm:w-auto px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition font-medium text-center"
                  >
                    Offer a New Skill
                  </Link>
                </div>
              </div>
            </div>
          </div>

          {/* Profile Content */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 p-8 border-t border-gray-200 dark:border-gray-700">
            {/* My Skills */}
            <div>
              <h2 className="text-2xl font-bold mb-4">My Skills</h2>
              {skills.length > 0 ? (
                <ul className="space-y-2">
                  {skills.map(skill => (
                    <li key={skill.id} className="flex items-start">
                      <span className="text-blue-500 mr-2">•</span>
                      <Link href={`/skills/${skill.id}`} className="hover:text-blue-500 transition">
                        {skill.title}
                      </Link>
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-gray-500 dark:text-gray-400">
                  You haven't added any skills yet.
                </p>
              )}

              <Link href="/skills" className="text-blue-500 hover:text-blue-600 mt-4 inline-block">
                View all skills →
              </Link>
            </div>

            {/* Recent Reviews */}
            <div>
              <h2 className="text-2xl font-bold mb-4">Recent Reviews</h2>
              {reviews.length > 0 ? (
                <ul className="space-y-3">
                  {reviews.map(review => (
                    <li key={review.id} className="bg-gray-100 dark:bg-gray-700 rounded-lg p-4">
                      <div className="flex items-center mb-2">
                        <div className="flex-shrink-0 mr-3">
                          {review.reviewer?.avatar_url ? (
                            <img
                              src={review.reviewer.avatar_url}
                              alt={review.reviewer?.display_name || 'Reviewer'}
                              className="w-10 h-10 rounded-full object-cover"
                            />
                          ) : (
                            <div className="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                              <span className="text-sm text-gray-500 dark:text-gray-400">
                                {(review.reviewer?.display_name || 'A').charAt(0).toUpperCase()}
                              </span>
                            </div>
                          )}
                        </div>
                        <div>
                          <div className="flex items-center">
                            <span className="font-medium text-gray-900 dark:text-white mr-2">
                              {review.reviewer?.display_name || 'Anonymous'}
                            </span>
                            <div className="flex items-center">
                              {[1, 2, 3, 4, 5].map((star) => (
                                <svg
                                  key={star}
                                  xmlns="http://www.w3.org/2000/svg"
                                  className={`h-4 w-4 ${star <= (typeof review.rating === 'number' ? review.rating : (review.rating === 'positive' ? 5 : 1)) ? 'text-yellow-500' : 'text-gray-300 dark:text-gray-600'}`}
                                  viewBox="0 0 20 20"
                                  fill="currentColor"
                                >
                                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                </svg>
                              ))}
                            </div>
                          </div>
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            {new Date(review.created_at).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      <p className="text-gray-600 dark:text-gray-300 mt-2">
                        "{review.comment || 'No comment provided'}"
                      </p>
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-gray-500 dark:text-gray-400">
                  You haven't received any reviews yet.
                </p>
              )}
            </div>

            {/* Hobbies */}
            <div>
              <h2 className="text-2xl font-bold mb-4">Hobbies</h2>
              {!isEditing ? (
                <div className="flex flex-wrap gap-2">
                  {hobbies && hobbies.length > 0 ? (
                    hobbies.map((hobby, index) => (
                      <span key={index} className="px-3 py-1 bg-gray-200 dark:bg-gray-700 rounded-full text-sm">
                        {hobby}
                      </span>
                    ))
                  ) : (
                    <p className="text-gray-500 dark:text-gray-400">
                      Add some hobbies to show what you're interested in.
                    </p>
                  )}
                </div>
              ) : (
                <div>
                  <div className="space-y-3">
                    <input
                      type="text"
                      value={hobbiesInput}
                      onChange={(e) => {
                        // Allow typing commas freely
                        setHobbiesInput(e.target.value);
                      }}
                      onBlur={() => {
                        // Only update the hobbies array when the input loses focus
                        const newHobbies = hobbiesInput
                          .split(',')
                          .map(h => h.trim())
                          .filter(h => h !== '');
                        setHobbies(newHobbies);
                      }}
                      className="w-full px-4 py-2 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Guitar, Languages, Cooking, etc. (comma separated)"
                    />
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Separate multiple hobbies with commas (e.g., "Guitar, Languages, Cooking")
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Session History */}
            <div>
              <h2 className="text-2xl font-bold mb-4">Session History</h2>
              {sessions.length > 0 ? (
                <ul className="space-y-2">
                  {sessions.slice(0, 3).map(session => {
                    const date = new Date(session.scheduled_at);
                    const isTeacher = session.teacher_id === profile.id;
                    return (
                      <li key={session.id} className="text-gray-600 dark:text-gray-300">
                        <Link href={`/sessions/${session.id}`} className="hover:text-blue-500 transition">
                          {date.toLocaleDateString()} – {isTeacher ? 'Taught' : 'Learned'} {session.skill.title} ({session.status})
                        </Link>
                      </li>
                    );
                  })}
                </ul>
              ) : (
                <p className="text-gray-500 dark:text-gray-400">
                  You haven't participated in any sessions yet.
                </p>
              )}

              <details className="mt-4">
                <summary className="cursor-pointer text-blue-500 hover:text-blue-600">
                  View more sessions
                </summary>
                <ul className="mt-2 space-y-2">
                  {sessions.slice(3).map(session => {
                    const date = new Date(session.scheduled_at);
                    const isTeacher = session.teacher_id === profile.id;
                    return (
                      <li key={session.id} className="text-gray-600 dark:text-gray-300">
                        <Link href={`/sessions/${session.id}`} className="hover:text-blue-500 transition">
                          {date.toLocaleDateString()} – {isTeacher ? 'Taught' : 'Learned'} {session.skill.title} ({session.status})
                        </Link>
                      </li>
                    );
                  })}
                </ul>
              </details>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="border-t border-gray-200 dark:border-gray-700 p-8 flex flex-col sm:flex-row justify-between gap-4">
            <Link
              href="/sessions"
              className="w-full sm:w-auto px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition font-medium text-center"
            >
              Book a Session
            </Link>

            <Link
              href="/dashboard"
              className="w-full sm:w-auto px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition font-medium text-center"
            >
              View Dashboard
            </Link>
          </div>
        </div>
      </main>

      <MainFooter />
    </div>
  );
}
