-- Create a table to store bad words for content filtering
CREATE TABLE IF NOT EXISTS bad_words (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  word TEXT NOT NULL UNIQUE,
  severity INTEGER NOT NULL DEFAULT 1, -- 1: mild, 2: moderate, 3: severe
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  created_by UUID REFERENCES auth.users(id),
  active BOOLEAN DEFAULT TRUE
);

-- Add RLS policies
ALTER TABLE bad_words ENABLE ROW LEVEL SECURITY;

-- Only admins can view, insert, update, or delete bad words
CREATE POLICY "Admins can view bad words" 
ON bad_words FOR SELECT 
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.is_admin = TRUE
  )
);

CREATE POLICY "Admins can insert bad words" 
ON bad_words FOR INSERT 
TO authenticated
WITH CHECK (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.is_admin = TRUE
  )
);

CREATE POLICY "Ad<PERSON> can update bad words" 
ON bad_words FOR UPDATE 
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.is_admin = TRUE
  )
);

CREATE POLICY "Admins can delete bad words" 
ON bad_words FOR DELETE 
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.is_admin = TRUE
  )
);

-- Function to get all active bad words
CREATE OR REPLACE FUNCTION get_bad_words()
RETURNS TABLE (word TEXT, severity INTEGER) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT bad_words.word, bad_words.severity
  FROM bad_words
  WHERE active = TRUE
  ORDER BY severity DESC, word;
END;
$$;

-- Function to add a new bad word
CREATE OR REPLACE FUNCTION add_bad_word(word_text TEXT, word_severity INTEGER DEFAULT 1)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  new_id UUID;
BEGIN
  -- Check if the user is an admin
  IF NOT EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.is_admin = TRUE
  ) THEN
    RAISE EXCEPTION 'Only admins can add bad words';
  END IF;

  -- Insert the new word
  INSERT INTO bad_words (word, severity, created_by)
  VALUES (LOWER(word_text), word_severity, auth.uid())
  RETURNING id INTO new_id;
  
  RETURN new_id;
END;
$$;

-- Function to update a bad word
CREATE OR REPLACE FUNCTION update_bad_word(word_id UUID, word_text TEXT DEFAULT NULL, word_severity INTEGER DEFAULT NULL, word_active BOOLEAN DEFAULT NULL)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the user is an admin
  IF NOT EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.is_admin = TRUE
  ) THEN
    RAISE EXCEPTION 'Only admins can update bad words';
  END IF;

  -- Update the word
  UPDATE bad_words
  SET 
    word = COALESCE(LOWER(word_text), word),
    severity = COALESCE(word_severity, severity),
    active = COALESCE(word_active, active)
  WHERE id = word_id;
  
  RETURN FOUND;
END;
$$;

-- Function to delete a bad word
CREATE OR REPLACE FUNCTION delete_bad_word(word_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the user is an admin
  IF NOT EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.is_admin = TRUE
  ) THEN
    RAISE EXCEPTION 'Only admins can delete bad words';
  END IF;

  -- Delete the word
  DELETE FROM bad_words
  WHERE id = word_id;
  
  RETURN FOUND;
END;
$$;

-- Insert initial bad words from the current list in the code
INSERT INTO bad_words (word, severity, created_at)
VALUES 
  ('fuck', 3, now()),
  ('shit', 2, now()),
  ('ass', 1, now()),
  ('bitch', 2, now()),
  ('dick', 2, now()),
  ('pussy', 3, now()),
  ('cock', 3, now()),
  ('cunt', 3, now()),
  ('whore', 3, now()),
  ('bastard', 2, now()),
  ('damn', 1, now()),
  ('asshole', 2, now()),
  ('piss', 1, now()),
  ('slut', 3, now()),
  ('tits', 2, now()),
  ('wanker', 2, now()),
  ('twat', 2, now())
ON CONFLICT (word) DO NOTHING;
