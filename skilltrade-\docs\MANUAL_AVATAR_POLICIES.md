# Manual Setup of Avatar Storage Policies in Supabase

If you're encountering issues with the automated scripts, you can set up the avatar storage policies manually through the Supabase dashboard. Follow these steps:

## 1. Create the Avatars Bucket (if not already created)

1. Log in to the [Supabase Dashboard](https://app.supabase.com/)
2. Select your project
3. Navigate to Storage in the left sidebar
4. Click "New Bucket"
5. Name the bucket "avatars"
6. Check "Public bucket" to make it public
7. Click "Create bucket"

## 2. Set Up Storage Policies

1. Click on the "avatars" bucket
2. Go to the "Policies" tab
3. Click "Add Policy" (or "New Policy")

### Policy 1: Viewing Avatars (Public Access)

1. Select "Get" (or "SELECT") operation
2. Policy name: "Avatars are viewable by everyone"
3. Policy definition: Select "Allow access to everyone" (or enter `true` in the definition field)
4. Click "Save Policy"

### Policy 2: Uploading Avatars (Authenticated Users Only)

1. Select "Insert" (or "INSERT") operation
2. Policy name: "Users can upload their own avatars"
3. Policy definition: Select "Allow authenticated users" (or enter `auth.uid() IS NOT NULL` in the definition field)
4. Click "Save Policy"

### Policy 3: Updating Avatars (Authenticated Users Only)

1. Select "Update" (or "UPDATE") operation
2. Policy name: "Users can update their own avatars"
3. Policy definition: Select "Allow authenticated users" (or enter `auth.uid() IS NOT NULL` in the definition field)
4. Click "Save Policy"

### Policy 4: Deleting Avatars (Authenticated Users Only)

1. Select "Delete" (or "DELETE") operation
2. Policy name: "Users can delete their own avatars"
3. Policy definition: Select "Allow authenticated users" (or enter `auth.uid() IS NOT NULL` in the definition field)
4. Click "Save Policy"

## 3. Verify the Policies

After setting up all four policies, you should see them listed in the Policies tab. The bucket should now have the following permissions:

- Everyone can view avatars
- Authenticated users can upload avatars
- Authenticated users can update avatars
- Authenticated users can delete avatars

## 4. Advanced Policies (Optional)

If you want more secure policies that ensure users can only access their own folders, you can create more specific policies. However, this requires a specific folder structure where each user's avatars are stored in a folder named with their user ID.

For these advanced policies, you would use a definition like:
```
auth.uid()::text = SPLIT_PART(storage.foldername(name), '/', 1)
```

This ensures that users can only access files in folders that match their user ID.

## 5. Testing the Policies

To test if the policies are working correctly:

1. Sign in to your application
2. Try to upload an avatar
3. Verify that the avatar is visible
4. Try to update or delete the avatar

If any of these operations fail, check the policy definitions and make sure they are set up correctly.
