-- Fix Avatar Storage Policies for Skilltrade using RLS approach
-- This script updates the storage policies to ensure avatar uploads and deletions work correctly

-- Create the avatars bucket if it doesn't exist
BEGIN;

-- Check if the bucket exists
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM storage.buckets WHERE name = 'avatars') THEN
    -- Create the bucket
    INSERT INTO storage.buckets (id, name, public)
    VALUES ('avatars', 'avatars', true);
  END IF;
END $$;

-- Remove existing policies
DROP POLICY IF EXISTS "Avatars are viewable by everyone" ON storage.objects;
DROP POLICY IF EXISTS "Users can upload their own avatars" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own avatars" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own avatars" ON storage.objects;

-- Create policies using RLS
-- Policy for viewing avatars (public access)
CREATE POLICY "Avatars are viewable by everyone" 
ON storage.objects FOR SELECT 
USING (bucket_id = 'avatars');

-- Policy for uploading avatars (authenticated users only)
CREATE POLICY "Users can upload their own avatars" 
ON storage.objects FOR INSERT 
TO authenticated
WITH CHECK (bucket_id = 'avatars');

-- Policy for updating avatars (authenticated users only)
CREATE POLICY "Users can update their own avatars" 
ON storage.objects FOR UPDATE 
TO authenticated
USING (bucket_id = 'avatars');

-- Policy for deleting avatars (authenticated users only)
CREATE POLICY "Users can delete their own avatars" 
ON storage.objects FOR DELETE 
TO authenticated
USING (bucket_id = 'avatars');

COMMIT;
