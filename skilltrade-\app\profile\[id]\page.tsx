'use client';

import { useState, useEffect } from 'react';
import { createClientSide } from '@/lib/supabase';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import MainHeader from '@/components/MainHeader';
import MainFooter from '@/components/MainFooter';
import { ProfileTabs } from '@/components/profile/ProfileTabs';
import LearningGoals from '@/components/profile/LearningGoals';
import AvailabilityCalendar from '@/components/profile/AvailabilityCalendar';

interface Profile {
  id: string;
  email: string;
  display_name: string | null;
  avatar_url: string | null;
  bio: string | null;
  hobbies: string[] | null;
  credit_balance: number;
  learning_goals: string[] | null;
}

interface Skill {
  id: string;
  title: string;
  description: string;
  tags: string[];
  is_active: boolean;
}

interface Review {
  id: string;
  comment: string;
  rating: number;
  created_at: string;
  session_id: string;
  reviewer: {
    id: string;
    display_name: string | null;
    avatar_url: string | null;
  };
  skill: {
    id: string;
    title: string;
  };
}

interface TimeSlot {
  id: string;
  dayOfWeek: number;
  startTime: string;
  endTime: string;
  isRecurring: boolean;
  specificDate: string | null;
}

export default function PublicProfilePage({ params }: { params: { id: string } }) {
  const [profile, setProfile] = useState<Profile | null>(null);
  const [skills, setSkills] = useState<Skill[]>([]);
  const [reviews, setReviews] = useState<Review[]>([]);
  const [availabilitySlots, setAvailabilitySlots] = useState<TimeSlot[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isCurrentUser, setIsCurrentUser] = useState(false);
  const [activeTab, setActiveTab] = useState('about');
  const router = useRouter();
  const supabase = createClientSide();
  const { id } = params;

  useEffect(() => {
    const fetchProfileData = async () => {
      try {
        setLoading(true);

        // Get the current user
        const { data: { user } } = await supabase.auth.getUser();

        // Check if this is the current user's profile
        if (user && user.id === id) {
          setIsCurrentUser(true);
        }

        // Get the user's profile
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', id)
          .single();

        if (profileError) {
          throw profileError;
        }

        console.log('Profile data from database:', profileData);
        console.log('Learning goals:', profileData.learning_goals);

        setProfile(profileData);

        // Get user's skills
        const { data: skillsData, error: skillsError } = await supabase
          .from('skills')
          .select('id, title, description, tags, is_active')
          .eq('owner_id', id)
          .eq('is_active', true)
          .order('created_at', { ascending: false });

        if (skillsError) {
          throw skillsError;
        }

        setSkills(skillsData || []);

        // Get reviews received as a teacher
        const { data: reviewsData, error: reviewsError } = await supabase
          .from('reviews')
          .select(`
            id,
            comment,
            rating,
            created_at,
            reviewer_id,
            session_id,
            sessions!inner(teacher_id)
          `)
          .eq('sessions.teacher_id', id)
          .order('created_at', { ascending: false });

        if (reviewsError) {
          throw reviewsError;
        }

        // Get all reviewer IDs
        const reviewerIds = reviewsData?.map(review => review.reviewer_id) || [];

        // Get session IDs
        const sessionIds = reviewsData?.map(review => review.session_id) || [];

        // Fetch sessions to get skill IDs
        const { data: sessionsData } = await supabase
          .from('sessions')
          .select('id, skill_id')
          .in('id', sessionIds);

        // Extract skill IDs from sessions
        const skillIds: string[] = [];
        sessionsData?.forEach(session => {
          if (session.skill_id) {
            skillIds.push(session.skill_id);
          }
        });

        // Fetch all reviewer profiles in a single query
        const { data: reviewerProfiles, error: profilesError } = await supabase
          .from('profiles')
          .select('id, display_name, avatar_url')
          .in('id', reviewerIds);

        if (profilesError) {
          console.error('Error fetching reviewer profiles:', profilesError);
        }

        // Fetch all skills in a single query
        const { data: reviewSkillsData, error: reviewSkillsError } = await supabase
          .from('skills')
          .select('id, title')
          .in('id', skillIds);

        if (reviewSkillsError) {
          console.error('Error fetching skills for reviews:', reviewSkillsError);
        }

        // Create maps for quick lookup
        const reviewerMap = (reviewerProfiles || []).reduce<Record<string, any>>((map, profile) => {
          if (profile && profile.id) {
            map[profile.id] = profile;
          }
          return map;
        }, {});

        const skillMap = (reviewSkillsData || []).reduce<Record<string, any>>((map, skill) => {
          if (skill && skill.id) {
            map[skill.id] = skill;
          }
          return map;
        }, {});

        // Create a map of session IDs to skill IDs
        const sessionToSkillMap: Record<string, string> = {};
        sessionsData?.forEach(session => {
          if (session.id && session.skill_id) {
            sessionToSkillMap[session.id] = session.skill_id;
          }
        });

        // Transform the data to match the Review interface
        const transformedReviews = (reviewsData || []).map(review => {
          const reviewer = reviewerMap[review.reviewer_id] || {};
          const skillId = sessionToSkillMap[review.session_id];
          const skill = skillId ? skillMap[skillId] || { id: skillId, title: 'Unknown Skill' } : { id: '', title: 'Unknown Skill' };

          return {
            id: review.id,
            comment: review.comment,
            rating: review.rating,
            created_at: review.created_at,
            session_id: review.session_id,
            reviewer: {
              id: reviewer.id || '',
              display_name: reviewer.display_name,
              avatar_url: reviewer.avatar_url
            },
            skill
          };
        });

        setReviews(transformedReviews);

        // Fetch availability slots
        const { data: availabilityData, error: availabilityError } = await supabase
          .from('user_availability')
          .select('*')
          .eq('user_id', id)
          .order('day_of_week')
          .order('start_time');

        if (availabilityError) {
          console.error('Error fetching availability slots:', availabilityError);
        } else {
          const transformedAvailabilitySlots = (availabilityData || []).map(slot => ({
            id: slot.id,
            dayOfWeek: slot.day_of_week,
            startTime: slot.start_time,
            endTime: slot.end_time,
            isRecurring: slot.is_recurring,
            specificDate: slot.specific_date
          }));

          console.log('Availability data from database:', availabilityData);
          console.log('Transformed availability slots:', transformedAvailabilitySlots);

          setAvailabilitySlots(transformedAvailabilitySlots);
        }

      } catch (error: any) {
        setError(error.message || 'Failed to load profile');
      } finally {
        setLoading(false);
      }
    };

    fetchProfileData();
  }, [id, router, supabase]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <MainHeader />
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto"></div>
            <p className="mt-4 text-lg text-gray-700 dark:text-gray-300">Loading profile...</p>
          </div>
        </div>
        <MainFooter />
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <MainHeader />
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">Profile not found</h2>
            <p className="mb-6 text-gray-600 dark:text-gray-400">The user profile you're looking for doesn't exist or has been removed.</p>
            <Link href="/explore" className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition">
              Explore Skills
            </Link>
          </div>
        </div>
        <MainFooter />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <MainHeader />

      <main className="container mx-auto py-4 sm:py-8 px-3 sm:px-4 max-w-4xl">
        {isCurrentUser && (
          <div className="mb-4 sm:mb-6 flex justify-end">
            <Link
              href="/profile"
              className="px-3 sm:px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition text-sm sm:text-base"
            >
              Edit Your Profile
            </Link>
          </div>
        )}

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
          {/* Profile Header */}
          <div className="p-4 sm:p-6 md:p-8">
            <div className="flex flex-col md:flex-row items-center md:items-start gap-4 sm:gap-6 md:gap-8">
              <div className="flex-shrink-0">
                {profile.avatar_url ? (
                  <Image
                    src={profile.avatar_url}
                    alt={profile.display_name || 'User'}
                    width={120}
                    height={120}
                    className="rounded-full object-cover border-4 border-gray-200 dark:border-gray-700 shadow-lg w-24 h-24 sm:w-28 sm:h-28 md:w-32 md:h-32"
                  />
                ) : (
                  <div className="w-24 h-24 sm:w-28 sm:h-28 md:w-32 md:h-32 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center border-4 border-gray-200 dark:border-gray-700 shadow-lg">
                    <span className="text-2xl sm:text-3xl md:text-4xl text-gray-500 dark:text-gray-300">
                      {profile.display_name?.charAt(0) || profile.email?.charAt(0) || 'U'}
                    </span>
                  </div>
                )}
              </div>

              <div className="flex-grow text-center md:text-left">
                <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-2 text-gray-900 dark:text-white">
                  {profile.display_name || 'User'}
                </h1>

                <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300 mb-4 sm:mb-6">
                  {profile.bio || 'No bio provided'}
                </p>

                <div className="flex flex-wrap justify-center md:justify-start gap-3 sm:gap-4 mb-4 sm:mb-6">
                  <div className="flex items-center">
                    <svg className="w-4 h-4 sm:w-5 sm:h-5 mr-1 sm:mr-2 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span className="text-sm sm:text-base font-semibold text-gray-700 dark:text-gray-300">Credits: {profile.credit_balance}h</span>
                  </div>

                  <div className="flex items-center">
                    <svg className="w-4 h-4 sm:w-5 sm:h-5 mr-1 sm:mr-2 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                    </svg>
                    <span className="text-sm sm:text-base font-semibold text-gray-700 dark:text-gray-300">Skills: {skills.length}</span>
                  </div>
                </div>

                {!isCurrentUser && skills.length > 0 && (
                  <Link
                    href={`/skills?teacher=${id}`}
                    className="px-4 sm:px-6 py-2 sm:py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition font-medium inline-block text-sm sm:text-base"
                  >
                    Book a Session
                  </Link>
                )}
              </div>
            </div>
          </div>

          {/* Profile Tabs */}
          <div className="border-t border-gray-200 dark:border-gray-700 p-4 sm:p-6 md:p-8">
            <ProfileTabs
              tabs={[
                { id: 'about', label: 'About' },
                { id: 'skills', label: 'Skills' },
                { id: 'reviews', label: 'Reviews' },
                { id: 'availability', label: 'Availability' },
              ]}
              defaultTab="about"
              onChange={setActiveTab}
              className="mb-4 sm:mb-6"
            />

            {activeTab === 'about' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                  <div className="mb-8">
                    <h2 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">About</h2>
                    <p className="text-gray-600 dark:text-gray-300">
                      {profile.bio || 'No bio provided'}
                    </p>
                  </div>

                  <div>
                    <h2 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">Hobbies & Interests</h2>
                    {profile.hobbies && profile.hobbies.length > 0 ? (
                      <div className="flex flex-wrap gap-2">
                        {profile.hobbies.map((hobby, index) => (
                          <span key={index} className="px-3 py-1 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-full text-sm">
                            {hobby}
                          </span>
                        ))}
                      </div>
                    ) : (
                      <p className="text-gray-500 dark:text-gray-400">
                        No hobbies listed.
                      </p>
                    )}
                  </div>
                </div>

                <div>
                  <div className="mb-8">
                    <LearningGoals
                      learningGoals={profile.learning_goals}
                      isEditable={false}
                    />
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'skills' && (
              <div>
                <h2 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">Skills Offered</h2>
                {skills.length > 0 ? (
                  <div className="space-y-4">
                    {skills.map(skill => (
                      <div key={skill.id} className="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg">
                        <Link href={`/skills/${skill.id}`} className="text-lg font-medium text-blue-600 dark:text-blue-400 hover:underline">
                          {skill.title}
                        </Link>
                        <p className="text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">
                          {skill.description || 'No description provided'}
                        </p>
                        <div className="flex flex-wrap gap-2 mt-2">
                          {skill.tags.map((tag, index) => (
                            <span key={index} className="px-2 py-1 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-full text-xs">
                              {tag}
                            </span>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 dark:text-gray-400">
                    This user hasn't added any skills yet.
                  </p>
                )}
              </div>
            )}

            {activeTab === 'reviews' && (
              <div>
                <h2 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">Reviews</h2>
                {reviews.length > 0 ? (
                  <div className="space-y-4">
                    {reviews.map(review => (
                      <div key={review.id} className="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg">
                        <div className="flex items-center mb-2">
                          <div className="flex-shrink-0 mr-3">
                            {review.reviewer.avatar_url ? (
                              <img
                                src={review.reviewer.avatar_url}
                                alt={review.reviewer.display_name || 'Reviewer'}
                                className="w-10 h-10 rounded-full object-cover"
                              />
                            ) : (
                              <div className="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                                <span className="text-sm text-gray-500 dark:text-gray-400">
                                  {(review.reviewer.display_name || 'A').charAt(0).toUpperCase()}
                                </span>
                              </div>
                            )}
                          </div>
                          <div>
                            <div className="flex items-center">
                              <span className="font-medium text-gray-900 dark:text-white mr-2">
                                {review.reviewer.display_name || 'Anonymous'}
                              </span>
                              <div className="flex items-center">
                                {[1, 2, 3, 4, 5].map((star) => (
                                  <svg
                                    key={star}
                                    xmlns="http://www.w3.org/2000/svg"
                                    className={`h-4 w-4 ${star <= review.rating ? 'text-yellow-500' : 'text-gray-300 dark:text-gray-600'}`}
                                    viewBox="0 0 20 20"
                                    fill="currentColor"
                                  >
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                ))}
                              </div>
                            </div>
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                              {new Date(review.created_at).toLocaleDateString()} • {review.skill.title}
                            </p>
                          </div>
                        </div>
                        <p className="text-gray-600 dark:text-gray-300 mt-2">
                          "{review.comment || 'No comment provided'}"
                        </p>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 dark:text-gray-400">
                    This user hasn't received any reviews yet.
                  </p>
                )}
              </div>
            )}

            {activeTab === 'availability' && (
              <div>
                <h2 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">Teaching Availability</h2>
                {availabilitySlots.length > 0 ? (
                  <AvailabilityCalendar
                    availabilitySlots={availabilitySlots}
                    isEditable={false}
                  />
                ) : (
                  <p className="text-gray-500 dark:text-gray-400">
                    This user hasn't set their teaching availability yet.
                  </p>
                )}
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="border-t border-gray-200 dark:border-gray-700 p-4 sm:p-6 md:p-8 flex flex-col sm:flex-row justify-between gap-3 sm:gap-4">
            {!isCurrentUser && skills.length > 0 ? (
              <Link
                href={`/skills?teacher=${id}`}
                className="w-full sm:w-auto px-4 sm:px-6 py-2 sm:py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition font-medium text-center text-sm sm:text-base"
              >
                View All Skills
              </Link>
            ) : (
              <Link
                href="/explore"
                className="w-full sm:w-auto px-4 sm:px-6 py-2 sm:py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition font-medium text-center text-sm sm:text-base"
              >
                Explore Skills
              </Link>
            )}

            <Link
              href="/dashboard"
              className="w-full sm:w-auto px-4 sm:px-6 py-2 sm:py-3 bg-gray-300 text-gray-800 dark:bg-gray-700 dark:text-white rounded-lg hover:bg-gray-400 dark:hover:bg-gray-600 transition font-medium text-center text-sm sm:text-base"
            >
              Back to Dashboard
            </Link>
          </div>
        </div>
      </main>

      <MainFooter />
    </div>
  );}
