import { NextRequest, NextResponse } from 'next/server';
import { createAdminClient } from '@/lib/supabase-admin';
import { isAdmin } from '@/lib/admin-utils';

// POST /api/admin/users/[id]/role - Promote/demote user to/from admin
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if the user is an admin
    const isUserAdmin = await isAdmin();
    if (!isUserAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    const userId = params.id;
    const supabase = createAdminClient();
    const body = await request.json();
    
    const { action } = body;
    
    if (action !== 'promote' && action !== 'demote') {
      return NextResponse.json(
        { error: 'Invalid action. Must be "promote" or "demote"' },
        { status: 400 }
      );
    }
    
    // Update the user's admin status
    const { data, error } = await supabase
      .from('profiles')
      .update({
        is_admin: action === 'promote',
        updated_at: new Date().toISOString(),
      })
      .eq('id', userId)
      .select()
      .single();
    
    if (error) {
      throw error;
    }
    
    return NextResponse.json({
      profile: data,
      message: `User ${action === 'promote' ? 'promoted to' : 'demoted from'} admin successfully`,
    });
  } catch (error: any) {
    console.error(`Error updating user role ${params.id}:`, error);
    return NextResponse.json(
      { error: error.message || 'Failed to update user role' },
      { status: 500 }
    );
  }
}
