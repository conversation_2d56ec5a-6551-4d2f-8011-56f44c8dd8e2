import { NextRequest, NextResponse } from 'next/server';
import { createServerSide } from '@/lib/supabase-server';
import { createAdminClient } from '@/lib/supabase-admin';

export const dynamic = 'force-dynamic';

// GET /api/conversations - Get all conversations for the current user
export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerSide();

    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const limit = parseInt(searchParams.get('limit') || '20');
    const page = parseInt(searchParams.get('page') || '1');
    const offset = (page - 1) * limit;

    // Use the admin client to bypass RLS
    const adminSupabase = createAdminClient();

    // Get all conversations where the user is a participant
    const { data: participantData, error: participantError } = await adminSupabase
      .from('conversation_participants')
      .select('conversation_id, last_read_at')
      .eq('user_id', user.id);

    if (participantError) {
      console.error('Error fetching participant data:', participantError);
      throw participantError;
    }

    if (!participantData || participantData.length === 0) {
      return NextResponse.json({
        conversations: [],
        count: 0,
        page,
        limit
      });
    }

    // Extract conversation IDs
    const conversationIds = participantData.map(p => p.conversation_id);

    // Create a map of conversation IDs to last_read_at times
    const lastReadMap = new Map();
    participantData.forEach(p => {
      lastReadMap.set(p.conversation_id, p.last_read_at);
    });

    // Get conversations with basic info
    const { data: conversations, error: conversationsError, count } = await adminSupabase
      .from('conversations')
      .select(`
        id,
        last_message_preview,
        last_message_at,
        created_at,
        updated_at,
        status
      `, { count: 'exact' })
      .in('id', conversationIds)
      .order('last_message_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (conversationsError) {
      console.error('Error fetching conversations:', conversationsError);
      throw conversationsError;
    }

    // Get other participants for these conversations
    const { data: participants, error: participantsError } = await adminSupabase
      .from('conversation_participants')
      .select(`
        conversation_id,
        user_id,
        user:profiles(
          id,
          display_name,
          avatar_url
        )
      `)
      .in('conversation_id', conversations.map(c => c.id))
      .neq('user_id', user.id);

    if (participantsError) {
      console.error('Error fetching participants:', participantsError);
      throw participantsError;
    }

    // Group participants by conversation
    const participantsByConversation = new Map();
    participants.forEach(p => {
      if (!participantsByConversation.has(p.conversation_id)) {
        participantsByConversation.set(p.conversation_id, []);
      }
      participantsByConversation.get(p.conversation_id).push(p.user);
    });

    if (!conversations || conversations.length === 0) {
      return NextResponse.json({
        conversations: [],
        count: 0,
        page,
        limit
      });
    }

    // Process conversations to include unread count and other participant info
    const processedConversations = conversations.map(conversation => {
      // Get the other participants for this conversation
      const otherParticipants = participantsByConversation.get(conversation.id) || [];

      // Calculate unread count
      let unreadCount = 0;
      if (conversation.last_message_at) {
        const lastReadAt = lastReadMap.get(conversation.id);
        if (!lastReadAt || new Date(lastReadAt) < new Date(conversation.last_message_at)) {
          unreadCount = 1; // We'll just indicate there are unread messages, not the exact count
        }
      }

      // Since we don't have initiator_id, we'll determine recipient status differently
      // Check if the conversation was created for this user (they're the recipient)
      // For now, we'll set all to false since we can't determine this accurately
      const isRecipient = false;

      return {
        id: conversation.id,
        last_message_preview: conversation.last_message_preview,
        last_message_at: conversation.last_message_at,
        created_at: conversation.created_at,
        updated_at: conversation.updated_at,
        status: conversation.status || 'pending', // Ensure status is always set
        other_participants: otherParticipants,
        unread_count: unreadCount,
        is_recipient: isRecipient
      };
    });

    return NextResponse.json({
      conversations: processedConversations || [],
      count: count || 0,
      page,
      limit
    });
  } catch (error: any) {
    console.error('Error fetching conversations:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch conversations' },
      { status: 500 }
    );
  }
}

// POST /api/conversations - Create a new conversation or get an existing one
export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerSide();

    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { user_id } = body;

    // Validate required fields
    if (!user_id) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Check if the user exists
    const { data: otherUser, error: userError } = await supabase
      .from('profiles')
      .select('id, display_name, avatar_url')
      .eq('id', user_id)
      .single();

    if (userError || !otherUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Use the admin client to bypass RLS
    const adminSupabase = createAdminClient();

    // Use the get_or_create_conversation function
    const { data: result, error: functionError } = await adminSupabase
      .rpc('get_or_create_conversation', {
        user1_id: user.id,
        user2_id: user_id
      });

    if (functionError) {
      throw functionError;
    }

    const conversationId = result;

    // Get the conversation details
    const { data: conversation, error: conversationError } = await adminSupabase
      .from('conversations')
      .select(`
        *,
        participants:conversation_participants(
          user_id,
          last_read_at,
          user:profiles(
            id,
            display_name,
            avatar_url
          )
        )
      `)
      .eq('id', conversationId)
      .single();

    if (conversationError) {
      throw conversationError;
    }

    // Process the conversation to include other participant info
    const otherParticipants = conversation.participants
      .filter((p: any) => p.user_id !== user.id)
      .map((p: any) => p.user);

    // When creating a new conversation, the current user is always the initiator, not the recipient
    // This is hardcoded since we don't have an initiator_id field
    const isRecipient = false;

    const processedConversation = {
      id: conversation.id,
      last_message_preview: conversation.last_message_preview,
      last_message_at: conversation.last_message_at,
      created_at: conversation.created_at,
      updated_at: conversation.updated_at,
      status: conversation.status || 'pending', // Ensure status is always set
      other_participants: otherParticipants,
      is_recipient: isRecipient
    };

    console.log('Processed conversation:', processedConversation);

    return NextResponse.json({
      conversation: processedConversation
    });
  } catch (error: any) {
    console.error('Error creating conversation:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to create conversation' },
      { status: 500 }
    );
  }
}
