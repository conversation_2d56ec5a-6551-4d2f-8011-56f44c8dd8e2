# Favicon Setup for Skilltrade

This document provides instructions for setting up and generating favicons for the Skilltrade platform.

## Favicon Files

The following favicon files have been set up:

- `app/favicon.ico` - The main favicon file (32x32)
- `public/favicon-16x16.png` - 16x16 PNG favicon
- `public/favicon-32x32.png` - 32x32 PNG favicon
- `public/apple-touch-icon.png` - 180x180 Apple Touch Icon
- `public/android-chrome-192x192.png` - 192x192 Android Chrome Icon
- `public/android-chrome-512x512.png` - 512x512 Android Chrome Icon
- `public/site.webmanifest` - Web App Manifest file

## Generating Favicons

To generate the actual favicon files from the logo:

1. Install the required dependency:
   ```bash
   npm install sharp
   ```

2. Run the favicon generation script:
   ```bash
   node scripts/generate-favicons.js
   ```

This script will:
- Take the `public/logo.png` file as the source
- Generate all the necessary favicon files in different sizes
- Update the `app/favicon.ico` file

## Manual Favicon Creation (Alternative)

If you prefer to create the favicons manually:

1. Use an online favicon generator like [favicon.io](https://favicon.io/) or [realfavicongenerator.net](https://realfavicongenerator.net/)
2. Upload your `logo.png` file
3. Download the generated favicon package
4. Replace the placeholder files in the `public` directory with the generated files

## Favicon Configuration

The favicons are already configured in the `app/layout.tsx` file using Next.js metadata API. The configuration includes:

- Standard favicon links
- Apple Touch Icon
- Web App Manifest
- OpenGraph and Twitter card images

## Verifying Favicons

To verify that the favicons are working correctly:

1. Run the development server: `npm run dev`
2. Open the website in different browsers
3. Check if the favicon appears in the browser tab
4. Test on mobile devices to verify the Apple Touch Icon and Android Chrome Icon

## Troubleshooting

If the favicons are not appearing:

1. Clear your browser cache
2. Verify that the favicon files exist in the correct locations
3. Check the browser console for any 404 errors related to favicon files
4. Ensure the paths in `app/layout.tsx` are correct
