-- Migration for enhancing user profiles with additional features
-- Run this in the Supabase SQL Editor

-- 1. Add new columns to profiles table
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS teaching_style TEXT,
ADD COLUMN IF NOT EXISTS learning_goals TEXT[];

-- 2. Create skill_taxonomies table for structured skill categorization
CREATE TABLE IF NOT EXISTS skill_taxonomies (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  parent_id UUID REFERENCES skill_taxonomies(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_skill_taxonomies_parent_id ON skill_taxonomies(parent_id);

-- Set up Row Level Security (RLS)
ALTER TABLE skill_taxonomies ENABLE ROW LEVEL SECURITY;

-- Skill taxonomies are viewable by everyone
CREATE POLICY "Skill taxonomies are viewable by everyone" ON skill_taxonomies
  FOR SELECT USING (true);

-- Only admins can modify skill taxonomies
CREATE POLICY "Admins can insert skill taxonomies" ON skill_taxonomies
  FOR INSERT WITH CHECK (auth.is_admin());

CREATE POLICY "Admins can update skill taxonomies" ON skill_taxonomies
  FOR UPDATE USING (auth.is_admin());

CREATE POLICY "Admins can delete skill taxonomies" ON skill_taxonomies
  FOR DELETE USING (auth.is_admin());

-- 3. Create user_skill_levels table for user proficiency in skills
CREATE TABLE IF NOT EXISTS user_skill_levels (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  taxonomy_id UUID NOT NULL REFERENCES skill_taxonomies(id) ON DELETE CASCADE,
  proficiency_level INTEGER NOT NULL CHECK (proficiency_level BETWEEN 1 AND 5),
  description TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  
  -- Ensure each user can only have one proficiency level per taxonomy
  CONSTRAINT unique_user_taxonomy UNIQUE (user_id, taxonomy_id)
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_skill_levels_user_id ON user_skill_levels(user_id);
CREATE INDEX IF NOT EXISTS idx_user_skill_levels_taxonomy_id ON user_skill_levels(taxonomy_id);

-- Set up Row Level Security (RLS)
ALTER TABLE user_skill_levels ENABLE ROW LEVEL SECURITY;

-- User skill levels are viewable by everyone
CREATE POLICY "User skill levels are viewable by everyone" ON user_skill_levels
  FOR SELECT USING (true);

-- Users can insert their own skill levels
CREATE POLICY "Users can insert their own skill levels" ON user_skill_levels
  FOR INSERT WITH CHECK ((select auth.uid()) = user_id);

-- Users can update their own skill levels
CREATE POLICY "Users can update their own skill levels" ON user_skill_levels
  FOR UPDATE USING ((select auth.uid()) = user_id);

-- Users can delete their own skill levels
CREATE POLICY "Users can delete their own skill levels" ON user_skill_levels
  FOR DELETE USING ((select auth.uid()) = user_id);

-- 4. Create portfolio_items table for user work samples
CREATE TABLE IF NOT EXISTS portfolio_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  image_url TEXT,
  link_url TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_portfolio_items_user_id ON portfolio_items(user_id);

-- Set up Row Level Security (RLS)
ALTER TABLE portfolio_items ENABLE ROW LEVEL SECURITY;

-- Portfolio items are viewable by everyone
CREATE POLICY "Portfolio items are viewable by everyone" ON portfolio_items
  FOR SELECT USING (true);

-- Users can insert their own portfolio items
CREATE POLICY "Users can insert their own portfolio items" ON portfolio_items
  FOR INSERT WITH CHECK ((select auth.uid()) = user_id);

-- Users can update their own portfolio items
CREATE POLICY "Users can update their own portfolio items" ON portfolio_items
  FOR UPDATE USING ((select auth.uid()) = user_id);

-- Users can delete their own portfolio items
CREATE POLICY "Users can delete their own portfolio items" ON portfolio_items
  FOR DELETE USING ((select auth.uid()) = user_id);

-- 5. Create user_availability table for teaching availability
CREATE TABLE IF NOT EXISTS user_availability (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  day_of_week INTEGER NOT NULL CHECK (day_of_week BETWEEN 0 AND 6),
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  is_recurring BOOLEAN NOT NULL DEFAULT true,
  specific_date DATE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  
  -- Ensure end_time is after start_time
  CONSTRAINT valid_time_range CHECK (end_time > start_time)
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_availability_user_id ON user_availability(user_id);
CREATE INDEX IF NOT EXISTS idx_user_availability_day_of_week ON user_availability(day_of_week);
CREATE INDEX IF NOT EXISTS idx_user_availability_specific_date ON user_availability(specific_date);

-- Set up Row Level Security (RLS)
ALTER TABLE user_availability ENABLE ROW LEVEL SECURITY;

-- User availability is viewable by everyone
CREATE POLICY "User availability is viewable by everyone" ON user_availability
  FOR SELECT USING (true);

-- Users can insert their own availability
CREATE POLICY "Users can insert their own availability" ON user_availability
  FOR INSERT WITH CHECK ((select auth.uid()) = user_id);

-- Users can update their own availability
CREATE POLICY "Users can update their own availability" ON user_availability
  FOR UPDATE USING ((select auth.uid()) = user_id);

-- Users can delete their own availability
CREATE POLICY "Users can delete their own availability" ON user_availability
  FOR DELETE USING ((select auth.uid()) = user_id);

-- 6. Create function to update the updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add triggers to update the updated_at column
DROP TRIGGER IF EXISTS update_skill_taxonomies_updated_at ON skill_taxonomies;
CREATE TRIGGER update_skill_taxonomies_updated_at
BEFORE UPDATE ON skill_taxonomies
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_user_skill_levels_updated_at ON user_skill_levels;
CREATE TRIGGER update_user_skill_levels_updated_at
BEFORE UPDATE ON user_skill_levels
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_portfolio_items_updated_at ON portfolio_items;
CREATE TRIGGER update_portfolio_items_updated_at
BEFORE UPDATE ON portfolio_items
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_user_availability_updated_at ON user_availability;
CREATE TRIGGER update_user_availability_updated_at
BEFORE UPDATE ON user_availability
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- 7. Update admin_reset_database function to include new tables
CREATE OR REPLACE FUNCTION admin_reset_database()
RETURNS VOID AS $$
BEGIN
  -- Check if the user is an admin
  IF NOT check_is_admin() THEN
    RAISE EXCEPTION 'Only administrators can reset the database';
  END IF;

  -- Disable triggers temporarily to avoid foreign key constraint issues
  SET session_replication_role = 'replica';
  
  -- Truncate tables in the correct order to avoid foreign key constraint issues
  -- Start with dependent tables and work backwards to base tables
  
  -- Clear new tables
  TRUNCATE TABLE user_availability CASCADE;
  TRUNCATE TABLE portfolio_items CASCADE;
  TRUNCATE TABLE user_skill_levels CASCADE;
  TRUNCATE TABLE skill_taxonomies CASCADE;
  
  -- Clear existing tables
  TRUNCATE TABLE reviews CASCADE;
  TRUNCATE TABLE session_messages CASCADE;
  TRUNCATE TABLE notifications CASCADE;
  TRUNCATE TABLE dispute_resolutions CASCADE;
  TRUNCATE TABLE ledger CASCADE;
  TRUNCATE TABLE skill_available_dates CASCADE;
  TRUNCATE TABLE sessions CASCADE;
  TRUNCATE TABLE skills CASCADE;
  
  -- Re-enable triggers
  SET session_replication_role = 'origin';
  
  RAISE NOTICE 'Database reset complete';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. Update admin_disable_rls function to include new tables
CREATE OR REPLACE FUNCTION admin_disable_rls()
RETURNS VOID AS $$
BEGIN
  -- Check if the user is an admin
  IF NOT check_is_admin() THEN
    RAISE EXCEPTION 'Only administrators can disable RLS';
  END IF;
  
  -- Disable RLS for all tables
  ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;
  ALTER TABLE skills DISABLE ROW LEVEL SECURITY;
  ALTER TABLE sessions DISABLE ROW LEVEL SECURITY;
  ALTER TABLE reviews DISABLE ROW LEVEL SECURITY;
  ALTER TABLE skill_available_dates DISABLE ROW LEVEL SECURITY;
  ALTER TABLE ledger DISABLE ROW LEVEL SECURITY;
  ALTER TABLE notifications DISABLE ROW LEVEL SECURITY;
  ALTER TABLE session_messages DISABLE ROW LEVEL SECURITY;
  ALTER TABLE dispute_resolutions DISABLE ROW LEVEL SECURITY;
  
  -- Disable RLS for new tables
  ALTER TABLE skill_taxonomies DISABLE ROW LEVEL SECURITY;
  ALTER TABLE user_skill_levels DISABLE ROW LEVEL SECURITY;
  ALTER TABLE portfolio_items DISABLE ROW LEVEL SECURITY;
  ALTER TABLE user_availability DISABLE ROW LEVEL SECURITY;
  
  RAISE NOTICE 'Row level security disabled';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 9. Update admin_enable_rls function to include new tables
CREATE OR REPLACE FUNCTION admin_enable_rls()
RETURNS VOID AS $$
BEGIN
  -- Check if the user is an admin
  IF NOT check_is_admin() THEN
    RAISE EXCEPTION 'Only administrators can enable RLS';
  END IF;
  
  -- Re-enable RLS for all tables
  ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
  ALTER TABLE skills ENABLE ROW LEVEL SECURITY;
  ALTER TABLE sessions ENABLE ROW LEVEL SECURITY;
  ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;
  ALTER TABLE skill_available_dates ENABLE ROW LEVEL SECURITY;
  ALTER TABLE ledger ENABLE ROW LEVEL SECURITY;
  ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
  ALTER TABLE session_messages ENABLE ROW LEVEL SECURITY;
  ALTER TABLE dispute_resolutions ENABLE ROW LEVEL SECURITY;
  
  -- Enable RLS for new tables
  ALTER TABLE skill_taxonomies ENABLE ROW LEVEL SECURITY;
  ALTER TABLE user_skill_levels ENABLE ROW LEVEL SECURITY;
  ALTER TABLE portfolio_items ENABLE ROW LEVEL SECURITY;
  ALTER TABLE user_availability ENABLE ROW LEVEL SECURITY;
  
  RAISE NOTICE 'Row level security enabled';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
