'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Save, ArrowLeft } from 'lucide-react';
import AdminPageHeader from '@/components/admin/AdminPageHeader';
import { createClientSide } from '@/lib/supabase';
import Link from 'next/link';

interface User {
  id: string;
  display_name: string | null;
  email: string;
  credit_balance: number;
}

export default function AdminAdjustCreditsPage() {
  const router = useRouter();
  const supabase = createClientSide();
  
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  const [formData, setFormData] = useState({
    userId: '',
    hours: 0,
    reason: '',
  });
  
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  
  // Fetch users for the dropdown
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setLoading(true);
        
        const { data, error } = await supabase
          .from('profiles')
          .select('id, display_name, email, credit_balance')
          .order('display_name', { ascending: true });
        
        if (error) {
          throw error;
        }
        
        setUsers(data || []);
      } catch (error: any) {
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };
    
    fetchUsers();
  }, [supabase]);
  
  // Update selected user when userId changes
  useEffect(() => {
    if (formData.userId) {
      const user = users.find(u => u.id === formData.userId);
      setSelectedUser(user || null);
    } else {
      setSelectedUser(null);
    }
  }, [formData.userId, users]);
  
  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: name === 'hours' ? parseFloat(value) : value,
    });
  };
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setSaving(true);
      setError(null);
      setSuccess(null);
      
      // Validate form
      if (!formData.userId) {
        throw new Error('Please select a user');
      }
      
      if (formData.hours === 0) {
        throw new Error('Hours adjustment cannot be zero');
      }
      
      if (!formData.reason) {
        throw new Error('Please provide a reason for the adjustment');
      }
      
      const response = await fetch('/api/admin/credits/adjust', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: formData.userId,
          hours: formData.hours,
          reason: formData.reason,
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to adjust credits');
      }
      
      const data = await response.json();
      
      // Update the selected user's credit balance
      if (selectedUser) {
        setSelectedUser({
          ...selectedUser,
          credit_balance: data.profile.credit_balance,
        });
      }
      
      // Show success message
      setSuccess(`Credits adjusted successfully. New balance: ${data.profile.credit_balance}`);
      
      // Reset form except for userId
      setFormData({
        userId: formData.userId,
        hours: 0,
        reason: '',
      });
    } catch (error: any) {
      setError(error.message);
    } finally {
      setSaving(false);
    }
  };
  
  return (
    <div>
      <AdminPageHeader
        title="Adjust Credits"
        description="Manually adjust a user's credit balance"
        backHref="/admin/credits"
      />
      
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6">
        {error && (
          <div className="bg-red-100 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 px-4 py-3 rounded-lg mb-6">
            {error}
          </div>
        )}
        
        {success && (
          <div className="bg-green-100 dark:bg-green-900/30 border border-green-200 dark:border-green-800 text-green-800 dark:text-green-300 px-4 py-3 rounded-lg mb-6">
            {success}
          </div>
        )}
        
        <form onSubmit={handleSubmit}>
          <div className="space-y-6">
            <div>
              <label htmlFor="userId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                User <span className="text-red-500">*</span>
              </label>
              <select
                id="userId"
                name="userId"
                value={formData.userId}
                onChange={handleInputChange}
                required
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="">Select a user</option>
                {users.map((user) => (
                  <option key={user.id} value={user.id}>
                    {user.display_name || user.email} (Current balance: {user.credit_balance})
                  </option>
                ))}
              </select>
            </div>
            
            {selectedUser && (
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  Selected User
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Name</p>
                    <p className="text-gray-900 dark:text-white">
                      {selectedUser.display_name || 'Unnamed User'}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Email</p>
                    <p className="text-gray-900 dark:text-white">{selectedUser.email}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Current Balance</p>
                    <p className="text-gray-900 dark:text-white font-bold">{selectedUser.credit_balance}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400">New Balance (after adjustment)</p>
                    <p className={`font-bold ${
                      selectedUser.credit_balance + formData.hours >= 0
                        ? 'text-green-600 dark:text-green-400'
                        : 'text-red-600 dark:text-red-400'
                    }`}>
                      {selectedUser.credit_balance + formData.hours}
                    </p>
                  </div>
                </div>
              </div>
            )}
            
            <div>
              <label htmlFor="hours" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Hours Adjustment <span className="text-red-500">*</span>
              </label>
              <div className="flex items-center">
                <input
                  type="number"
                  id="hours"
                  name="hours"
                  value={formData.hours}
                  onChange={handleInputChange}
                  step="0.5"
                  required
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
                <div className="ml-4 flex space-x-2">
                  <button
                    type="button"
                    onClick={() => setFormData({ ...formData, hours: formData.hours + 1 })}
                    className="px-3 py-2 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-500"
                  >
                    +1
                  </button>
                  <button
                    type="button"
                    onClick={() => setFormData({ ...formData, hours: formData.hours - 1 })}
                    className="px-3 py-2 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-500"
                  >
                    -1
                  </button>
                </div>
              </div>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Use positive values to add credits, negative values to subtract credits.
              </p>
            </div>
            
            <div>
              <label htmlFor="reason" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Reason <span className="text-red-500">*</span>
              </label>
              <textarea
                id="reason"
                name="reason"
                value={formData.reason}
                onChange={handleInputChange}
                rows={3}
                required
                placeholder="Explain why you're adjusting this user's credits"
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>
            
            <div className="flex justify-end space-x-3">
              <Link
                href="/admin/credits"
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Cancel
              </Link>
              <button
                type="submit"
                disabled={saving}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {saving ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    Adjusting...
                  </>
                ) : (
                  <>
                    <Save className="h-5 w-5 mr-2" />
                    Adjust Credits
                  </>
                )}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}
