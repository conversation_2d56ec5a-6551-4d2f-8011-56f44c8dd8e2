import { NextRequest, NextResponse } from 'next/server';
import { createAdminClient } from '@/lib/supabase-admin';
import { isAdmin } from '@/lib/admin-utils';

export const dynamic = 'force-dynamic';

// GET /api/admin/analytics - Get platform analytics
export async function GET(request: NextRequest) {
  try {
    // Check if the user is an admin
    const isUserAdmin = await isAdmin();
    if (!isUserAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    const supabase = createAdminClient();

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const period = searchParams.get('period') || '30days'; // 7days, 30days, 90days, 1year, all

    // Calculate date range based on period
    const now = new Date();
    let startDate: Date;

    switch (period) {
      case '7days':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30days':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90days':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      case '1year':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
      case 'all':
      default:
        startDate = new Date(0); // Beginning of time
        break;
    }

    const startDateStr = startDate.toISOString();

    // Get total users
    const { count: totalUsers, error: usersError } = await supabase
      .from('profiles')
      .select('*', { count: 'exact', head: true });

    if (usersError) {
      throw usersError;
    }

    // Get new users in period
    const { count: newUsers, error: newUsersError } = await supabase
      .from('profiles')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', startDateStr);

    if (newUsersError) {
      throw newUsersError;
    }

    // Get total skills
    const { count: totalSkills, error: skillsError } = await supabase
      .from('skills')
      .select('*', { count: 'exact', head: true });

    if (skillsError) {
      throw skillsError;
    }

    // Get new skills in period
    const { count: newSkills, error: newSkillsError } = await supabase
      .from('skills')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', startDateStr);

    if (newSkillsError) {
      throw newSkillsError;
    }

    // Get total sessions
    const { count: totalSessions, error: sessionsError } = await supabase
      .from('sessions')
      .select('*', { count: 'exact', head: true });

    if (sessionsError) {
      throw sessionsError;
    }

    // Get new sessions in period
    const { count: newSessions, error: newSessionsError } = await supabase
      .from('sessions')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', startDateStr);

    if (newSessionsError) {
      throw newSessionsError;
    }

    // Get sessions by status
    const { data: sessionsByStatus, error: statusError } = await supabase
      .from('sessions')
      .select('status')
      .gte('created_at', startDateStr);

    if (statusError) {
      throw statusError;
    }

    const statusCounts = sessionsByStatus?.reduce((acc, session) => {
      const status = session.status;
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>) || {};

    // Get popular skills (most booked)
    const { data: popularSkills, error: popularSkillsError } = await supabase
      .from('sessions')
      .select(`
        skill_id,
        skill:skills(id, title)
      `)
      .gte('created_at', startDateStr)
      .order('created_at', { ascending: false });

    if (popularSkillsError) {
      throw popularSkillsError;
    }

    const skillCounts = popularSkills?.reduce((acc, session) => {
      const skillId = session.skill_id;
      // Handle the case where skill might be an array or an object or undefined
      let skillTitle = 'Unknown Skill';
      if (Array.isArray(session.skill) && session.skill.length > 0 && session.skill[0].title) {
        skillTitle = String(session.skill[0].title);
      } else if (session.skill && typeof session.skill === 'object' && 'title' in session.skill) {
        // Use type assertion to tell TypeScript that title is a string
        skillTitle = String((session.skill as { title: string }).title);
      }

      if (!acc[skillId]) {
        acc[skillId] = {
          id: skillId,
          title: skillTitle,
          count: 0,
        };
      }

      acc[skillId].count += 1;
      return acc;
    }, {} as Record<string, { id: string; title: string; count: number }>) || {};

    const topSkills = Object.values(skillCounts)
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Get active users (users with at least one session)
    const { data: activeUserData, error: activeUserError } = await supabase
      .from('sessions')
      .select('teacher_id, learner_id')
      .gte('created_at', startDateStr);

    if (activeUserError) {
      throw activeUserError;
    }

    const activeUserIds = new Set();
    activeUserData?.forEach(session => {
      activeUserIds.add(session.teacher_id);
      activeUserIds.add(session.learner_id);
    });

    // Get user growth over time
    const { data: userGrowthData, error: userGrowthError } = await supabase
      .from('profiles')
      .select('created_at')
      .order('created_at', { ascending: true });

    if (userGrowthError) {
      throw userGrowthError;
    }

    // Group users by month
    const userGrowthByMonth: Record<string, number> = {};
    let cumulativeUsers = 0;

    userGrowthData?.forEach(user => {
      const date = new Date(user.created_at);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

      if (!userGrowthByMonth[monthKey]) {
        userGrowthByMonth[monthKey] = 0;
      }

      userGrowthByMonth[monthKey] += 1;
      cumulativeUsers += 1;
    });

    // Convert to array for charting
    const userGrowth = Object.entries(userGrowthByMonth).map(([month, count]) => ({
      month,
      count,
    }));

    // Get session growth over time
    const { data: sessionGrowthData, error: sessionGrowthError } = await supabase
      .from('sessions')
      .select('created_at')
      .order('created_at', { ascending: true });

    if (sessionGrowthError) {
      throw sessionGrowthError;
    }

    // Group sessions by month
    const sessionGrowthByMonth: Record<string, number> = {};

    sessionGrowthData?.forEach(session => {
      const date = new Date(session.created_at);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

      if (!sessionGrowthByMonth[monthKey]) {
        sessionGrowthByMonth[monthKey] = 0;
      }

      sessionGrowthByMonth[monthKey] += 1;
    });

    // Convert to array for charting
    const sessionGrowth = Object.entries(sessionGrowthByMonth).map(([month, count]) => ({
      month,
      count,
    }));

    // Get total credits in the system
    const { data: creditData, error: creditError } = await supabase
      .from('profiles')
      .select('credit_balance');

    if (creditError) {
      throw creditError;
    }

    const totalCredits = creditData?.reduce((sum, profile) => sum + profile.credit_balance, 0) || 0;

    return NextResponse.json({
      period,
      users: {
        total: totalUsers || 0,
        new: newUsers || 0,
        active: activeUserIds.size,
        growth: userGrowth,
      },
      skills: {
        total: totalSkills || 0,
        new: newSkills || 0,
        popular: topSkills,
      },
      sessions: {
        total: totalSessions || 0,
        new: newSessions || 0,
        byStatus: statusCounts,
        growth: sessionGrowth,
      },
      credits: {
        total: totalCredits,
      },
    });
  } catch (error: any) {
    console.error('Error fetching analytics:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch analytics' },
      { status: 500 }
    );
  }
}
