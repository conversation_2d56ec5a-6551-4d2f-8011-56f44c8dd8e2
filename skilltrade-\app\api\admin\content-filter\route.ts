import { NextRequest, NextResponse } from 'next/server';
import { createServerSide } from '@/lib/supabase-server';
import { createAdminClient } from '@/lib/supabase-admin';
import { isAdmin } from '@/lib/admin-utils';

export const dynamic = 'force-dynamic';

// GET /api/admin/content-filter - Get all bad words
export async function GET(request: NextRequest) {
  try {
    console.log('GET /api/admin/content-filter - Checking admin status');

    // Check if the user is an admin
    const isUserAdmin = await isAdmin();
    if (!isUserAdmin) {
      console.log('User is not an admin');
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log('User is an admin, fetching bad words');
    const supabase = createAdminClient();

    // Get all bad words
    const { data, error } = await supabase
      .from('bad_words')
      .select('*')
      .order('severity', { ascending: false })
      .order('word');

    if (error) {
      console.error('Error fetching bad words:', error);
      return NextResponse.json(
        { error: 'Failed to fetch bad words' },
        { status: 500 }
      );
    }

    console.log(`Successfully fetched ${data.length} bad words`);
    return NextResponse.json({ badWords: data });
  } catch (error: any) {
    console.error('Error in GET /api/admin/content-filter:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}

// POST /api/admin/content-filter - Add a new bad word
export async function POST(request: NextRequest) {
  try {
    console.log('POST /api/admin/content-filter - Checking admin status');

    // Check if the user is an admin
    const isUserAdmin = await isAdmin();
    if (!isUserAdmin) {
      console.log('User is not an admin');
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { word, severity = 1 } = body;

    console.log('Adding new bad word:', { word, severity });

    if (!word || typeof word !== 'string' || word.trim() === '') {
      console.log('Invalid word provided');
      return NextResponse.json(
        { error: 'Word is required' },
        { status: 400 }
      );
    }

    const supabase = createAdminClient();

    // Add the new word
    console.log('Calling add_bad_word RPC function');
    const { data, error } = await supabase
      .rpc('add_bad_word', {
        word_text: word.toLowerCase().trim(),
        word_severity: severity
      });

    if (error) {
      console.error('Error adding bad word:', error);
      return NextResponse.json(
        { error: 'Failed to add bad word' },
        { status: 500 }
      );
    }

    console.log('Successfully added bad word, id:', data);
    return NextResponse.json({ id: data });
  } catch (error: any) {
    console.error('Error in POST /api/admin/content-filter:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}

// PATCH /api/admin/content-filter - Update a bad word
export async function PATCH(request: NextRequest) {
  try {
    // Check if the user is an admin
    const isUserAdmin = await isAdmin();
    if (!isUserAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { id, word, severity, active } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'Word ID is required' },
        { status: 400 }
      );
    }

    const supabase = createAdminClient();

    // Update the word
    const { data, error } = await supabase
      .rpc('update_bad_word', {
        word_id: id,
        word_text: word?.toLowerCase().trim(),
        word_severity: severity,
        word_active: active
      });

    if (error) {
      console.error('Error updating bad word:', error);
      return NextResponse.json(
        { error: 'Failed to update bad word' },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: data });
  } catch (error: any) {
    console.error('Error in PATCH /api/admin/content-filter:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/content-filter?id={id} - Delete a bad word
export async function DELETE(request: NextRequest) {
  try {
    // Check if the user is an admin
    const isUserAdmin = await isAdmin();
    if (!isUserAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const url = new URL(request.url);
    const id = url.searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'Word ID is required' },
        { status: 400 }
      );
    }

    const supabase = createAdminClient();

    // Delete the word
    const { data, error } = await supabase
      .rpc('delete_bad_word', {
        word_id: id
      });

    if (error) {
      console.error('Error deleting bad word:', error);
      return NextResponse.json(
        { error: 'Failed to delete bad word' },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: data });
  } catch (error: any) {
    console.error('Error in DELETE /api/admin/content-filter:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}
