'use client';

import { useState, useEffect } from 'react';
import {
  AlertCircle,
  Plus,
  Edit,
  Trash2,
  Check,
  X,
  AlertTriangle,
  Shield,
  Search
} from 'lucide-react';
import {
  getAllBadWords,
  addBadWord,
  updateBadWord,
  deleteBadWord,
  validateText,
  BadWord
} from '@/lib/bad-words-filter-pages';

export default function ContentFilterPage() {
  const [badWords, setBadWords] = useState<BadWord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [newWord, setNewWord] = useState('');
  const [newSeverity, setNewSeverity] = useState(1);
  const [editingWord, setEditingWord] = useState<BadWord | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterActive, setFilterActive] = useState(true);

  // Test filter section
  const [testText, setTestText] = useState('');
  const [testResult, setTestResult] = useState<{
    isValid: boolean;
    badWords: string[];
    censoredText: string;
  } | null>(null);
  const [testing, setTesting] = useState(false);

  // Fetch all bad words
  const fetchBadWords = async () => {
    try {
      setLoading(true);
      setError(null);

      // Try to fetch from API first
      try {
        const response = await fetch('/api/admin/content-filter');
        if (response.ok) {
          const data = await response.json();
          console.log('Fetched bad words from API:', data);
          if (data.badWords && Array.isArray(data.badWords)) {
            setBadWords(data.badWords);
            setLoading(false);
            return;
          }
        } else {
          console.error('Error fetching from API:', response.statusText);
        }
      } catch (apiError) {
        console.error('API fetch error:', apiError);
      }

      // Fallback to direct function call
      console.log('Falling back to direct function call');
      const words = await getAllBadWords();
      console.log('Fetched bad words directly:', words);
      setBadWords(words);
    } catch (err: any) {
      console.error('Error in fetchBadWords:', err);
      setError(err.message || 'Failed to fetch bad words');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBadWords();
  }, []);

  // Add a new bad word
  const handleAddWord = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newWord.trim()) {
      setError('Word cannot be empty');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Try API first
      try {
        const response = await fetch('/api/admin/content-filter', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            word: newWord.trim(),
            severity: newSeverity
          }),
        });

        if (response.ok) {
          const data = await response.json();
          console.log('Added word via API:', data);
          setNewWord('');
          setNewSeverity(1);
          await fetchBadWords();
          return;
        } else {
          console.error('Error adding word via API:', response.statusText);
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to add word via API');
        }
      } catch (apiError: any) {
        console.error('API add error:', apiError);
        // Continue to fallback
      }

      // Fallback to direct function
      console.log('Falling back to direct function call for adding word');
      const result = await addBadWord(newWord.trim(), newSeverity);

      if (result) {
        console.log('Added word via direct function:', result);
        setNewWord('');
        setNewSeverity(1);
        await fetchBadWords();
      } else {
        throw new Error('Failed to add word via direct function');
      }
    } catch (err: any) {
      console.error('Error in handleAddWord:', err);
      setError(err.message || 'Failed to add word');
    } finally {
      setLoading(false);
    }
  };

  // Update a bad word
  const handleUpdateWord = async () => {
    if (!editingWord) return;

    try {
      setLoading(true);
      setError(null);

      const result = await updateBadWord(editingWord.id!, {
        word: editingWord.word,
        severity: editingWord.severity,
        active: editingWord.active
      });

      if (result) {
        setEditingWord(null);
        await fetchBadWords();
      } else {
        setError('Failed to update word');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to update word');
    } finally {
      setLoading(false);
    }
  };

  // Delete a bad word
  const handleDeleteWord = async (id: string) => {
    if (!confirm('Are you sure you want to delete this word?')) return;

    try {
      setLoading(true);
      setError(null);

      const result = await deleteBadWord(id);

      if (result) {
        await fetchBadWords();
      } else {
        setError('Failed to delete word');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to delete word');
    } finally {
      setLoading(false);
    }
  };

  // Test the filter
  const handleTestFilter = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!testText.trim()) {
      setError('Test text cannot be empty');
      return;
    }

    try {
      setTesting(true);
      setError(null);

      // Try API first
      try {
        const response = await fetch('/api/admin/content-filter/test', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            text: testText.trim()
          }),
        });

        if (response.ok) {
          const result = await response.json();
          console.log('Test result from API:', result);
          setTestResult(result);
          return;
        } else {
          console.error('Error testing via API:', response.statusText);
          // Continue to fallback
        }
      } catch (apiError) {
        console.error('API test error:', apiError);
        // Continue to fallback
      }

      // Fallback to direct function
      console.log('Falling back to direct function call for testing');
      const result = await validateText(testText);
      console.log('Test result from direct function:', result);
      setTestResult(result);
    } catch (err: any) {
      console.error('Error in handleTestFilter:', err);
      setError(err.message || 'Failed to test filter');
    } finally {
      setTesting(false);
    }
  };

  // Filter and sort bad words
  const filteredWords = badWords
    .filter(word =>
      word.word.toLowerCase().includes(searchQuery.toLowerCase()) &&
      (filterActive ? word.active : true)
    )
    .sort((a, b) => {
      // Sort by severity (high to low) then alphabetically
      if (a.severity !== b.severity) {
        return b.severity - a.severity;
      }
      return a.word.localeCompare(b.word);
    });

  // Get severity label and color
  const getSeverityInfo = (severity: number) => {
    switch (severity) {
      case 3:
        return { label: 'High', color: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300' };
      case 2:
        return { label: 'Medium', color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300' };
      case 1:
      default:
        return { label: 'Low', color: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300' };
    }
  };

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Content Filter Management</h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          Manage the list of bad words used for content filtering across the platform.
        </p>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-100 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <div className="flex items-center text-red-800 dark:text-red-300">
            <AlertCircle size={20} className="mr-2" />
            <span>{error}</span>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Add new word form */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6">
          <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Add New Word</h2>
          <form onSubmit={handleAddWord}>
            <div className="mb-4">
              <label htmlFor="newWord" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Word
              </label>
              <input
                type="text"
                id="newWord"
                value={newWord}
                onChange={(e) => setNewWord(e.target.value)}
                className="w-full px-4 py-2 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter a word to filter"
              />
            </div>
            <div className="mb-4">
              <label htmlFor="newSeverity" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Severity
              </label>
              <select
                id="newSeverity"
                value={newSeverity}
                onChange={(e) => setNewSeverity(parseInt(e.target.value))}
                className="w-full px-4 py-2 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value={1}>Low</option>
                <option value={2}>Medium</option>
                <option value={3}>High</option>
              </select>
            </div>
            <button
              type="submit"
              disabled={loading}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Adding...' : 'Add Word'}
            </button>
          </form>
        </div>

        {/* Test filter form */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6">
          <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Test Filter</h2>
          <form onSubmit={handleTestFilter}>
            <div className="mb-4">
              <label htmlFor="testText" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Text to Test
              </label>
              <textarea
                id="testText"
                value={testText}
                onChange={(e) => setTestText(e.target.value)}
                className="w-full px-4 py-2 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter text to test the filter"
                rows={3}
              />
            </div>
            <button
              type="submit"
              disabled={testing}
              className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {testing ? 'Testing...' : 'Test Filter'}
            </button>
          </form>

          {testResult && (
            <div className="mt-4 p-4 bg-gray-100 dark:bg-gray-700 rounded-lg">
              <div className="flex items-center mb-2">
                <span className="font-medium text-gray-900 dark:text-white mr-2">Result:</span>
                {testResult.isValid ? (
                  <span className="text-green-600 dark:text-green-400 flex items-center">
                    <Check size={16} className="mr-1" /> Clean
                  </span>
                ) : (
                  <span className="text-red-600 dark:text-red-400 flex items-center">
                    <X size={16} className="mr-1" /> Contains bad words
                  </span>
                )}
              </div>
              {!testResult.isValid && (
                <>
                  <div className="mb-2">
                    <span className="font-medium text-gray-900 dark:text-white mr-2">Bad words found:</span>
                    <span className="text-red-600 dark:text-red-400">{testResult.badWords.join(', ')}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-900 dark:text-white mr-2">Censored text:</span>
                    <span className="text-gray-700 dark:text-gray-300">{testResult.censoredText}</span>
                  </div>
                </>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Bad words list */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
          <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4 sm:mb-0">Bad Words List</h2>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search words..."
                className="w-full sm:w-64 px-4 py-2 pl-10 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                id="filterActive"
                checked={filterActive}
                onChange={(e) => setFilterActive(e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="filterActive" className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                Show active only
              </label>
            </div>
          </div>
        </div>

        {loading && badWords.length === 0 ? (
          <div className="flex justify-center items-center h-32">
            <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : filteredWords.length === 0 ? (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            {searchQuery ? 'No words match your search' : 'No bad words found'}
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Word
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Severity
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {filteredWords.map((word) => (
                  <tr key={word.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    {editingWord && editingWord.id === word.id ? (
                      // Editing mode
                      <>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <input
                            type="text"
                            value={editingWord.word}
                            onChange={(e) => setEditingWord({ ...editingWord, word: e.target.value })}
                            className="w-full px-2 py-1 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <select
                            value={editingWord.severity}
                            onChange={(e) => setEditingWord({ ...editingWord, severity: parseInt(e.target.value) })}
                            className="w-full px-2 py-1 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          >
                            <option value={1}>Low</option>
                            <option value={2}>Medium</option>
                            <option value={3}>High</option>
                          </select>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <select
                            value={editingWord.active ? 'true' : 'false'}
                            onChange={(e) => setEditingWord({ ...editingWord, active: e.target.value === 'true' })}
                            className="w-full px-2 py-1 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          >
                            <option value="true">Active</option>
                            <option value="false">Inactive</option>
                          </select>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button
                            onClick={handleUpdateWord}
                            className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 mr-3"
                          >
                            <Check size={18} />
                          </button>
                          <button
                            onClick={() => setEditingWord(null)}
                            className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                          >
                            <X size={18} />
                          </button>
                        </td>
                      </>
                    ) : (
                      // View mode
                      <>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="text-gray-900 dark:text-white font-medium">{word.word}</span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getSeverityInfo(word.severity).color}`}>
                            {getSeverityInfo(word.severity).label}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            word.active
                              ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300'
                              : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                          }`}>
                            {word.active ? 'Active' : 'Inactive'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button
                            onClick={() => setEditingWord(word)}
                            className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3"
                          >
                            <Edit size={18} />
                          </button>
                          <button
                            onClick={() => handleDeleteWord(word.id!)}
                            className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                          >
                            <Trash2 size={18} />
                          </button>
                        </td>
                      </>
                    )}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Information section */}
      <div className="mt-8 bg-blue-50 dark:bg-blue-900/10 border border-blue-200 dark:border-blue-800 rounded-xl p-6">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <Shield className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div className="ml-3">
            <h3 className="text-lg font-medium text-blue-800 dark:text-blue-300">About Content Filtering</h3>
            <div className="mt-2 text-sm text-blue-700 dark:text-blue-400">
              <p className="mb-2">
                The content filter is used across the platform to filter out inappropriate language in:
              </p>
              <ul className="list-disc pl-5 space-y-1">
                <li>Skill titles and descriptions</li>
                <li>Session messages</li>
                <li>Direct messages between users</li>
                <li>User profiles and other user-generated content</li>
              </ul>
              <p className="mt-2">
                Words are filtered based on severity levels. Higher severity words are always filtered, while lower severity words may be allowed in certain contexts.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
