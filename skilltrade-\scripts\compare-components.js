// Script to compare original and polished components
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const originalDir = path.join(process.cwd(), 'components', 'profile');
const polishedDir = path.join(process.cwd(), 'components', 'profile', 'polished');
const outputDir = path.join(process.cwd(), 'comparison-reports');

// Create output directory if it doesn't exist
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Get all component files
const componentFiles = fs.readdirSync(originalDir)
  .filter(file => file.endsWith('.tsx') && file !== 'index.tsx');

// Function to compare components
function compareComponents(fileName) {
  const originalPath = path.join(originalDir, fileName);
  const polishedPath = path.join(polishedDir, fileName);
  
  if (!fs.existsSync(polishedPath)) {
    console.log(`Polished version of ${fileName} not found`);
    return null;
  }
  
  const originalContent = fs.readFileSync(originalPath, 'utf8');
  const polishedContent = fs.readFileSync(polishedPath, 'utf8');
  
  // Count lines
  const originalLines = originalContent.split('\n').length;
  const polishedLines = polishedContent.split('\n').length;
  
  // Count classes
  const originalClasses = (originalContent.match(/className="[^"]*"/g) || []).length;
  const polishedClasses = (polishedContent.match(/className="[^"]*"/g) || []).length;
  
  // Count animations
  const originalAnimations = (originalContent.match(/animate-|transition-|duration-|ease-/g) || []).length;
  const polishedAnimations = (polishedContent.match(/animate-|transition-|duration-|ease-/g) || []).length;
  
  // Count hover effects
  const originalHovers = (originalContent.match(/hover:/g) || []).length;
  const polishedHovers = (polishedContent.match(/hover:/g) || []).length;
  
  // Count focus effects
  const originalFocus = (originalContent.match(/focus:/g) || []).length;
  const polishedFocus = (polishedContent.match(/focus:/g) || []).length;
  
  // Count dark mode classes
  const originalDarkMode = (originalContent.match(/dark:/g) || []).length;
  const polishedDarkMode = (polishedContent.match(/dark:/g) || []).length;
  
  // Count responsive classes
  const originalResponsive = (originalContent.match(/sm:|md:|lg:|xl:/g) || []).length;
  const polishedResponsive = (polishedContent.match(/sm:|md:|lg:|xl:/g) || []).length;
  
  // Count error handling
  const originalErrorHandling = (originalContent.match(/catch \(error/g) || []).length;
  const polishedErrorHandling = (polishedContent.match(/catch \(error/g) || []).length;
  
  // Count loading states
  const originalLoading = (originalContent.match(/loading|isSaving/g) || []).length;
  const polishedLoading = (polishedContent.match(/loading|isSaving/g) || []).length;
  
  return {
    fileName,
    originalLines,
    polishedLines,
    linesDiff: polishedLines - originalLines,
    originalClasses,
    polishedClasses,
    classesDiff: polishedClasses - originalClasses,
    originalAnimations,
    polishedAnimations,
    animationsDiff: polishedAnimations - originalAnimations,
    originalHovers,
    polishedHovers,
    hoversDiff: polishedHovers - originalHovers,
    originalFocus,
    polishedFocus,
    focusDiff: polishedFocus - originalFocus,
    originalDarkMode,
    polishedDarkMode,
    darkModeDiff: polishedDarkMode - originalDarkMode,
    originalResponsive,
    polishedResponsive,
    responsiveDiff: polishedResponsive - originalResponsive,
    originalErrorHandling,
    polishedErrorHandling,
    errorHandlingDiff: polishedErrorHandling - originalErrorHandling,
    originalLoading,
    polishedLoading,
    loadingDiff: polishedLoading - originalLoading,
  };
}

// Function to generate a report
function generateReport(comparisons) {
  let report = '# Component Comparison Report\n\n';
  
  report += '## Summary\n\n';
  report += '| Component | Lines | Classes | Animations | Hover Effects | Focus Effects | Dark Mode | Responsive | Error Handling | Loading States |\n';
  report += '|-----------|-------|---------|------------|---------------|---------------|-----------|------------|----------------|---------------|\n';
  
  let totalLinesDiff = 0;
  let totalClassesDiff = 0;
  let totalAnimationsDiff = 0;
  let totalHoversDiff = 0;
  let totalFocusDiff = 0;
  let totalDarkModeDiff = 0;
  let totalResponsiveDiff = 0;
  let totalErrorHandlingDiff = 0;
  let totalLoadingDiff = 0;
  
  comparisons.forEach(comparison => {
    report += `| ${comparison.fileName} | ${comparison.originalLines} → ${comparison.polishedLines} (${comparison.linesDiff > 0 ? '+' : ''}${comparison.linesDiff}) | ${comparison.originalClasses} → ${comparison.polishedClasses} (${comparison.classesDiff > 0 ? '+' : ''}${comparison.classesDiff}) | ${comparison.originalAnimations} → ${comparison.polishedAnimations} (${comparison.animationsDiff > 0 ? '+' : ''}${comparison.animationsDiff}) | ${comparison.originalHovers} → ${comparison.polishedHovers} (${comparison.hoversDiff > 0 ? '+' : ''}${comparison.hoversDiff}) | ${comparison.originalFocus} → ${comparison.polishedFocus} (${comparison.focusDiff > 0 ? '+' : ''}${comparison.focusDiff}) | ${comparison.originalDarkMode} → ${comparison.polishedDarkMode} (${comparison.darkModeDiff > 0 ? '+' : ''}${comparison.darkModeDiff}) | ${comparison.originalResponsive} → ${comparison.polishedResponsive} (${comparison.responsiveDiff > 0 ? '+' : ''}${comparison.responsiveDiff}) | ${comparison.originalErrorHandling} → ${comparison.polishedErrorHandling} (${comparison.errorHandlingDiff > 0 ? '+' : ''}${comparison.errorHandlingDiff}) | ${comparison.originalLoading} → ${comparison.polishedLoading} (${comparison.loadingDiff > 0 ? '+' : ''}${comparison.loadingDiff}) |\n`;
    
    totalLinesDiff += comparison.linesDiff;
    totalClassesDiff += comparison.classesDiff;
    totalAnimationsDiff += comparison.animationsDiff;
    totalHoversDiff += comparison.hoversDiff;
    totalFocusDiff += comparison.focusDiff;
    totalDarkModeDiff += comparison.darkModeDiff;
    totalResponsiveDiff += comparison.responsiveDiff;
    totalErrorHandlingDiff += comparison.errorHandlingDiff;
    totalLoadingDiff += comparison.loadingDiff;
  });
  
  report += `| **Total** | **${totalLinesDiff > 0 ? '+' : ''}${totalLinesDiff}** | **${totalClassesDiff > 0 ? '+' : ''}${totalClassesDiff}** | **${totalAnimationsDiff > 0 ? '+' : ''}${totalAnimationsDiff}** | **${totalHoversDiff > 0 ? '+' : ''}${totalHoversDiff}** | **${totalFocusDiff > 0 ? '+' : ''}${totalFocusDiff}** | **${totalDarkModeDiff > 0 ? '+' : ''}${totalDarkModeDiff}** | **${totalResponsiveDiff > 0 ? '+' : ''}${totalResponsiveDiff}** | **${totalErrorHandlingDiff > 0 ? '+' : ''}${totalErrorHandlingDiff}** | **${totalLoadingDiff > 0 ? '+' : ''}${totalLoadingDiff}** |\n\n`;
  
  report += '## Improvements\n\n';
  report += '### Animations\n';
  report += '- Added fade-in animations for smooth component rendering\n';
  report += '- Added hover animations for interactive elements\n';
  report += '- Added loading animations for better user feedback\n';
  report += '- Added transition effects for smoother state changes\n\n';
  
  report += '### Dark Mode\n';
  report += '- Improved dark mode color consistency\n';
  report += '- Added smooth transitions between light and dark modes\n';
  report += '- Ensured proper contrast in dark mode\n\n';
  
  report += '### Responsive Design\n';
  report += '- Improved mobile layout with proper spacing\n';
  report += '- Added responsive grid layouts\n';
  report += '- Optimized touch targets for mobile devices\n\n';
  
  report += '### Error Handling\n';
  report += '- Added proper error states for all components\n';
  report += '- Improved error messages for better user understanding\n';
  report += '- Added retry functionality for failed operations\n\n';
  
  report += '### Loading States\n';
  report += '- Added loading skeletons for better user experience\n';
  report += '- Improved loading indicators for actions\n';
  report += '- Added disabled states during loading\n\n';
  
  report += '## Next Steps\n\n';
  report += '1. Review and test the polished components\n';
  report += '2. Implement the polished components in the application\n';
  report += '3. Gather user feedback on the improved UI\n';
  report += '4. Make further refinements based on feedback\n';
  
  return report;
}

// Main function
function main() {
  console.log('Comparing original and polished components...');
  
  const comparisons = componentFiles.map(file => compareComponents(file))
    .filter(comparison => comparison !== null);
  
  const report = generateReport(comparisons);
  
  const reportPath = path.join(outputDir, 'comparison-report.md');
  fs.writeFileSync(reportPath, report);
  
  console.log(`Comparison report written to ${reportPath}`);
}

main();
