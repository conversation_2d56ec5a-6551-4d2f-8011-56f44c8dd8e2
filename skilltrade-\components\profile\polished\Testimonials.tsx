'use client';

import { useState } from 'react';
import Image from 'next/image';
import { Star } from 'lucide-react';

interface Reviewer {
  id: string;
  displayName: string | null;
  avatarUrl: string | null;
}

interface Skill {
  id: string;
  title: string;
}

interface Review {
  id: string;
  sessionId: string;
  reviewerId: string;
  rating: number;
  comment: string | null;
  createdAt: string;
  reviewer: Reviewer;
  skill: Skill;
}

interface TestimonialsProps {
  reviews: Review[];
  minRating?: number;
}

// Added animation wrapper
const AnimatedComponent = ({ children }: { children: React.ReactNode }) => (
  <div className="animate-fadeIn">
    {children}
  </div>
);

export default function Testimonials({
  reviews = [],
  minRating = 4,
}: TestimonialsProps) {
  const [filterRating, setFilterRating] = useState(minRating);

  // Filter reviews by rating
  const filteredReviews = reviews.filter(review => review.rating >= filterRating);

  // Sort reviews by rating (highest first) and then by date (newest first)
  const sortedReviews = [...filteredReviews].sort((a, b) => {
    if (b.rating !== a.rating) {
      return b.rating - a.rating;
    }
    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
  });

  const renderStars = (rating: number) => {
    return (
      <div className="flex">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-4 w-4 ${
              star <= rating
                ? 'text-yellow-400 fill-yellow-400'
                : 'text-gray-300 dark:text-gray-600'
            }`}
          />
        ))}
      </div>
    );
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <AnimatedComponent>
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm transition-colors duration-200">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Testimonials</h3>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600 dark:text-gray-400">Filter:</span>
            <select
              value={filterRating}
              onChange={(e) => setFilterRating(Number(e.target.value))}
              className="px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value={0}>All Reviews</option>
              <option value={3}>3+ Stars</option>
              <option value={4}>4+ Stars</option>
              <option value={5}>5 Stars Only</option>
            </select>
          </div>
      </div>

      {sortedReviews.length > 0 ? (
        <div className="space-y-4">
          {sortedReviews.map((review) => (
            <div
              key={review.id}
              className="bg-gray-50 dark:bg-gray-700 p-6 rounded-xl"
            >
              <div className="flex justify-between items-start">
                <div className="flex items-center">
                  <div className="flex-shrink-0 mr-3">
                    {review.reviewer.avatarUrl ? (
                      <div className="relative h-10 w-10 rounded-xl overflow-hidden">
                        <Image
                          src={review.reviewer.avatarUrl}
                          alt={review.reviewer.displayName || 'Reviewer'}
                          fill
                          className="object-cover"
                        />
                      </div>
                    ) : (
                      <div className="h-10 w-10 rounded-xl bg-gray-300 dark:bg-gray-600 flex items-center justify-center transition-colors duration-200">
                        <span className="text-gray-600 dark:text-gray-300 text-sm font-medium">
                          {(review.reviewer.displayName || 'User').charAt(0).toUpperCase()}
                        </span>
                      </div>
                    )}
                  </div>
                  <div>
                    <p className="font-medium text-gray-800 dark:text-gray-200">
                      {review.reviewer.displayName || 'Anonymous User'}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {formatDate(review.createdAt)}
                    </p>
                  </div>
                </div>
                <div className="flex flex-col items-end">
                  {renderStars(review.rating)}
                  <span className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    for {review.skill.title}
                  </span>
                </div>
              </div>
              {review.comment && (
                <div className="mt-3">
                  <p className="text-gray-700 dark:text-gray-300">
                    "{review.comment}"
                  </p>
                </div>
              )}
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-8">
          {reviews.length > 0 ? (
            <p className="text-gray-500 dark:text-gray-400">
              No reviews match your filter criteria. Try adjusting the filter.
            </p>
          ) : (
            <p className="text-gray-500 dark:text-gray-400">
              No testimonials available yet.
            </p>
          )}
        </div>
      )}
    </div>
    </AnimatedComponent>
  );
}
