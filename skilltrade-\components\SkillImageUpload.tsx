'use client';

import { useState, useEffect } from 'react';
import { uploadSkillImage, deleteSkillImage, extractPathFromUrl } from '@/utils/skill-image-upload';
import { ImageIcon, X } from 'lucide-react';

interface SkillImageUploadProps {
  skillId: string;
  ownerId: string;
  url?: string | null;
  onUpload?: (url: string) => void;
  onRemove?: () => void;
}

export default function SkillImageUpload({ 
  skillId, 
  ownerId, 
  url, 
  onUpload, 
  onRemove 
}: SkillImageUploadProps) {
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (url) setImageUrl(url);
  }, [url]);

  async function handleUpload(event: React.ChangeEvent<HTMLInputElement>) {
    try {
      setUploading(true);
      setError(null);

      if (!event.target.files || event.target.files.length === 0) {
        throw new Error('You must select an image to upload.');
      }

      const file = event.target.files[0];
      const result = await uploadSkillImage(skillId, ownerId, file);

      setImageUrl(result.url);
      if (onUpload) onUpload(result.url);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Error uploading image');
      console.error('Error uploading image:', error);
    } finally {
      setUploading(false);
    }
  }

  async function handleRemove() {
    try {
      if (!imageUrl) return;
      setUploading(true);
      setError(null);

      // Extract the path from the URL
      const path = extractPathFromUrl(imageUrl);
      
      // Delete the image
      await deleteSkillImage(skillId, path);
      
      setImageUrl(null);
      if (onRemove) onRemove();
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Error removing image');
      console.error('Error removing image:', error);
    } finally {
      setUploading(false);
    }
  }

  return (
    <div className="w-full">
      {error && (
        <div className="mb-4 p-3 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 rounded-lg text-sm">
          {error}
        </div>
      )}

      {imageUrl ? (
        <div className="relative rounded-lg overflow-hidden mb-4">
          <img 
            src={imageUrl} 
            alt="Skill image" 
            className="w-full h-48 object-cover"
          />
          <button
            onClick={handleRemove}
            disabled={uploading}
            className="absolute top-2 right-2 p-1 bg-red-600 text-white rounded-full hover:bg-red-700 transition-colors"
            title="Remove image"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
      ) : (
        <label className="flex flex-col items-center justify-center w-full h-48 border-2 border-gray-300 dark:border-gray-600 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600">
          <div className="flex flex-col items-center justify-center pt-5 pb-6">
            <ImageIcon className="w-10 h-10 mb-3 text-gray-400" />
            <p className="mb-2 text-sm text-gray-500 dark:text-gray-400">
              <span className="font-semibold">Click to upload</span> or drag and drop
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              PNG, JPG or GIF (MAX. 2MB)
            </p>
          </div>
          <input
            type="file"
            className="hidden"
            accept="image/*"
            onChange={handleUpload}
            disabled={uploading}
          />
        </label>
      )}
      
      {uploading && (
        <div className="mt-2 text-sm text-gray-500 dark:text-gray-400">
          Uploading...
        </div>
      )}
    </div>
  );
}
