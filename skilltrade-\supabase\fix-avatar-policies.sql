-- Fix Avatar Storage Policies for Skilltrade
-- This script updates the storage policies to ensure avatar uploads and deletions work correctly

-- Create the avatars bucket if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM storage.buckets WHERE name = 'avatars') THEN
    INSERT INTO storage.buckets (id, name, public)
    VALUES ('avatars', 'avatars', true);
  END IF;
END $$;

-- First, delete any existing policies for the avatars bucket
DELETE FROM storage.policies
WHERE bucket_id = (SELECT id FROM storage.buckets WHERE name = 'avatars');

-- Create policy for viewing avatars (public access)
INSERT INTO storage.policies (name, bucket_id, operation, definition)
VALUES (
  'Avatars are viewable by everyone',
  (SELECT id FROM storage.buckets WHERE name = 'avatars'),
  'SELECT',
  'true'
);

-- Create policy for uploading avatars (authenticated users only)
INSERT INTO storage.policies (name, bucket_id, operation, definition)
VALUES (
  'Users can upload their own avatars',
  (SELECT id FROM storage.buckets WHERE name = 'avatars'),
  'INSERT',
  'auth.role() = ''authenticated'''
);

-- Create policy for updating avatars (authenticated users only)
INSERT INTO storage.policies (name, bucket_id, operation, definition)
VALUES (
  'Users can update their own avatars',
  (SELECT id FROM storage.buckets WHERE name = 'avatars'),
  'UPDATE',
  'auth.role() = ''authenticated'''
);

-- Create policy for deleting avatars (authenticated users only)
INSERT INTO storage.policies (name, bucket_id, operation, definition)
VALUES (
  'Users can delete their own avatars',
  (SELECT id FROM storage.buckets WHERE name = 'avatars'),
  'DELETE',
  'auth.role() = ''authenticated'''
);
