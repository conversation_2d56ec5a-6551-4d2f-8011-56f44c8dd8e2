const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceRoleKey) {
  console.error('Error: Missing Supabase environment variables.');
  console.error('Make sure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set in .env.local');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);

async function setupAvatarPolicies() {
  try {
    console.log('Setting up storage policies for avatars bucket...');

    // Check if the avatars bucket exists
    const { data: buckets, error: bucketsError } = await supabase
      .storage
      .listBuckets();

    if (bucketsError) {
      throw new Error(`Error listing buckets: ${bucketsError.message}`);
    }

    const avatarsBucket = buckets.find(bucket => bucket.name === 'avatars');
    
    if (!avatarsBucket) {
      throw new Error('Avatars bucket not found. Please create it first in the Supabase dashboard.');
    }

    console.log('Avatars bucket found. Setting up policies...');

    // 1. Policy for viewing avatars (public access)
    console.log('Creating policy for viewing avatars...');
    await supabase
      .rpc('create_storage_policy', {
        bucket_name: 'avatars',
        policy_name: 'Avatars are viewable by everyone',
        definition: 'true',
        operation: 'SELECT'
      });

    // 2. Policy for uploading avatars (authenticated users only)
    console.log('Creating policy for uploading avatars...');
    await supabase
      .rpc('create_storage_policy', {
        bucket_name: 'avatars',
        policy_name: 'Users can upload their own avatars',
        definition: '(auth.uid() = auth.uid())',
        operation: 'INSERT'
      });

    // 3. Policy for updating avatars (own avatars only)
    console.log('Creating policy for updating avatars...');
    await supabase
      .rpc('create_storage_policy', {
        bucket_name: 'avatars',
        policy_name: 'Users can update their own avatars',
        definition: '(auth.uid() = auth.uid())',
        operation: 'UPDATE'
      });

    // 4. Policy for deleting avatars (own avatars only)
    console.log('Creating policy for deleting avatars...');
    await supabase
      .rpc('create_storage_policy', {
        bucket_name: 'avatars',
        policy_name: 'Users can delete their own avatars',
        definition: '(auth.uid() = auth.uid())',
        operation: 'DELETE'
      });

    console.log('Avatar storage policies setup completed successfully!');
  } catch (error) {
    console.error('Error setting up avatar policies:', error.message);
    
    // Provide more helpful error message if the RPC function doesn't exist
    if (error.message.includes('function create_storage_policy() does not exist')) {
      console.log('\nAlternative method: You can set up the policies manually in the Supabase dashboard:');
      console.log('1. Go to Storage > Buckets > avatars > Policies');
      console.log('2. Create the following policies:');
      console.log('   - "Avatars are viewable by everyone" (SELECT) with definition: true');
      console.log('   - "Users can upload their own avatars" (INSERT) with definition: (auth.uid() = auth.uid())');
      console.log('   - "Users can update their own avatars" (UPDATE) with definition: (auth.uid() = auth.uid())');
      console.log('   - "Users can delete their own avatars" (DELETE) with definition: (auth.uid() = auth.uid())');
    }
    
    process.exit(1);
  }
}

// Alternative implementation using direct SQL if the RPC method doesn't work
async function setupAvatarPoliciesWithSQL() {
  try {
    console.log('Setting up storage policies for avatars bucket using SQL...');

    // Check if the avatars bucket exists
    const { data: buckets, error: bucketsError } = await supabase
      .storage
      .listBuckets();

    if (bucketsError) {
      throw new Error(`Error listing buckets: ${bucketsError.message}`);
    }

    const avatarsBucket = buckets.find(bucket => bucket.name === 'avatars');
    
    if (!avatarsBucket) {
      throw new Error('Avatars bucket not found. Please create it first in the Supabase dashboard.');
    }

    console.log('Avatars bucket found. Setting up policies...');

    // SQL for creating storage policies
    const sql = `
    -- First, remove any existing policies for the avatars bucket
    BEGIN;
    
    DELETE FROM storage.policies 
    WHERE bucket_id = (SELECT id FROM storage.buckets WHERE name = 'avatars');
    
    -- 1. Policy for viewing avatars (public access)
    INSERT INTO storage.policies (name, bucket_id, operation, definition)
    VALUES (
      'Avatars are viewable by everyone',
      (SELECT id FROM storage.buckets WHERE name = 'avatars'),
      'SELECT',
      'true'
    );
    
    -- 2. Policy for uploading avatars (authenticated users only)
    INSERT INTO storage.policies (name, bucket_id, operation, definition)
    VALUES (
      'Users can upload their own avatars',
      (SELECT id FROM storage.buckets WHERE name = 'avatars'),
      'INSERT',
      '(auth.uid() = auth.uid())'
    );
    
    -- 3. Policy for updating avatars (own avatars only)
    INSERT INTO storage.policies (name, bucket_id, operation, definition)
    VALUES (
      'Users can update their own avatars',
      (SELECT id FROM storage.buckets WHERE name = 'avatars'),
      'UPDATE',
      '(auth.uid() = auth.uid())'
    );
    
    -- 4. Policy for deleting avatars (own avatars only)
    INSERT INTO storage.policies (name, bucket_id, operation, definition)
    VALUES (
      'Users can delete their own avatars',
      (SELECT id FROM storage.buckets WHERE name = 'avatars'),
      'DELETE',
      '(auth.uid() = auth.uid())'
    );
    
    COMMIT;
    `;

    // Execute the SQL
    const { error } = await supabase.rpc('exec_sql', { sql });

    if (error) {
      throw error;
    }

    console.log('Avatar storage policies setup completed successfully using SQL!');
  } catch (error) {
    console.error('Error setting up avatar policies with SQL:', error.message);
    console.log('\nPlease set up the policies manually in the Supabase dashboard:');
    console.log('1. Go to Storage > Buckets > avatars > Policies');
    console.log('2. Create the following policies:');
    console.log('   - "Avatars are viewable by everyone" (SELECT) with definition: true');
    console.log('   - "Users can upload their own avatars" (INSERT) with definition: (auth.uid() = auth.uid())');
    console.log('   - "Users can update their own avatars" (UPDATE) with definition: (auth.uid() = auth.uid())');
    console.log('   - "Users can delete their own avatars" (DELETE) with definition: (auth.uid() = auth.uid())');
    
    process.exit(1);
  }
}

// Try the RPC method first, and if it fails, try the SQL method
setupAvatarPolicies().catch(() => {
  console.log('Falling back to SQL method...');
  setupAvatarPoliciesWithSQL();
});
