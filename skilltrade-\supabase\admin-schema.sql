-- Admin functionality for Skilltrade
-- This SQL adds admin capabilities to the database schema
-- Run this SQL in the Supabase SQL Editor

-- Add is_admin field to profiles table
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS is_admin BOOLEAN NOT NULL DEFAULT false;

-- Create a function to check if a user is an admin
CREATE OR REPLACE FUNCTION auth.is_admin()
RETURNS BOOLEAN AS $$
  SELECT EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND is_admin = true
  );
$$ LANGUAGE sql SECURITY DEFINER;

-- Update RLS policies to give admins full access to profiles
CREATE POLICY "Admins can view all profiles" ON profiles
  FOR SELECT USING (auth.is_admin());

CREATE POLICY "Admins can update all profiles" ON profiles
  FOR UPDATE USING (auth.is_admin());

-- Update RLS policies to give admins full access to skills
CREATE POLICY "Ad<PERSON> can view all skills" ON skills
  FOR SELECT USING (auth.is_admin());

CREATE POLICY "Ad<PERSON> can update all skills" ON skills
  FOR UPDATE USING (auth.is_admin());

CREATE POLICY "Ad<PERSON> can delete all skills" ON skills
  FOR DELETE USING (auth.is_admin());

-- Update RLS policies to give admins full access to sessions
CREATE POLICY "Admins can view all sessions" ON sessions
  FOR SELECT USING (auth.is_admin());

CREATE POLICY "Admins can update all sessions" ON sessions
  FOR UPDATE USING (auth.is_admin());

-- Update RLS policies to give admins full access to reviews
CREATE POLICY "Admins can view all reviews" ON reviews
  FOR SELECT USING (auth.is_admin());

CREATE POLICY "Admins can update all reviews" ON reviews
  FOR UPDATE USING (auth.is_admin());

CREATE POLICY "Admins can delete reviews" ON reviews
  FOR DELETE USING (auth.is_admin());

-- Update RLS policies to give admins full access to ledger
CREATE POLICY "Admins can view all ledger entries" ON ledger
  FOR SELECT USING (auth.is_admin());

-- Create a function to promote a user to admin
CREATE OR REPLACE FUNCTION promote_to_admin(user_id UUID)
RETURNS VOID AS $$
BEGIN
  UPDATE profiles
  SET is_admin = true
  WHERE id = user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to demote an admin to regular user
CREATE OR REPLACE FUNCTION demote_from_admin(user_id UUID)
RETURNS VOID AS $$
BEGIN
  UPDATE profiles
  SET is_admin = false
  WHERE id = user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to get all admin users
CREATE OR REPLACE FUNCTION get_admin_users()
RETURNS SETOF profiles AS $$
BEGIN
  RETURN QUERY
  SELECT * FROM profiles
  WHERE is_admin = true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the database.types.ts file to include the is_admin field
-- This needs to be done manually in the codebase
