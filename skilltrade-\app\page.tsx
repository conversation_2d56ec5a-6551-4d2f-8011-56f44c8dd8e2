import Image from "next/image";
import Link from "next/link";
import MainHeader from "@/components/MainHeader";
import MainFooter from "@/components/MainFooter";
import FeaturedSkillsSection from "@/components/FeaturedSkillsSection";
import PopularSkillCategories from "@/components/PopularSkillCategories";

export const dynamic = 'force-dynamic';

export default function Home() {
  return (
    <div className="flex flex-col min-h-screen bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-white">
      {/* Header/Navigation */}
      <MainHeader />

      {/* Hero Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center">
            <div className="md:w-1/2 mb-10 md:mb-0">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight text-gray-900 dark:text-white">
                Exchange Skills, <span className="bg-gradient-to-r from-blue-500 to-purple-600 text-transparent bg-clip-text">Learn Together</span>
              </h1>
              <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-lg">
                A community time-bank where the currency isn't money, but hours of help. Teach what you know, learn what you don't.
              </p>
              <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
                <Link
                  href="/signup"
                  className="px-6 py-3 rounded-lg bg-blue-600 hover:bg-blue-700 text-white text-center transition text-lg font-medium"
                >
                  Get Started
                </Link>
                <Link
                  href="/how-it-works"
                  className="px-6 py-3 rounded-lg border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 text-center transition text-lg font-medium"
                >
                  Learn More
                </Link>
              </div>
            </div>
            <div className="md:w-1/2 flex justify-center">
              <div className="relative w-full max-w-md h-80 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 rounded-xl overflow-hidden shadow-lg">
                <div className="absolute inset-0 flex items-center justify-center">
                  <p className="text-xl font-medium text-center px-6 text-gray-800 dark:text-white">
                    Connect with people who want to share their knowledge and learn from yours
                  </p>
                </div>
                <div className="absolute -bottom-10 -right-10 w-40 h-40 bg-blue-500/20 rounded-full blur-2xl"></div>
                <div className="absolute -top-10 -left-10 w-40 h-40 bg-purple-500/20 rounded-full blur-2xl"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Skills Section */}
      <FeaturedSkillsSection />

      {/* How It Works Section */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12 text-gray-900 dark:text-white">How Skilltrade Works</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-gray-50 dark:bg-gray-700 p-8 rounded-xl shadow-md">
              <div className="w-14 h-14 bg-blue-600 rounded-full flex items-center justify-center mb-6 text-white">
                <span className="text-xl font-bold">1</span>
              </div>
              <h3 className="text-xl font-semibold mb-3 text-gray-900 dark:text-white">Share Your Skills</h3>
              <p className="text-gray-600 dark:text-gray-300">
                List the skills you can teach others, from languages to cooking, coding to crafts.
              </p>
            </div>
            <div className="bg-gray-50 dark:bg-gray-700 p-8 rounded-xl shadow-md">
              <div className="w-14 h-14 bg-blue-600 rounded-full flex items-center justify-center mb-6 text-white">
                <span className="text-xl font-bold">2</span>
              </div>
              <h3 className="text-xl font-semibold mb-3 text-gray-900 dark:text-white">Earn Time Credits</h3>
              <p className="text-gray-600 dark:text-gray-300">
                Each hour you spend teaching earns you one time credit to spend on learning.
              </p>
            </div>
            <div className="bg-gray-50 dark:bg-gray-700 p-8 rounded-xl shadow-md">
              <div className="w-14 h-14 bg-blue-600 rounded-full flex items-center justify-center mb-6 text-white">
                <span className="text-xl font-bold">3</span>
              </div>
              <h3 className="text-xl font-semibold mb-3 text-gray-900 dark:text-white">Learn New Skills</h3>
              <p className="text-gray-600 dark:text-gray-300">
                Use your earned credits to book sessions and learn from other community members.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Popular Skill Categories Section */}
      <PopularSkillCategories />

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900/40 dark:to-purple-900/40">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6 text-gray-900 dark:text-white">Ready to Join Our Community?</h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
            Start sharing your skills and learning from others today. Everyone has something valuable to teach!
          </p>
          <Link
            href="/signup"
            className="px-8 py-4 rounded-lg bg-blue-600 hover:bg-blue-700 text-white transition text-lg font-medium inline-block"
          >
            Sign Up Now
          </Link>
        </div>
      </section>

      <MainFooter />
    </div>
  );
}
