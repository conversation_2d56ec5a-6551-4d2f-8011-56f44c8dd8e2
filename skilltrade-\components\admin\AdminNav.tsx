'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import {
  LayoutDashboard,
  Users,
  BookOpen,
  Calendar,
  CreditCard,
  BarChart2,
  Settings,
  Menu,
  X,
  LogOut,
  Database,
  Shield
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

export default function AdminNav() {
  const pathname = usePathname();
  const { signOut } = useAuth();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const navItems = [
    { name: 'Dashboard', href: '/admin', icon: LayoutDashboard },
    { name: 'Users', href: '/admin/users', icon: Users },
    { name: 'Skills', href: '/admin/skills', icon: BookOpen },
    { name: 'Sessions', href: '/admin/sessions', icon: Calendar },
    { name: 'Credits', href: '/admin/credits', icon: CreditCard },
    { name: 'Content Filter', href: '/admin/content-filter', icon: Shield },
    { name: 'Analytics', href: '/admin/analytics', icon: BarChart2 },
    { name: 'Database', href: '/admin/database', icon: Database },
    { name: 'Settings', href: '/admin/settings', icon: Settings },
  ];

  const isActive = (path: string) => {
    if (path === '/admin') {
      return pathname === '/admin';
    }
    return pathname?.startsWith(path);
  };

  return (
    <>
      {/* Mobile menu button */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <button
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          className="p-2 rounded-md bg-gray-800 text-white"
        >
          {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
        </button>
      </div>

      {/* Sidebar for desktop */}
      <div className="hidden lg:flex flex-col w-64 bg-gray-900 text-white h-screen fixed">
        <div className="p-5 border-b border-gray-800">
          <div className="flex items-center">
            <Image
              src="/logo-rounded.png"
              alt="Skilltrade Logo"
              width={36}
              height={36}
              className="mr-2 rounded-lg"
            />
            <h1 className="text-xl font-bold">Skilltrade Admin</h1>
          </div>
        </div>
        <nav className="flex-1 overflow-y-auto py-4">
          <ul className="space-y-2 px-3">
            {navItems.map((item) => (
              <li key={item.name}>
                <Link
                  href={item.href}
                  className={`flex items-center px-4 py-3 rounded-lg transition-colors ${
                    isActive(item.href)
                      ? 'bg-blue-600 text-white'
                      : 'text-gray-300 hover:bg-gray-800'
                  }`}
                >
                  <item.icon className="h-5 w-5 mr-3" />
                  <span>{item.name}</span>
                </Link>
              </li>
            ))}
          </ul>
        </nav>
        <div className="p-4 border-t border-gray-800">
          <button
            onClick={() => signOut()}
            className="flex items-center px-4 py-2 text-gray-300 hover:text-white w-full"
          >
            <LogOut className="h-5 w-5 mr-3" />
            <span>Sign Out</span>
          </button>
        </div>
      </div>

      {/* Mobile menu */}
      {isMobileMenuOpen && (
        <div className="lg:hidden fixed inset-0 z-40 bg-gray-900 bg-opacity-95">
          <div className="flex flex-col h-full pt-16">
            <div className="px-5 pb-4 flex items-center">
              <Image
                src="/logo-rounded.png"
                alt="Skilltrade Logo"
                width={32}
                height={32}
                className="mr-2 rounded-lg"
              />
              <h1 className="text-xl font-bold text-white">Skilltrade Admin</h1>
            </div>
            <nav className="flex-1 overflow-y-auto py-4">
              <ul className="space-y-2 px-5">
                {navItems.map((item) => (
                  <li key={item.name}>
                    <Link
                      href={item.href}
                      onClick={() => setIsMobileMenuOpen(false)}
                      className={`flex items-center px-4 py-3 rounded-lg transition-colors ${
                        isActive(item.href)
                          ? 'bg-blue-600 text-white'
                          : 'text-gray-300 hover:bg-gray-800'
                      }`}
                    >
                      <item.icon className="h-5 w-5 mr-3" />
                      <span>{item.name}</span>
                    </Link>
                  </li>
                ))}
              </ul>
            </nav>
            <div className="p-5 border-t border-gray-800">
              <button
                onClick={() => signOut()}
                className="flex items-center px-4 py-2 text-gray-300 hover:text-white w-full"
              >
                <LogOut className="h-5 w-5 mr-3" />
                <span>Sign Out</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
