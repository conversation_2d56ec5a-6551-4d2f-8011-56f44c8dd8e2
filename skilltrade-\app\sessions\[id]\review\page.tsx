'use client';

import { useState, useEffect } from 'react';
import { createClientSide } from '@/lib/supabase';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import MainHeader from '@/components/MainHeader';
import MainFooter from '@/components/MainFooter';

interface Session {
  id: string;
  skill_id: string;
  teacher_id: string;
  learner_id: string;
  scheduled_at: string;
  duration_hours: number;
  status: string;
  skill: {
    title: string;
  };
  teacher: {
    display_name: string | null;
    email?: string;
  };
}

export default function ReviewPage({ params }: { params: { id: string } }) {
  const [session, setSession] = useState<Session | null>(null);
  const [rating, setRating] = useState<number>(5);
  const [comment, setComment] = useState('');
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [message, setMessage] = useState<string | null>(null);
  const router = useRouter();
  const supabase = createClientSide();
  const { id } = params;

  useEffect(() => {
    const fetchSession = async () => {
      try {
        setLoading(true);

        // Get the current user
        const { data: { user } } = await supabase.auth.getUser();

        if (!user) {
          router.push('/login');
          return;
        }

        // Get the session
        const { data: sessionData, error: sessionError } = await supabase
          .from('sessions')
          .select(`
            *,
            skill:skills(title),
            teacher:profiles!sessions_teacher_id_fkey(display_name, email)
          `)
          .eq('id', id)
          .single();

        if (sessionError) {
          throw sessionError;
        }

        setSession(sessionData);

        // Check if the current user is the learner
        if (sessionData.learner_id !== user.id) {
          router.push(`/sessions/${id}`);
          return;
        }

        // Check if the session is completed
        if (sessionData.status !== 'completed') {
          router.push(`/sessions/${id}`);
          return;
        }

        // Check if a review already exists
        const { data: reviewData, error: reviewError } = await supabase
          .from('reviews')
          .select('*')
          .eq('session_id', id)
          .maybeSingle();

        if (reviewError) {
          throw reviewError;
        }

        if (reviewData) {
          router.push(`/sessions/${id}`);
          return;
        }
      } catch (error: any) {
        setError(error.message || 'Failed to load session');
      } finally {
        setLoading(false);
      }
    };

    fetchSession();
  }, [id, router, supabase]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);
    setError(null);
    setMessage(null);

    try {
      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        router.push('/login');
        return;
      }

      // Insert the review
      const { error } = await supabase
        .from('reviews')
        .insert({
          session_id: id,
          reviewer_id: user.id,
          rating,
          comment,
        });

      if (error) {
        throw error;
      }

      setMessage('Review submitted successfully!');

      // Redirect to the session page after a short delay
      setTimeout(() => {
        router.push(`/sessions/${id}`);
      }, 1500);
    } catch (error: any) {
      setError(error.message || 'Failed to submit review');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <MainHeader />
        <div className="container mx-auto px-4 py-8">
          <div className="flex justify-center items-center h-64">
            <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
            <p className="ml-4 text-gray-700 dark:text-gray-300">Loading session...</p>
          </div>
        </div>
        <MainFooter />
      </div>
    );
  }

  if (!session) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <MainHeader />
        <div className="container mx-auto px-4 py-8">
          <div className="bg-white dark:bg-gray-800 rounded-xl p-8 text-center shadow-lg">
            <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Session not found</h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              The session you're looking for doesn't exist or you don't have permission to review it.
            </p>
            <Link
              href="/sessions"
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition inline-block"
            >
              Back to Sessions
            </Link>
          </div>
        </div>
        <MainFooter />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <MainHeader />

      <main className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <div className="mb-8">
            <Link
              href={`/sessions/${id}`}
              className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition flex items-center gap-2"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M19 12H5M12 19l-7-7 7-7"/>
              </svg>
              Back to Session
            </Link>
          </div>

          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Leave a Review</h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Share your experience with {session.teacher.display_name || 'your teacher'} for the {session.skill.title} session
            </p>
          </div>

          {error && (
            <div className="bg-red-100 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 px-4 py-3 rounded-lg mb-6">
              {error}
            </div>
          )}

          {message && (
            <div className="bg-green-100 dark:bg-green-900/30 border border-green-200 dark:border-green-800 text-green-800 dark:text-green-300 px-4 py-3 rounded-lg mb-6">
              {message}
            </div>
          )}

          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
            <form onSubmit={handleSubmit}>
              <div className="mb-6">
                <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                  Rate your experience (1-5 stars)
                </label>
                <div className="flex items-center gap-2 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-300 dark:border-gray-600">
                  <div className="flex">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <button
                        key={star}
                        type="button"
                        onClick={() => setRating(star)}
                        className="p-1 focus:outline-none focus:ring-0"
                        aria-label={`Rate ${star} stars`}
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className={`h-8 w-8 ${star <= rating ? 'text-yellow-500' : 'text-gray-300 dark:text-gray-600'} transition-colors duration-150`}
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                      </button>
                    ))}
                  </div>
                  <span className="ml-2 text-gray-700 dark:text-gray-300 font-medium">
                    {rating} {rating === 1 ? 'Star' : 'Stars'}
                  </span>
                </div>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  {rating >= 3
                    ? 'Ratings of 3-5 stars will release time credits to the teacher.'
                    : 'Ratings below 3 stars will mark the session as disputed and will not release time credits.'}
                </p>
              </div>

              <div className="mb-6">
                <label htmlFor="comment" className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                  Your Review
                </label>
                <textarea
                  id="comment"
                  value={comment}
                  onChange={(e) => setComment(e.target.value)}
                  rows={6}
                  className="w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Share details about your experience..."
                  required
                ></textarea>
              </div>

              <div className="flex flex-col sm:flex-row justify-end gap-4 mt-8">
                <Link
                  href={`/sessions/${id}`}
                  className="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition text-center"
                >
                  Cancel
                </Link>
                <button
                  type="submit"
                  className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition font-medium"
                  disabled={submitting}
                >
                  {submitting ? 'Submitting...' : 'Submit Review'}
                </button>
              </div>
            </form>
          </div>

          <div className="mt-6 bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
            <h2 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Important Information</h2>
            <ul className="list-disc list-inside space-y-2 text-gray-600 dark:text-gray-300">
              <li>Reviews cannot be edited or deleted after submission.</li>
              <li>Ratings of 3-5 stars will release time credits to the teacher.</li>
              <li>Ratings below 3 stars will mark the session as disputed and will not release time credits.</li>
              <li>If a session is disputed, you and the teacher will need to resolve it through the chat system.</li>
              <li>Be honest, specific, and constructive in your feedback.</li>
              <li>Reviews help other community members make informed decisions.</li>
            </ul>
          </div>
        </div>
      </main>

      <MainFooter />
    </div>
  );
}
