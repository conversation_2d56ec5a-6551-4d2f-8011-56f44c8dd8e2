-- Admin database reset functions
-- This migration adds functions to safely reset the database while preserving user accounts

-- Create a function to check if the current user is an admin
CREATE OR REPLACE FUNCTION check_is_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND is_admin = true
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to reset the database (delete all data except users)
CREATE OR REPLACE FUNCTION admin_reset_database()
RETURNS VOID AS $$
BEGIN
  -- Check if the user is an admin
  IF NOT check_is_admin() THEN
    RAISE EXCEPTION 'Only administrators can reset the database';
  END IF;

  -- Disable triggers temporarily to avoid foreign key constraint issues
  SET session_replication_role = 'replica';
  
  -- Truncate tables in the correct order to avoid foreign key constraint issues
  -- Start with dependent tables and work backwards to base tables
  
  -- Clear reviews
  TRUNCATE TABLE reviews CASCADE;
  
  -- Clear session messages
  TRUNCATE TABLE session_messages CASCADE;
  
  -- Clear notifications
  TRUNCATE TABLE notifications CASCAD<PERSON>;
  
  -- Clear dispute resolutions
  TRUNCATE TABLE dispute_resolutions CASCADE;
  
  -- Clear ledger entries
  TRUNCATE TABLE ledger CASCADE;
  
  -- Clear available dates
  TRUNCATE TABLE skill_available_dates CASCADE;
  
  -- Clear sessions
  TRUNCATE TABLE sessions CASCADE;
  
  -- Clear skills
  TRUNCATE TABLE skills CASCADE;
  
  -- Reset credit balances to 1 for all users
  UPDATE profiles SET credit_balance = 1;
  
  -- Re-enable triggers
  SET session_replication_role = 'origin';
  
  -- Log the reset
  RAISE NOTICE 'Database reset completed successfully';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to temporarily disable RLS policies
CREATE OR REPLACE FUNCTION admin_disable_rls()
RETURNS VOID AS $$
BEGIN
  -- Check if the user is an admin
  IF NOT check_is_admin() THEN
    RAISE EXCEPTION 'Only administrators can disable RLS';
  END IF;

  -- Disable RLS for all tables
  ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;
  ALTER TABLE skills DISABLE ROW LEVEL SECURITY;
  ALTER TABLE sessions DISABLE ROW LEVEL SECURITY;
  ALTER TABLE reviews DISABLE ROW LEVEL SECURITY;
  ALTER TABLE skill_available_dates DISABLE ROW LEVEL SECURITY;
  ALTER TABLE ledger DISABLE ROW LEVEL SECURITY;
  ALTER TABLE notifications DISABLE ROW LEVEL SECURITY;
  ALTER TABLE session_messages DISABLE ROW LEVEL SECURITY;
  ALTER TABLE dispute_resolutions DISABLE ROW LEVEL SECURITY;
  
  RAISE NOTICE 'Row level security disabled';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to re-enable RLS policies
CREATE OR REPLACE FUNCTION admin_enable_rls()
RETURNS VOID AS $$
BEGIN
  -- Check if the user is an admin
  IF NOT check_is_admin() THEN
    RAISE EXCEPTION 'Only administrators can enable RLS';
  END IF;

  -- Re-enable RLS for all tables
  ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
  ALTER TABLE skills ENABLE ROW LEVEL SECURITY;
  ALTER TABLE sessions ENABLE ROW LEVEL SECURITY;
  ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;
  ALTER TABLE skill_available_dates ENABLE ROW LEVEL SECURITY;
  ALTER TABLE ledger ENABLE ROW LEVEL SECURITY;
  ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
  ALTER TABLE session_messages ENABLE ROW LEVEL SECURITY;
  ALTER TABLE dispute_resolutions ENABLE ROW LEVEL SECURITY;
  
  RAISE NOTICE 'Row level security enabled';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION check_is_admin() TO authenticated;
GRANT EXECUTE ON FUNCTION admin_reset_database() TO authenticated;
GRANT EXECUTE ON FUNCTION admin_disable_rls() TO authenticated;
GRANT EXECUTE ON FUNCTION admin_enable_rls() TO authenticated;
