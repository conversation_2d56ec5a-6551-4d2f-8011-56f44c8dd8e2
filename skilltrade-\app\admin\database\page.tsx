'use client';

import { useState } from 'react';
import { AlertTriangle, Database, Trash2, RefreshCw } from 'lucide-react';
import { createClientSide } from '@/lib/supabase';

export default function AdminDatabasePage() {
  const [isResetting, setIsResetting] = useState(false);
  const [confirmText, setConfirmText] = useState('');
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [resetProgress, setResetProgress] = useState<string[]>([]);
  const supabase = createClientSide();

  const resetDatabase = async () => {
    if (confirmText !== 'RESET DATABASE') {
      setMessage({
        type: 'error',
        text: 'Please type "RESET DATABASE" to confirm',
      });
      return;
    }

    try {
      setIsResetting(true);
      setMessage(null);
      setResetProgress([]);

      // Function to add progress messages
      const addProgress = (message: string) => {
        setResetProgress((prev) => [...prev, message]);
      };

      addProgress('Starting database reset...');

      // Use a direct SQL approach to bypass foreign key constraints
      addProgress('Preparing to reset database with SQL...');

      // Execute SQL to disable foreign key checks and truncate tables
      const { error: sqlError } = await supabase.rpc('admin_reset_database');

      if (sqlError) {
        // If the RPC doesn't exist, we'll try the manual approach
        addProgress('Direct SQL approach failed, trying alternative method...');

        // 1. Temporarily disable RLS policies
        addProgress('Temporarily disabling row-level security...');
        await supabase.rpc('admin_disable_rls');

        // 2. Delete all reviews (no foreign key dependencies)
        addProgress('Deleting reviews...');
        await supabase
          .from('reviews')
          .delete()
          .neq('id', '********-0000-0000-0000-************');
        addProgress('✅ Reviews deleted successfully');

        // 3. Delete all session messages
        addProgress('Deleting session messages...');
        await supabase
          .from('session_messages')
          .delete()
          .neq('id', '********-0000-0000-0000-************');
        addProgress('✅ Session messages deleted successfully');

        // 4. Delete all notifications
        addProgress('Deleting notifications...');
        await supabase
          .from('notifications')
          .delete()
          .neq('id', '********-0000-0000-0000-************');
        addProgress('✅ Notifications deleted successfully');

        // 5. Delete all dispute resolutions
        addProgress('Deleting dispute resolutions...');
        await supabase
          .from('dispute_resolutions')
          .delete()
          .neq('id', '********-0000-0000-0000-************');
        addProgress('✅ Dispute resolutions deleted successfully');

        // 6. Delete all ledger entries
        addProgress('Deleting ledger entries...');
        await supabase
          .from('ledger')
          .delete()
          .neq('id', '********-0000-0000-0000-************');
        addProgress('✅ Ledger entries deleted successfully');

        // 7. Delete all available dates
        addProgress('Deleting available dates...');
        await supabase
          .from('skill_available_dates')
          .delete()
          .neq('id', '********-0000-0000-0000-************');
        addProgress('✅ Available dates deleted successfully');

        // 8. Delete all sessions
        addProgress('Deleting sessions...');
        await supabase
          .from('sessions')
          .delete()
          .neq('id', '********-0000-0000-0000-************');
        addProgress('✅ Sessions deleted successfully');

        // 9. Delete all skills
        addProgress('Deleting skills...');
        await supabase
          .from('skills')
          .delete()
          .neq('id', '********-0000-0000-0000-************');
        addProgress('✅ Skills deleted successfully');

        // 10. Re-enable RLS policies
        addProgress('Re-enabling row-level security...');
        await supabase.rpc('admin_enable_rls');
      } else {
        addProgress('✅ Database tables cleared successfully via SQL');
      }

      // Reset credit balances to 1 for all users
      addProgress('Resetting user credit balances...');
      const { error: creditsError } = await supabase
        .from('profiles')
        .update({ credit_balance: 1 })
        .neq('id', '********-0000-0000-0000-************');

      if (creditsError) {
        throw new Error(`Error resetting credit balances: ${creditsError.message}`);
      }
      addProgress('✅ User credit balances reset successfully');

      addProgress('✅ Database reset completed successfully!');
      setMessage({
        type: 'success',
        text: 'Database has been reset successfully. All skills, sessions, reviews, and other data have been deleted while preserving user accounts.',
      });
    } catch (error: any) {
      console.error('Error resetting database:', error);
      setMessage({
        type: 'error',
        text: error.message || 'An error occurred while resetting the database',
      });
      setResetProgress((prev) => [...prev, `❌ Error: ${error.message}`]);
    } finally {
      setIsResetting(false);
      setConfirmText('');
    }
  };

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Database Management</h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          Reset the database by deleting all skills, sessions, reviews, and other data while preserving user accounts.
        </p>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-8">
        <div className="flex items-start mb-6">
          <div className="p-3 rounded-full bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 mr-4">
            <AlertTriangle size={24} />
          </div>
          <div>
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">Reset Database</h2>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              This action will delete all skills, sessions, reviews, messages, notifications, and other data from the database.
              User accounts will be preserved, but all credit balances will be reset to 1.
              <span className="block mt-2 font-semibold text-red-600 dark:text-red-400">
                This action cannot be undone!
              </span>
            </p>
          </div>
        </div>

        <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
          <div className="mb-4">
            <label htmlFor="confirm" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Type &quot;RESET DATABASE&quot; to confirm
            </label>
            <input
              type="text"
              id="confirm"
              value={confirmText}
              onChange={(e) => setConfirmText(e.target.value)}
              className="w-full px-4 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
              placeholder="RESET DATABASE"
              disabled={isResetting}
            />
          </div>

          <button
            onClick={resetDatabase}
            disabled={isResetting || confirmText !== 'RESET DATABASE'}
            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {isResetting ? (
              <>
                <RefreshCw size={18} className="mr-2 animate-spin" />
                Resetting Database...
              </>
            ) : (
              <>
                <Trash2 size={18} className="mr-2" />
                Reset Database
              </>
            )}
          </button>
        </div>

        {message && (
          <div
            className={`mt-6 p-4 rounded-lg ${
              message.type === 'success'
                ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 border border-green-200 dark:border-green-800'
                : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 border border-red-200 dark:border-red-800'
            }`}
          >
            {message.text}
          </div>
        )}

        {resetProgress.length > 0 && (
          <div className="mt-6 p-4 bg-gray-100 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Progress Log</h3>
            <div className="max-h-60 overflow-y-auto">
              {resetProgress.map((message, index) => (
                <div key={index} className="py-1 text-sm text-gray-700 dark:text-gray-300">
                  {message}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
