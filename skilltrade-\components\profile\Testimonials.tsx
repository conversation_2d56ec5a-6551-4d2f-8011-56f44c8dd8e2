'use client';

import { useState } from 'react';
import Image from 'next/image';
import { Star, Pencil } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface Testimonial {
  id: string;
  rating: number;
  comment: string | null;
  createdAt: string;
  skillTitle: string;
  learnerName: string;
  learnerAvatar: string | null;
  sessionId: string;
  featured: boolean;
}

interface TestimonialsProps {
  testimonials: Testimonial[];
  featuredTestimonials: Testimonial[];
  isEditable?: boolean;
  onUpdateFeatured?: (selectedIds: string[]) => Promise<void>;
  minRating?: number;
}

export default function Testimonials({
  testimonials = [],
  featuredTestimonials = [],
  isEditable = false,
  onUpdateFeatured,
  minRating = 0,
}: TestimonialsProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [selectedIds, setSelectedIds] = useState<string[]>(
    featuredTestimonials.map(t => t.id)
  );
  const [isSaving, setIsSaving] = useState(false);
  const [filterRating, setFilterRating] = useState(minRating);

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = async () => {
    if (!onUpdateFeatured) return;

    try {
      setIsSaving(true);
      await onUpdateFeatured(selectedIds);
      setIsEditing(false);
    } catch (error) {
      console.error('Error updating featured testimonials:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setSelectedIds(featuredTestimonials.map(t => t.id));
    setIsEditing(false);
  };

  const handleToggleSelect = (id: string) => {
    if (selectedIds.includes(id)) {
      setSelectedIds(selectedIds.filter(selectedId => selectedId !== id));
    } else {
      if (selectedIds.length < 3) {
        setSelectedIds([...selectedIds, id]);
      }
    }
  };

  // Filter testimonials by rating
  const filteredTestimonials = testimonials.filter(
    testimonial => filterRating === 0 || testimonial.rating >= filterRating
  );

  // Sort testimonials by rating (highest first) and then by date (newest first)
  const sortedTestimonials = [...filteredTestimonials].sort((a, b) => {
    if (b.rating !== a.rating) {
      return b.rating - a.rating;
    }
    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
  });

  const renderStars = (rating: number) => {
    return (
      <div className="flex">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-4 w-4 ${
              star <= rating
                ? 'text-yellow-400 fill-yellow-400'
                : 'text-gray-300 dark:text-gray-600'
            }`}
          />
        ))}
      </div>
    );
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm mb-6 w-full max-w-full break-words">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 w-full">
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2 sm:mb-0">Testimonials</h3>
        <div className="flex items-center gap-2">
          {!isEditing && (
            <select
              value={filterRating}
              onChange={(e) => setFilterRating(Number(e.target.value))}
              className="px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value={0}>All Reviews</option>
              <option value={3}>3+ Stars</option>
              <option value={4}>4+ Stars</option>
              <option value={5}>5 Stars Only</option>
            </select>
          )}
          {isEditable && !isEditing && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleEdit}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
            >
              <Pencil className="h-4 w-4 mr-2" />
              Edit
            </Button>
          )}
        </div>
      </div>

      {isEditing ? (
        <div className="space-y-4">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Select up to 3 testimonials to feature on your profile. These will be shown prominently to potential learners.
          </p>

          <div className="space-y-3">
            {testimonials.length > 0 ? (
              testimonials.map((testimonial) => (
                <div
                  key={testimonial.id}
                  className={`bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border-2 ${
                    selectedIds.includes(testimonial.id)
                      ? 'border-blue-500'
                      : 'border-transparent'
                  }`}
                >
                  <div className="flex justify-between items-start">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 mr-3">
                        {testimonial.learnerAvatar ? (
                          <div className="relative h-10 w-10 rounded-full overflow-hidden">
                            <Image
                              src={testimonial.learnerAvatar}
                              alt={testimonial.learnerName}
                              fill
                              className="object-cover"
                            />
                          </div>
                        ) : (
                          <div className="h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                            <span className="text-gray-600 dark:text-gray-300 text-sm font-medium">
                              {testimonial.learnerName.charAt(0).toUpperCase()}
                            </span>
                          </div>
                        )}
                      </div>
                      <div>
                        <p className="font-medium text-gray-800 dark:text-gray-200">
                          {testimonial.learnerName}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {formatDate(testimonial.createdAt)}
                        </p>
                      </div>
                    </div>
                    <div className="flex flex-col items-end">
                      {renderStars(testimonial.rating)}
                      <span className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        for {testimonial.skillTitle}
                      </span>
                    </div>
                  </div>
                  {testimonial.comment && (
                    <div className="mt-3">
                      <p className="text-gray-700 dark:text-gray-300">
                        "{testimonial.comment}"
                      </p>
                    </div>
                  )}
                  <div className="mt-3 flex justify-end">
                    <Button
                      variant={selectedIds.includes(testimonial.id) ? "default" : "outline"}
                      size="sm"
                      onClick={() => handleToggleSelect(testimonial.id)}
                      disabled={!selectedIds.includes(testimonial.id) && selectedIds.length >= 3}
                    >
                      {selectedIds.includes(testimonial.id) ? 'Selected' : 'Select'}
                    </Button>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-gray-500 dark:text-gray-400 italic">
                No testimonials available yet. Testimonials will appear here after you receive reviews from learners.
              </p>
            )}
          </div>

          <div className="flex flex-wrap gap-2">
            <Button onClick={handleSave} disabled={isSaving} className="w-full sm:w-auto">
              {isSaving ? 'Saving...' : 'Save'}
            </Button>
            <Button variant="outline" onClick={handleCancel} disabled={isSaving} className="w-full sm:w-auto">
              Cancel
            </Button>
          </div>
        </div>
      ) : (
        <div>
          {isEditable ? (
            <div className="space-y-4">
              <h4 className="font-medium text-gray-900 dark:text-white">Featured Testimonials</h4>
              {featuredTestimonials.length > 0 ? (
                <div className="space-y-3">
                  {featuredTestimonials.map((testimonial) => (
                    <div
                      key={testimonial.id}
                      className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg"
                    >
                      <div className="flex justify-between items-start">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 mr-3">
                            {testimonial.learnerAvatar ? (
                              <div className="relative h-10 w-10 rounded-full overflow-hidden">
                                <Image
                                  src={testimonial.learnerAvatar}
                                  alt={testimonial.learnerName}
                                  fill
                                  className="object-cover"
                                />
                              </div>
                            ) : (
                              <div className="h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                                <span className="text-gray-600 dark:text-gray-300 text-sm font-medium">
                                  {testimonial.learnerName.charAt(0).toUpperCase()}
                                </span>
                              </div>
                            )}
                          </div>
                          <div>
                            <p className="font-medium text-gray-800 dark:text-gray-200">
                              {testimonial.learnerName}
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                              {formatDate(testimonial.createdAt)}
                            </p>
                          </div>
                        </div>
                        <div className="flex flex-col items-end">
                          {renderStars(testimonial.rating)}
                          <span className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            for {testimonial.skillTitle}
                          </span>
                        </div>
                      </div>
                      {testimonial.comment && (
                        <div className="mt-3">
                          <p className="text-gray-700 dark:text-gray-300">
                            "{testimonial.comment}"
                          </p>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 dark:text-gray-400 italic">
                  No featured testimonials yet. Click Edit to select testimonials to feature.
                </p>
              )}

              <h4 className="font-medium text-gray-900 dark:text-white mt-6">All Testimonials</h4>
              {sortedTestimonials.length > 0 ? (
                <div className="space-y-3">
                  {sortedTestimonials.map((testimonial) => (
                    <div
                      key={testimonial.id}
                      className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg"
                    >
                      <div className="flex justify-between items-start">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 mr-3">
                            {testimonial.learnerAvatar ? (
                              <div className="relative h-10 w-10 rounded-full overflow-hidden">
                                <Image
                                  src={testimonial.learnerAvatar}
                                  alt={testimonial.learnerName}
                                  fill
                                  className="object-cover"
                                />
                              </div>
                            ) : (
                              <div className="h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                                <span className="text-gray-600 dark:text-gray-300 text-sm font-medium">
                                  {testimonial.learnerName.charAt(0).toUpperCase()}
                                </span>
                              </div>
                            )}
                          </div>
                          <div>
                            <p className="font-medium text-gray-800 dark:text-gray-200">
                              {testimonial.learnerName}
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                              {formatDate(testimonial.createdAt)}
                            </p>
                          </div>
                        </div>
                        <div className="flex flex-col items-end">
                          {renderStars(testimonial.rating)}
                          <span className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            for {testimonial.skillTitle}
                          </span>
                        </div>
                      </div>
                      {testimonial.comment && (
                        <div className="mt-3">
                          <p className="text-gray-700 dark:text-gray-300">
                            "{testimonial.comment}"
                          </p>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 dark:text-gray-400 italic">
                  No testimonials match your filter criteria. Try adjusting the filter.
                </p>
              )}
            </div>
          ) : (
            <div className="space-y-3">
              {sortedTestimonials.length > 0 ? (
                sortedTestimonials.map((testimonial) => (
                  <div
                    key={testimonial.id}
                    className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg"
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 mr-3">
                          {testimonial.learnerAvatar ? (
                            <div className="relative h-10 w-10 rounded-full overflow-hidden">
                              <Image
                                src={testimonial.learnerAvatar}
                                alt={testimonial.learnerName}
                                fill
                                className="object-cover"
                              />
                            </div>
                          ) : (
                            <div className="h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                              <span className="text-gray-600 dark:text-gray-300 text-sm font-medium">
                                {testimonial.learnerName.charAt(0).toUpperCase()}
                              </span>
                            </div>
                          )}
                        </div>
                        <div>
                          <p className="font-medium text-gray-800 dark:text-gray-200">
                            {testimonial.learnerName}
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            {formatDate(testimonial.createdAt)}
                          </p>
                        </div>
                      </div>
                      <div className="flex flex-col items-end">
                        {renderStars(testimonial.rating)}
                        <span className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          for {testimonial.skillTitle}
                        </span>
                      </div>
                    </div>
                    {testimonial.comment && (
                      <div className="mt-3">
                        <p className="text-gray-700 dark:text-gray-300">
                          "{testimonial.comment}"
                        </p>
                      </div>
                    )}
                  </div>
                ))
              ) : (
                <p className="text-center text-gray-500 dark:text-gray-400 italic">
                  {testimonials.length > 0
                    ? 'No testimonials match your filter criteria. Try adjusting the filter.'
                    : 'No testimonials available yet.'}
                </p>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
