-- Avatar Storage Policies for Skilltrade
-- This version uses the Supabase Storage API functions instead of direct table access

-- Create policy for viewing avatars (public access)
select storage.policy(
  'avatars', 
  'Avatars are viewable by everyone',
  'SELECT',
  'authenticated, anon',
  true
);

-- Create policy for uploading avatars (authenticated users only)
select storage.policy(
  'avatars', 
  'Users can upload their own avatars',
  'INSERT',
  'authenticated',
  'auth.uid() IS NOT NULL'
);

-- Create policy for updating avatars (own avatars only)
select storage.policy(
  'avatars', 
  'Users can update their own avatars',
  'UPDATE',
  'authenticated',
  'auth.uid() IS NOT NULL'
);

-- Create policy for deleting avatars (own avatars only)
select storage.policy(
  'avatars', 
  'Users can delete their own avatars',
  'DELETE',
  'authenticated',
  'auth.uid() IS NOT NULL'
);

-- Advanced policies (optional)
-- These ensure users can only access their own folders
-- Uncomment and run these if you want more secure policies

/*
-- Create policy for uploading avatars to own folder
select storage.policy(
  'avatars', 
  'Users can upload avatars to their own folder',
  'INSERT',
  'authenticated',
  'auth.uid()::text = SPLIT_PART(storage.foldername(name), "/", 1)'
);

-- Create policy for updating avatars in own folder
select storage.policy(
  'avatars', 
  'Users can update avatars in their own folder',
  'UPDATE',
  'authenticated',
  'auth.uid()::text = SPLIT_PART(storage.foldername(name), "/", 1)'
);

-- Create policy for deleting avatars in own folder
select storage.policy(
  'avatars', 
  'Users can delete avatars in their own folder',
  'DELETE',
  'authenticated',
  'auth.uid()::text = SPLIT_PART(storage.foldername(name), "/", 1)'
);
*/
