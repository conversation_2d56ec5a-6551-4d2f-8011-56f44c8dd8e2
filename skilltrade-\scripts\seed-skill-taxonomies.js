// This script seeds the skill_taxonomies table with initial categories
// Run with: node scripts/seed-skill-taxonomies.js

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Error: Supabase URL or service role key not found in environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

const skillCategories = [
  // Main categories
  { name: 'Programming & Development', parent_id: null },
  { name: 'Design & Creative', parent_id: null },
  { name: 'Business & Finance', parent_id: null },
  { name: 'Marketing & Communication', parent_id: null },
  { name: 'Education & Teaching', parent_id: null },
  { name: 'Music & Audio', parent_id: null },
  { name: 'Health & Fitness', parent_id: null },
  { name: 'Languages', parent_id: null },
  { name: 'Crafts & DIY', parent_id: null },
  { name: 'Cooking & Culinary Arts', parent_id: null },
];

const subcategories = {
  'Programming & Development': [
    'Web Development',
    'Mobile App Development',
    'Game Development',
    'Data Science & Analytics',
    'DevOps & Cloud Computing',
    'Blockchain & Cryptocurrency',
    'Artificial Intelligence',
    'Database Management',
  ],
  'Design & Creative': [
    'Graphic Design',
    'UI/UX Design',
    'Animation',
    'Illustration',
    'Photography',
    'Video Editing',
    '3D Modeling',
    'Fashion Design',
  ],
  'Business & Finance': [
    'Accounting & Bookkeeping',
    'Financial Planning',
    'Investment Strategies',
    'Entrepreneurship',
    'Project Management',
    'Business Strategy',
    'Sales & Negotiation',
    'Real Estate',
  ],
  'Marketing & Communication': [
    'Digital Marketing',
    'Content Creation',
    'Social Media Management',
    'SEO & SEM',
    'Public Relations',
    'Copywriting',
    'Email Marketing',
    'Brand Strategy',
  ],
  'Education & Teaching': [
    'Tutoring',
    'Curriculum Development',
    'Special Education',
    'Test Preparation',
    'Early Childhood Education',
    'Adult Education',
    'Educational Technology',
    'Homeschooling',
  ],
  'Music & Audio': [
    'Instrument Lessons',
    'Music Production',
    'Singing & Vocal Training',
    'Music Theory',
    'DJing',
    'Songwriting',
    'Audio Engineering',
    'Music Composition',
  ],
  'Health & Fitness': [
    'Personal Training',
    'Yoga',
    'Nutrition',
    'Meditation & Mindfulness',
    'Dance',
    'Sports Coaching',
    'Physical Therapy',
    'Mental Health',
  ],
  'Languages': [
    'English',
    'Spanish',
    'French',
    'German',
    'Chinese',
    'Japanese',
    'Arabic',
    'Russian',
    'Portuguese',
    'Italian',
  ],
  'Crafts & DIY': [
    'Knitting & Crochet',
    'Woodworking',
    'Jewelry Making',
    'Sewing & Textiles',
    'Paper Crafts',
    'Pottery & Ceramics',
    'Home Improvement',
    'Gardening',
  ],
  'Cooking & Culinary Arts': [
    'Baking',
    'International Cuisine',
    'Vegetarian & Vegan Cooking',
    'Desserts & Pastries',
    'Wine & Beverage Pairing',
    'Meal Planning',
    'Food Photography',
    'Fermentation & Preserving',
  ],
};

async function seedSkillTaxonomies() {
  try {
    console.log('Starting to seed skill taxonomies...');
    
    // Insert main categories first
    const { data: mainCategories, error: mainError } = await supabase
      .from('skill_taxonomies')
      .upsert(
        skillCategories.map(category => ({
          name: category.name,
          parent_id: null,
        })),
        { onConflict: 'name' }
      )
      .select();
      
    if (mainError) {
      throw mainError;
    }
    
    console.log(`Inserted ${mainCategories.length} main categories`);
    
    // Create a map of category names to IDs
    const categoryMap = {};
    mainCategories.forEach(category => {
      categoryMap[category.name] = category.id;
    });
    
    // Insert subcategories
    let totalSubcategories = 0;
    
    for (const [mainCategory, subCats] of Object.entries(subcategories)) {
      const parentId = categoryMap[mainCategory];
      
      if (!parentId) {
        console.warn(`Parent category "${mainCategory}" not found, skipping subcategories`);
        continue;
      }
      
      const { data: subCategories, error: subError } = await supabase
        .from('skill_taxonomies')
        .upsert(
          subCats.map(name => ({
            name,
            parent_id: parentId,
          })),
          { onConflict: 'name' }
        )
        .select();
        
      if (subError) {
        console.error(`Error inserting subcategories for "${mainCategory}":`, subError);
        continue;
      }
      
      totalSubcategories += subCategories.length;
      console.log(`Inserted ${subCategories.length} subcategories for "${mainCategory}"`);
    }
    
    console.log(`Seeding complete! Inserted ${mainCategories.length} main categories and ${totalSubcategories} subcategories`);
    
  } catch (error) {
    console.error('Error seeding skill taxonomies:', error);
  }
}

seedSkillTaxonomies();
