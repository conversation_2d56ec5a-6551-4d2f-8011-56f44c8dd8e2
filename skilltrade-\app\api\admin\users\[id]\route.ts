import { NextRequest, NextResponse } from 'next/server';
import { createAdminClient } from '@/lib/supabase-admin';
import { isAdmin } from '@/lib/admin-utils';

// GET /api/admin/users/[id] - Get a specific user
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if the user is an admin
    const isUserAdmin = await isAdmin();
    if (!isUserAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    const userId = params.id;
    const supabase = createAdminClient();

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (profileError) {
      throw profileError;
    }

    // Get user's skills
    const { data: skills, error: skillsError } = await supabase
      .from('skills')
      .select('*')
      .eq('owner_id', userId);

    if (skillsError) {
      throw skillsError;
    }

    // Get user's sessions (as teacher or learner)
    const { data: sessions, error: sessionsError } = await supabase
      .from('sessions')
      .select(`
        *,
        skill:skills(title),
        teacher:profiles!sessions_teacher_id_fkey(display_name, email),
        learner:profiles!sessions_learner_id_fkey(display_name, email)
      `)
      .or(`teacher_id.eq.${userId},learner_id.eq.${userId}`)
      .order('scheduled_at', { ascending: false });

    if (sessionsError) {
      throw sessionsError;
    }

    // Get user's credit transactions
    const { data: transactions, error: transactionsError } = await supabase
      .from('ledger')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (transactionsError) {
      throw transactionsError;
    }

    return NextResponse.json({
      profile,
      skills: skills || [],
      sessions: sessions || [],
      transactions: transactions || [],
    });
  } catch (error: any) {
    console.error(`Error fetching user ${params.id}:`, error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch user' },
      { status: 500 }
    );
  }
}

// PATCH /api/admin/users/[id] - Update a user
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if the user is an admin
    const isUserAdmin = await isAdmin();
    if (!isUserAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    const userId = params.id;
    const supabase = createAdminClient();
    const body = await request.json();

    const { display_name, bio, hobbies, credit_balance, is_admin, email } = body;

    // Update profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .update({
        display_name,
        bio,
        hobbies,
        credit_balance,
        is_admin,
        email,
        updated_at: new Date().toISOString(),
      })
      .eq('id', userId)
      .select()
      .single();

    if (profileError) {
      throw profileError;
    }

    return NextResponse.json({
      profile,
      message: 'User updated successfully',
    });
  } catch (error: any) {
    console.error(`Error updating user ${params.id}:`, error);
    return NextResponse.json(
      { error: error.message || 'Failed to update user' },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/users/[id] - Delete a user
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if the user is an admin
    const isUserAdmin = await isAdmin();
    if (!isUserAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    const userId = params.id;
    const supabase = createAdminClient();

    // Delete the user from Supabase Auth
    const { error } = await supabase.auth.admin.deleteUser(userId);

    if (error) {
      throw error;
    }

    return NextResponse.json({
      message: 'User deleted successfully',
    });
  } catch (error: any) {
    console.error(`Error deleting user ${params.id}:`, error);
    return NextResponse.json(
      { error: error.message || 'Failed to delete user' },
      { status: 500 }
    );
  }
}
