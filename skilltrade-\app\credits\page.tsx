'use client';

import { useState, useEffect } from 'react';
import { createClientSide } from '@/lib/supabase';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import MainHeader from '@/components/MainHeader';
import MainFooter from '@/components/MainFooter';

interface LedgerEntry {
  id: string;
  user_id: string;
  session_id: string;
  hours_delta: number;
  reason: string;
  created_at: string;
  session: {
    skill: {
      title: string;
    };
    teacher: {
      display_name: string | null;
    };
    learner: {
      display_name: string | null;
    };
  };
}

interface Profile {
  id: string;
  credit_balance: number;
}

export default function CreditsPage() {
  const [ledgerEntries, setLedgerEntries] = useState<LedgerEntry[]>([]);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const supabase = createClientSide();

  useEffect(() => {
    const fetchCredits = async () => {
      try {
        setLoading(true);

        // Get the current user
        const { data: { user } } = await supabase.auth.getUser();

        if (!user) {
          router.push('/login');
          return;
        }

        // Get the user's profile
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('id, credit_balance')
          .eq('id', user.id)
          .single();

        if (profileError) {
          throw profileError;
        }

        setProfile(profileData);

        // Get the user's ledger entries
        const { data: ledgerData, error: ledgerError } = await supabase
          .from('ledger')
          .select(`
            *,
            session:sessions(
              skill:skills(title),
              teacher:profiles!sessions_teacher_id_fkey(display_name),
              learner:profiles!sessions_learner_id_fkey(display_name)
            )
          `)
          .eq('user_id', user.id)
          .order('created_at', { ascending: false });

        if (ledgerError) {
          throw ledgerError;
        }

        setLedgerEntries(ledgerData || []);
      } catch (error: any) {
        setError(error.message || 'Failed to load credit history');
      } finally {
        setLoading(false);
      }
    };

    fetchCredits();
  }, [router, supabase]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <MainHeader />
        <div className="container mx-auto px-4 py-8">
          <div className="flex justify-center items-center h-64">
            <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
            <p className="ml-4 text-gray-700 dark:text-gray-300">Loading credits...</p>
          </div>
        </div>
        <MainFooter />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <MainHeader />

      <main className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Credit History</h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Track your time credit earnings and spendings
            </p>
          </div>

          {error && (
            <div className="bg-red-100 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 px-4 py-3 rounded-lg mb-6">
              {error}
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/50 dark:to-purple-900/50 rounded-xl shadow-lg p-6">
              <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Current Balance</h2>
              <div className="flex items-center">
                <div className="text-4xl font-bold text-gray-900 dark:text-white">{profile?.credit_balance || 0}</div>
                <div className="ml-2 text-gray-700 dark:text-gray-300">credits</div>
              </div>
              <p className="mt-2 text-sm text-gray-600 dark:text-gray-300">
                Use credits to learn new skills or earn more by teaching
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
              <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Credits Earned</h2>
              <div className="flex items-center">
                <div className="text-4xl font-bold text-green-600 dark:text-green-400">
                  {ledgerEntries
                    .filter(entry => entry.hours_delta > 0)
                    .reduce((sum, entry) => sum + entry.hours_delta, 0)}
                </div>
                <div className="ml-2 text-gray-700 dark:text-gray-300">credits</div>
              </div>
              <p className="mt-2 text-sm text-gray-600 dark:text-gray-300">
                Total credits earned from teaching
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
              <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Credits Spent</h2>
              <div className="flex items-center">
                <div className="text-4xl font-bold text-red-600 dark:text-red-400">
                  {Math.abs(ledgerEntries
                    .filter(entry => entry.hours_delta < 0)
                    .reduce((sum, entry) => sum + entry.hours_delta, 0))}
                </div>
                <div className="ml-2 text-gray-700 dark:text-gray-300">credits</div>
              </div>
              <p className="mt-2 text-sm text-gray-600 dark:text-gray-300">
                Total credits spent on learning
              </p>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Transaction History</h2>
            </div>

            {ledgerEntries.length === 0 ? (
              <div className="p-8 text-center">
                <p className="text-gray-600 dark:text-gray-400 mb-4">No transactions yet</p>
                <div className="flex flex-col sm:flex-row justify-center gap-4">
                  <Link
                    href="/explore"
                    className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition inline-block"
                  >
                    Find Skills to Learn
                  </Link>
                  <Link
                    href="/skills/create"
                    className="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition inline-block"
                  >
                    Share Your Skills
                  </Link>
                </div>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="bg-gray-100 dark:bg-gray-700">
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 dark:text-gray-300 uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 dark:text-gray-300 uppercase tracking-wider">
                        Description
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 dark:text-gray-300 uppercase tracking-wider">
                        Type
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-700 dark:text-gray-300 uppercase tracking-wider">
                        Amount
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                    {ledgerEntries.map((entry) => {
                      const isEarning = entry.hours_delta > 0;
                      const otherPerson = isEarning
                        ? entry.session?.learner?.display_name || 'Unknown Learner'
                        : entry.session?.teacher?.display_name || 'Unknown Teacher';

                      return (
                        <tr key={entry.id} className="hover:bg-gray-50 dark:hover:bg-gray-750">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-700 dark:text-gray-300">
                              {new Date(entry.created_at).toLocaleDateString()}
                            </div>
                          </td>
                          <td className="px-6 py-4">
                            <div className="text-sm">
                              <Link
                                href={`/sessions/${entry.session_id}`}
                                className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium"
                              >
                                {entry.session?.skill?.title || 'Unknown Skill'}
                              </Link>
                              <span className="text-gray-600 dark:text-gray-400">
                                {' with '}{otherPerson}
                              </span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              isEarning
                                ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'
                                : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'
                            }`}>
                              {isEarning ? 'Teaching' : 'Learning'}
                            </span>
                          </td>
                          <td className="px-6 py-4 text-right whitespace-nowrap">
                            <div className={`text-sm font-medium ${
                              isEarning ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
                            }`}>
                              {isEarning ? '+' : ''}{entry.hours_delta}
                            </div>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      </main>

      <MainFooter />
    </div>
  );
}
