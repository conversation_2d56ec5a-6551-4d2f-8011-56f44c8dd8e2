'use client';

import { useState, useEffect, useRef } from 'react';
import { createClientSide } from '@/lib/supabase';
import Image from 'next/image';
import { Search, X } from 'lucide-react';

interface User {
  id: string;
  display_name: string | null;
  avatar_url: string | null;
  email: string;
}

interface Conversation {
  id: string;
  last_message_preview: string | null;
  last_message_at: string | null;
  created_at: string;
  updated_at: string;
  status: string;
  other_participants: {
    id: string;
    display_name: string | null;
    avatar_url: string | null;
  }[];
  unread_count: number;
}

interface NewConversationModalProps {
  onClose: () => void;
  onConversationCreated: (conversation: Conversation) => void;
  currentUser: any;
}

export default function NewConversationModal({
  onClose,
  onConversationCreated,
  currentUser
}: NewConversationModalProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const modalRef = useRef<HTMLDivElement>(null);
  const supabase = createClientSide();

  // Close modal when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);

  // Search for users
  useEffect(() => {
    const searchUsers = async () => {
      if (!searchQuery.trim()) {
        setUsers([]);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        const { data, error } = await supabase
          .from('profiles')
          .select('id, display_name, avatar_url, email')
          .or(`display_name.ilike.%${searchQuery}%,email.ilike.%${searchQuery}%`)
          .neq('id', currentUser.id)
          .limit(10);

        if (error) {
          throw error;
        }

        setUsers(data || []);
      } catch (error: any) {
        console.error('Error searching users:', error);
        setError(error.message || 'Failed to search users');
      } finally {
        setLoading(false);
      }
    };

    const debounceTimeout = setTimeout(() => {
      searchUsers();
    }, 300);

    return () => {
      clearTimeout(debounceTimeout);
    };
  }, [searchQuery, currentUser.id, supabase]);

  const handleUserSelect = async (userId: string) => {
    try {
      setLoading(true);
      setError(null);

      console.log('Creating conversation with user:', userId);
      console.log('Current user:', currentUser.id);

      const response = await fetch('/api/conversations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id: userId,
        }),
      });

      console.log('Conversation creation response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Conversation creation error:', errorData);
        throw new Error(errorData.error || 'Failed to create conversation');
      }

      const data = await response.json();
      console.log('Conversation created successfully:', data);

      // Ensure the conversation has the required fields
      const conversation = data.conversation;

      if (!conversation.status) {
        console.warn('Conversation is missing status field, adding default "pending" status');
        conversation.status = 'pending';
      }

      // Add unread_count if it doesn't exist
      if (conversation.unread_count === undefined) {
        console.warn('Conversation is missing unread_count field, adding default 0');
        conversation.unread_count = 0;
      }

      onConversationCreated(conversation);
    } catch (error: any) {
      console.error('Error creating conversation:', error);
      setError(error.message || 'Failed to create conversation');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div
        ref={modalRef}
        className="bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-md overflow-hidden"
      >
        <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">New Conversation</h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <X size={20} />
          </button>
        </div>

        <div className="p-4">
          <div className="relative mb-4">
            <input
              type="text"
              placeholder="Search for users..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full px-4 py-2 pl-10 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              autoFocus
            />
            <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
          </div>

          {error && (
            <div className="mb-4 p-3 bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 rounded-lg">
              {error}
            </div>
          )}

          <div className="max-h-60 overflow-y-auto">
            {loading ? (
              <div className="flex justify-center items-center h-20">
                <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-500"></div>
              </div>
            ) : users.length === 0 ? (
              <div className="text-center text-gray-500 dark:text-gray-400 py-4">
                {searchQuery ? 'No users found' : 'Type to search for users'}
              </div>
            ) : (
              <ul className="space-y-2">
                {users.map((user) => (
                  <li
                    key={user.id}
                    onClick={() => handleUserSelect(user.id)}
                    className="p-3 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg cursor-pointer"
                  >
                    <div className="flex items-center">
                      <div className="flex-shrink-0 mr-3">
                        {user.avatar_url ? (
                          <Image
                            src={user.avatar_url}
                            alt={user.display_name || user.email}
                            width={40}
                            height={40}
                            className="rounded-full"
                          />
                        ) : (
                          <div className="w-10 h-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                            <span className="text-gray-600 dark:text-gray-300 text-sm font-medium">
                              {(user.display_name || user.email.charAt(0)).toUpperCase()}
                            </span>
                          </div>
                        )}
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                          {user.display_name || 'Unnamed User'}
                        </h4>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {/* Email hidden for privacy */}
                          User
                        </p>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
