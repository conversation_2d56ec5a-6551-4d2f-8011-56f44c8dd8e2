/**
 * This script promotes a user to admin status.
 * It can be run with:
 * npx ts-node scripts/promote-admin.ts <user_email>
 * 
 * If no email is provided, it will promote the first user in the database.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing environment variables. Make sure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set.');
  process.exit(1);
}

// Create a Supabase client with the service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function promoteToAdmin(email?: string) {
  try {
    let userQuery = supabase.from('profiles').select('id, email, display_name, is_admin');
    
    // If email is provided, filter by email
    if (email) {
      userQuery = userQuery.eq('email', email);
    }
    
    // Get the user(s)
    const { data: users, error } = await userQuery.limit(1);
    
    if (error) {
      throw error;
    }
    
    if (!users || users.length === 0) {
      console.error('No users found.');
      process.exit(1);
    }
    
    const user = users[0];
    
    if (user.is_admin) {
      console.log(`User ${user.display_name || user.email} is already an admin.`);
      process.exit(0);
    }
    
    // Promote the user to admin
    const { error: updateError } = await supabase
      .from('profiles')
      .update({ is_admin: true })
      .eq('id', user.id);
    
    if (updateError) {
      throw updateError;
    }
    
    console.log(`Successfully promoted ${user.display_name || user.email} to admin.`);
  } catch (error) {
    console.error('Error promoting user to admin:', error);
    process.exit(1);
  }
}

// Get the email from command line arguments
const email = process.argv[2];

// Run the function
promoteToAdmin(email);
