-- Migration to fix conversation queries and functions

-- Fix the get_or_create_conversation function to properly check for existing conversations
CREATE OR REPLACE FUNCTION get_or_create_conversation(user1_id UUID, user2_id UUID)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  existing_conversation_id UUID;
  new_conversation_id UUID;
BEGIN
  -- Check if a conversation already exists between these users
  -- This query is more robust and will find conversations regardless of the order of participants
  SELECT c.id INTO existing_conversation_id
  FROM conversations c
  WHERE EXISTS (
    SELECT 1
    FROM conversation_participants cp1
    WHERE cp1.conversation_id = c.id AND cp1.user_id = user1_id
  )
  AND EXISTS (
    SELECT 1
    FROM conversation_participants cp2
    WHERE cp2.conversation_id = c.id AND cp2.user_id = user2_id
  )
  LIMIT 1;

  -- If a conversation exists, return it
  IF existing_conversation_id IS NOT NULL THEN
    RETURN existing_conversation_id;
  END IF;

  -- Otherwise, create a new conversation with pending status
  INSERT INTO conversations (id, created_at, updated_at, status)
  VALUES (uuid_generate_v4(), NOW(), NOW(), 'pending')
  RETURNING id INTO new_conversation_id;

  -- Add both users as participants
  INSERT INTO conversation_participants (conversation_id, user_id)
  VALUES
    (new_conversation_id, user1_id),
    (new_conversation_id, user2_id);

  -- Create a notification for the recipient
  PERFORM create_notification(
    user2_id,
    'conversation_request',
    'New conversation request',
    'Someone wants to start a conversation with you',
    '/dashboard/messages?id=' || new_conversation_id
  );

  RETURN new_conversation_id;
END;
$$;

-- Fix the accept_conversation_request function
CREATE OR REPLACE FUNCTION accept_conversation_request(conversation_id UUID, user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  is_participant BOOLEAN;
  other_user_id UUID;
  conversation_status TEXT;
BEGIN
  -- Check if the user is a participant in the conversation
  SELECT EXISTS (
    SELECT 1 FROM conversation_participants
    WHERE conversation_id = $1 AND user_id = $2
  ) INTO is_participant;

  IF NOT is_participant THEN
    RAISE EXCEPTION 'User is not a participant in this conversation';
  END IF;

  -- Check the current status of the conversation
  SELECT status INTO conversation_status
  FROM conversations
  WHERE id = conversation_id;

  -- Only update if the status is pending
  IF conversation_status = 'pending' THEN
    -- Update the conversation status to accepted
    UPDATE conversations
    SET status = 'accepted'
    WHERE id = conversation_id;

    -- Get the other participant's ID
    SELECT cp.user_id INTO other_user_id
    FROM conversation_participants cp
    WHERE cp.conversation_id = $1 AND cp.user_id != $2
    LIMIT 1;

    -- Create a notification for the other user
    PERFORM create_notification(
      other_user_id,
      'conversation_accepted',
      'Conversation request accepted',
      'Your conversation request has been accepted',
      '/dashboard/messages?id=' || conversation_id
    );
  END IF;

  RETURN TRUE;
END;
$$;

-- Create a function to mark all notifications as read
CREATE OR REPLACE FUNCTION mark_all_notifications_as_read(user_id_param UUID)
RETURNS SETOF notifications
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  UPDATE notifications
  SET is_read = TRUE
  WHERE user_id = user_id_param AND is_read = FALSE
  RETURNING *;
END;
$$;
