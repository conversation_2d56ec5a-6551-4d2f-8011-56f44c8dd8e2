'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Pencil, Plus, X } from 'lucide-react';

interface SkillLevel {
  id: string;
  taxonomyId: string;
  taxonomyName: string;
  proficiencyLevel: number;
  description: string | null;
}

interface SkillCategory {
  id: string;
  name: string;
  parentId: string | null;
}

interface SkillsTaxonomyProps {
  skillLevels: SkillLevel[];
  categories: SkillCategory[];
  isEditable?: boolean;
  onSave?: (skillLevels: SkillLevel[]) => Promise<void>;
}

export default function SkillsTaxonomy({
  skillLevels = [],
  categories = [],
  isEditable = false,
  onSave,
}: SkillsTaxonomyProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [userSkills, setUserSkills] = useState<SkillLevel[]>(skillLevels);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [proficiencyLevel, setProficiencyLevel] = useState<number>(3);
  const [description, setDescription] = useState<string>('');
  const [isSaving, setIsSaving] = useState(false);

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = async () => {
    if (!onSave) return;
    
    try {
      setIsSaving(true);
      await onSave(userSkills);
      setIsEditing(false);
    } catch (error) {
      console.error('Error saving skill levels:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setUserSkills(skillLevels);
    setSelectedCategory('');
    setProficiencyLevel(3);
    setDescription('');
    setIsEditing(false);
  };

  const handleAddSkill = () => {
    if (!selectedCategory) return;
    
    const category = categories.find(c => c.id === selectedCategory);
    if (!category) return;
    
    const newSkill: SkillLevel = {
      id: `temp-${Date.now()}`, // Temporary ID, will be replaced on save
      taxonomyId: category.id,
      taxonomyName: category.name,
      proficiencyLevel,
      description: description.trim() || null,
    };
    
    setUserSkills([...userSkills, newSkill]);
    setSelectedCategory('');
    setProficiencyLevel(3);
    setDescription('');
  };

  const handleRemoveSkill = (index: number) => {
    setUserSkills(userSkills.filter((_, i) => i !== index));
  };

  const renderProficiencyBars = (level: number) => {
    return (
      <div className="flex space-x-1">
        {[1, 2, 3, 4, 5].map((i) => (
          <div
            key={i}
            className={`h-2 w-4 rounded-sm ${
              i <= level
                ? 'bg-blue-500'
                : 'bg-gray-300 dark:bg-gray-600'
            }`}
          />
        ))}
      </div>
    );
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Skills</h3>
        {isEditable && !isEditing && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleEdit}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          >
            <Pencil className="h-4 w-4 mr-2" />
            Edit
          </Button>
        )}
      </div>

      {isEditing ? (
        <div className="space-y-4">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Add a skill
            </label>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="">Select a skill category</option>
              {categories.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Proficiency Level (1-5)
            </label>
            <input
              type="range"
              min="1"
              max="5"
              value={proficiencyLevel}
              onChange={(e) => setProficiencyLevel(parseInt(e.target.value))}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
              <span>Beginner</span>
              <span>Intermediate</span>
              <span>Expert</span>
            </div>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Description (optional)
            </label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Briefly describe your experience with this skill..."
              className="w-full h-20 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white resize-none"
            />
          </div>

          <Button 
            onClick={handleAddSkill} 
            disabled={!selectedCategory}
            className="w-full"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Skill
          </Button>

          <div className="mt-6 space-y-2">
            <h4 className="font-medium text-gray-900 dark:text-white">Your Skills</h4>
            {userSkills.length > 0 ? (
              userSkills.map((skill, index) => (
                <div 
                  key={index} 
                  className="flex items-center justify-between bg-gray-100 dark:bg-gray-700 p-3 rounded-lg"
                >
                  <div>
                    <div className="flex items-center">
                      <span className="text-gray-800 dark:text-gray-200 font-medium">
                        {skill.taxonomyName}
                      </span>
                      <span className="ml-2 text-gray-500 dark:text-gray-400 text-sm">
                        (Level {skill.proficiencyLevel})
                      </span>
                    </div>
                    {skill.description && (
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        {skill.description}
                      </p>
                    )}
                  </div>
                  <button
                    onClick={() => handleRemoveSkill(index)}
                    className="text-gray-500 hover:text-red-500 dark:text-gray-400 dark:hover:text-red-400"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              ))
            ) : (
              <p className="text-gray-500 dark:text-gray-400 italic">
                No skills added yet. Add skills to showcase your expertise.
              </p>
            )}
          </div>

          <div className="flex space-x-2">
            <Button onClick={handleSave} disabled={isSaving}>
              {isSaving ? 'Saving...' : 'Save'}
            </Button>
            <Button variant="outline" onClick={handleCancel} disabled={isSaving}>
              Cancel
            </Button>
          </div>
        </div>
      ) : (
        <div>
          {userSkills.length > 0 ? (
            <div className="space-y-4">
              {userSkills.map((skill, index) => (
                <div key={index} className="space-y-1">
                  <div className="flex justify-between items-center">
                    <span className="font-medium text-gray-800 dark:text-gray-200">
                      {skill.taxonomyName}
                    </span>
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      Level {skill.proficiencyLevel}
                    </span>
                  </div>
                  {renderProficiencyBars(skill.proficiencyLevel)}
                  {skill.description && (
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      {skill.description}
                    </p>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500 dark:text-gray-400 italic">
              No skills information provided yet.
              {isEditable && ' Click Edit to add your skills.'}
            </p>
          )}
        </div>
      )}
    </div>
  );
}
