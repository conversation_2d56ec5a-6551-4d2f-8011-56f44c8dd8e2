import { createServerSide } from '@/lib/supabase-server';
import Link from 'next/link';

export const dynamic = 'force-dynamic';

interface Skill {
  tags: string[];
}

interface TagCount {
  tag: string;
  count: number;
}

export default async function PopularSkillCategories() {
  try {
    const supabase = await createServerSide();

    // Fetch all skills to extract tags
    const { data: skills, error } = await supabase
      .from('skills')
      .select('tags')
      .eq('is_active', true);

    if (error) {
      console.error('Error fetching skills for tags:', error);
      throw error;
    }

    // Extract all tags and count their occurrences
    const tagCounts: Record<string, number> = {};
    skills?.forEach((skill: Skill) => {
      if (skill.tags && Array.isArray(skill.tags)) {
        skill.tags.forEach(tag => {
          // Normalize tag (lowercase, trim)
          const normalizedTag = tag.toLowerCase().trim();
          if (normalizedTag) {
            tagCounts[normalizedTag] = (tagCounts[normalizedTag] || 0) + 1;
          }
        });
      }
    });

    // Convert to array and sort by count (most popular first)
    const sortedTags: TagCount[] = Object.entries(tagCounts)
      .map(([tag, count]) => ({ tag, count }))
      .sort((a: TagCount, b: TagCount) => b.count - a.count);

    // Take top 8 tags or fewer if less are available
    const topTags = sortedTags.slice(0, 8);

    // If we have less than 8 tags, add some default categories to fill the grid
    const defaultCategories = [
      'programming', 'language learning', 'music', 'cooking',
      'fitness', 'art & design', 'photography', 'business'
    ];

    let displayCategories = [];

    if (topTags.length >= 8) {
      // Use the top 8 actual tags
      displayCategories = topTags.map(item => item.tag);
    } else if (topTags.length > 0) {
      // Use the available tags and fill the rest with defaults
      const actualTags = topTags.map(item => item.tag);
      const actualTagsSet = new Set(actualTags.map(tag => tag.toLowerCase()));

      // Add defaults that don't overlap with actual tags
      const remainingDefaults = defaultCategories.filter(
        cat => !actualTagsSet.has(cat.toLowerCase())
      ).slice(0, 8 - actualTags.length);

      displayCategories = [...actualTags, ...remainingDefaults];
    } else {
      // No tags found, use all defaults
      displayCategories = defaultCategories;
    }

    // Prepare categories for display and linking
    const categories = displayCategories.map(category => {
      // Keep original for the URL parameter
      const originalTag = category;

      // Format for display (capitalize first letter of each word)
      const formattedTag = category
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
        .join(' ');

      return { original: originalTag, formatted: formattedTag };
    });

    return (
      <section className="py-16 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12 text-gray-900 dark:text-white">Discover Skills</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {categories.map((category, index) => (
              <Link
                key={index}
                href={`/explore?tags=${encodeURIComponent(category.original)}`}
                className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-md hover:shadow-lg transition cursor-pointer"
              >
                <h3 className="text-lg font-medium mb-2 text-gray-900 dark:text-white">{category.formatted}</h3>
                <p className="text-gray-500 dark:text-gray-400 text-sm">Explore teachers</p>
              </Link>
            ))}
          </div>
          <div className="text-center mt-10">
            <Link
              href="/explore"
              className="px-6 py-3 rounded-lg border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition inline-block"
            >
              View All Skills
            </Link>
          </div>
        </div>
      </section>
    );
  } catch (error) {
    console.error('Error in PopularSkillCategories:', error);

    // Fallback to default categories in case of error
    const defaultCategories = [
      'programming', 'language learning', 'music', 'cooking',
      'fitness', 'art & design', 'photography', 'business'
    ];

    // Prepare categories for display and linking
    const categories = defaultCategories.map(category => {
      // Keep original for the URL parameter
      const originalTag = category;

      // Format for display (capitalize first letter of each word)
      const formattedTag = category
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
        .join(' ');

      return { original: originalTag, formatted: formattedTag };
    });

    return (
      <section className="py-16 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12 text-gray-900 dark:text-white">Discover Skills</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {categories.map((category, index) => (
              <Link
                key={index}
                href={`/explore?tags=${encodeURIComponent(category.original)}`}
                className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-md hover:shadow-lg transition cursor-pointer"
              >
                <h3 className="text-lg font-medium mb-2 text-gray-900 dark:text-white">{category.formatted}</h3>
                <p className="text-gray-500 dark:text-gray-400 text-sm">Explore teachers</p>
              </Link>
            ))}
          </div>
          <div className="text-center mt-10">
            <Link
              href="/explore"
              className="px-6 py-3 rounded-lg border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition inline-block"
            >
              View All Skills
            </Link>
          </div>
        </div>
      </section>
    );
  }
}
