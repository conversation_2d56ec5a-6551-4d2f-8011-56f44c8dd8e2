'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { Edit, Trash2, Plus, CheckCircle, XCircle } from 'lucide-react';
import AdminPageHeader from '@/components/admin/AdminPageHeader';
import AdminDataTable from '@/components/admin/AdminDataTable';

interface Owner {
  id: string;
  display_name: string | null;
  email: string | null;
  avatar_url: string | null;
}

interface Skill {
  id: string;
  title: string;
  description: string | null;
  tags: string[];
  is_active: boolean;
  created_at: string;
  owner: Owner;
}

export default function AdminSkillsPage() {
  const [skills, setSkills] = useState<Skill[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [total, setTotal] = useState(0);
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');
  const [searchQuery, setSearchQuery] = useState('');
  
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    // Get status filter from URL if present
    const status = searchParams.get('status');
    if (status === 'active' || status === 'inactive') {
      setStatusFilter(status);
    }
    
    // Get search query from URL if present
    const query = searchParams.get('q');
    if (query) {
      setSearchQuery(query);
    }
  }, [searchParams]);

  const fetchSkills = async () => {
    try {
      setLoading(true);
      
      const queryParams = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        status: statusFilter,
      });
      
      if (searchQuery) {
        queryParams.set('query', searchQuery);
      }
      
      const response = await fetch(`/api/admin/skills?${queryParams.toString()}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch skills');
      }
      
      const data = await response.json();
      setSkills(data.skills);
      setTotal(data.total);
    } catch (error: any) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSkills();
  }, [page, limit, statusFilter, searchQuery]);

  const handleDeleteSkill = async (skillId: string) => {
    if (!confirm('Are you sure you want to delete this skill? This action cannot be undone.')) {
      return;
    }
    
    try {
      const response = await fetch(`/api/admin/skills/${skillId}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete skill');
      }
      
      // Refresh the skill list
      fetchSkills();
    } catch (error: any) {
      setError(error.message);
    }
  };

  const handleStatusChange = async (skillId: string, action: 'approve' | 'reject') => {
    try {
      const response = await fetch(`/api/admin/skills/${skillId}/status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: action }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to ${action} skill`);
      }
      
      // Refresh the skill list
      fetchSkills();
    } catch (error: any) {
      setError(error.message);
    }
  };

  const updateUrlParams = (status: string) => {
    const params = new URLSearchParams(searchParams.toString());
    
    if (status === 'all') {
      params.delete('status');
    } else {
      params.set('status', status);
    }
    
    if (searchQuery) {
      params.set('q', searchQuery);
    } else {
      params.delete('q');
    }
    
    router.push(`/admin/skills${params.toString() ? `?${params.toString()}` : ''}`);
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    
    const params = new URLSearchParams(searchParams.toString());
    
    if (searchQuery) {
      params.set('q', searchQuery);
    } else {
      params.delete('q');
    }
    
    if (statusFilter !== 'all') {
      params.set('status', statusFilter);
    } else {
      params.delete('status');
    }
    
    router.push(`/admin/skills${params.toString() ? `?${params.toString()}` : ''}`);
  };

  const columns = [
    {
      key: 'title',
      label: 'Title',
      sortable: true,
      render: (value: string, skill: Skill) => (
        <div>
          <div className="font-medium text-gray-900 dark:text-white">{value}</div>
          <div className="text-sm text-gray-500 dark:text-gray-400 line-clamp-1">
            {skill.description || 'No description'}
          </div>
        </div>
      ),
    },
    {
      key: 'owner',
      label: 'Owner',
      render: (owner: Owner) => (
        <div className="flex items-center">
          <div className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 flex-shrink-0 overflow-hidden mr-3">
            {owner?.avatar_url ? (
              <img
                src={owner.avatar_url}
                alt={owner?.display_name || 'User'}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {owner?.display_name?.charAt(0) || owner?.email?.charAt(0) || '?'}
                </span>
              </div>
            )}
          </div>
          <div className="text-sm text-gray-900 dark:text-white">
            {owner?.display_name || owner?.email || 'Unknown User'}
          </div>
        </div>
      ),
    },
    {
      key: 'tags',
      label: 'Tags',
      render: (tags: string[]) => (
        <div className="flex flex-wrap gap-1">
          {tags && tags.length > 0 ? (
            tags.slice(0, 3).map((tag, index) => (
              <span
                key={index}
                className="px-2 py-1 text-xs rounded-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300"
              >
                {tag}
              </span>
            ))
          ) : (
            <span className="text-gray-500 dark:text-gray-400 text-sm">No tags</span>
          )}
          {tags && tags.length > 3 && (
            <span className="text-gray-500 dark:text-gray-400 text-xs">+{tags.length - 3} more</span>
          )}
        </div>
      ),
    },
    {
      key: 'is_active',
      label: 'Status',
      sortable: true,
      render: (isActive: boolean) => (
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            isActive
              ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'
              : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'
          }`}
        >
          {isActive ? 'Active' : 'Inactive'}
        </span>
      ),
    },
    {
      key: 'created_at',
      label: 'Created',
      sortable: true,
      render: (value: string) => (
        <div className="text-gray-500 dark:text-gray-400">
          {new Date(value).toLocaleDateString()}
        </div>
      ),
    },
  ];

  const renderActions = (skill: Skill) => (
    <div className="flex items-center space-x-3">
      <Link
        href={`/admin/skills/${skill.id}`}
        className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
      >
        <Edit className="h-5 w-5" />
      </Link>
      
      {skill.is_active ? (
        <button
          onClick={() => handleStatusChange(skill.id, 'reject')}
          className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300"
          title="Reject skill"
        >
          <XCircle className="h-5 w-5" />
        </button>
      ) : (
        <button
          onClick={() => handleStatusChange(skill.id, 'approve')}
          className="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300"
          title="Approve skill"
        >
          <CheckCircle className="h-5 w-5" />
        </button>
      )}
      
      <button
        onClick={() => handleDeleteSkill(skill.id)}
        className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300"
        title="Delete skill"
      >
        <Trash2 className="h-5 w-5" />
      </button>
    </div>
  );

  return (
    <div>
      <AdminPageHeader
        title="Skill Management"
        description="View and manage skills on the platform"
        actions={
          <Link
            href="/admin/skills/new"
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <Plus className="h-5 w-5 mr-2" />
            Add Skill
          </Link>
        }
      />
      
      {error && (
        <div className="bg-red-100 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 px-4 py-3 rounded-lg mb-6">
          {error}
        </div>
      )}
      
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-md mb-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div>
            <label htmlFor="status-filter" className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
              Filter by Status
            </label>
            <select
              id="status-filter"
              value={statusFilter}
              onChange={(e) => {
                setStatusFilter(e.target.value as 'all' | 'active' | 'inactive');
                updateUrlParams(e.target.value);
              }}
              className="px-4 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Skills</option>
              <option value="active">Active Skills</option>
              <option value="inactive">Inactive Skills</option>
            </select>
          </div>
          
          <div className="flex-grow">
            <form onSubmit={handleSearch}>
              <label htmlFor="search-query" className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                Search Skills
              </label>
              <div className="flex gap-2">
                <input
                  type="text"
                  id="search-query"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search by title or description..."
                  className="flex-grow px-4 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
                >
                  Search
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
      
      <AdminDataTable
        columns={columns}
        data={skills}
        actions={renderActions}
        loading={loading}
        emptyState={
          <div className="text-center">
            <p className="text-gray-500 dark:text-gray-400 mb-4">No skills found</p>
            <Link
              href="/admin/skills/new"
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <Plus className="h-5 w-5 mr-2" />
              Add Skill
            </Link>
          </div>
        }
      />
      
      {/* Pagination */}
      {total > 0 && (
        <div className="mt-6 flex items-center justify-between">
          <div className="text-sm text-gray-700 dark:text-gray-300">
            Showing <span className="font-medium">{(page - 1) * limit + 1}</span> to{' '}
            <span className="font-medium">{Math.min(page * limit, total)}</span> of{' '}
            <span className="font-medium">{total}</span> skills
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => setPage(page - 1)}
              disabled={page === 1}
              className={`px-3 py-1 rounded-md ${
                page === 1
                  ? 'bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-600 cursor-not-allowed'
                  : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600'
              }`}
            >
              Previous
            </button>
            <button
              onClick={() => setPage(page + 1)}
              disabled={page * limit >= total}
              className={`px-3 py-1 rounded-md ${
                page * limit >= total
                  ? 'bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-600 cursor-not-allowed'
                  : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600'
              }`}
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
