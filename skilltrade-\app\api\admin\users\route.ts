import { NextRequest, NextResponse } from 'next/server';
import { createAdminClient } from '@/lib/supabase-admin';
import { isAdmin } from '@/lib/admin-utils';

// GET /api/admin/users - Get all users
export async function GET(request: NextRequest) {
  try {
    // Check if the user is an admin
    const isUserAdmin = await isAdmin();
    if (!isUserAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    const supabase = createAdminClient();
    
    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const query = searchParams.get('query') || '';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = (page - 1) * limit;
    
    // Build the query
    let usersQuery = supabase
      .from('profiles')
      .select('*', { count: 'exact' });
    
    // Add search filter if query is provided
    if (query) {
      usersQuery = usersQuery.or(
        `email.ilike.%${query}%,display_name.ilike.%${query}%`
      );
    }
    
    // Add pagination
    usersQuery = usersQuery
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);
    
    const { data: users, count, error } = await usersQuery;
    
    if (error) {
      throw error;
    }
    
    return NextResponse.json({
      users,
      total: count || 0,
      page,
      limit,
      totalPages: count ? Math.ceil(count / limit) : 0,
    });
  } catch (error: any) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch users' },
      { status: 500 }
    );
  }
}

// POST /api/admin/users - Create a new user
export async function POST(request: NextRequest) {
  try {
    // Check if the user is an admin
    const isUserAdmin = await isAdmin();
    if (!isUserAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    const supabase = createAdminClient();
    const body = await request.json();
    
    const { email, password, display_name, is_admin } = body;
    
    // Validate required fields
    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      );
    }
    
    // Create the user in Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email,
      password,
      email_confirm: true,
    });
    
    if (authError) {
      throw authError;
    }
    
    // Update the user's profile
    if (authData.user) {
      const { error: profileError } = await supabase
        .from('profiles')
        .update({
          display_name: display_name || null,
          is_admin: is_admin || false,
        })
        .eq('id', authData.user.id);
      
      if (profileError) {
        throw profileError;
      }
    }
    
    return NextResponse.json({
      user: authData.user,
      message: 'User created successfully',
    });
  } catch (error: any) {
    console.error('Error creating user:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to create user' },
      { status: 500 }
    );
  }
}
