-- Migration to add a function to mark a single notification as read

-- Create a function to mark a single notification as read
CREATE OR REPLACE FUNCTION mark_notification_read(notification_id_param UUID, user_id_param UUID)
RETURNS notifications
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  updated_notification notifications;
BEGIN
  -- Update the notification and return the updated record
  UPDATE notifications
  SET is_read = TRUE
  WHERE id = notification_id_param AND user_id = user_id_param
  RETURNING * INTO updated_notification;
  
  RETURN updated_notification;
END;
$$;
