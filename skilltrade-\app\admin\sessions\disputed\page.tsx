'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { AlertTriangle, Eye } from 'lucide-react';
import AdminPageHeader from '@/components/admin/AdminPageHeader';
import AdminDataTable from '@/components/admin/AdminDataTable';

interface Skill {
  id: string;
  title: string;
}

interface Profile {
  id: string;
  display_name: string | null;
  email: string | null;
  avatar_url: string | null;
}

interface Session {
  id: string;
  skill: Skill;
  teacher: Profile;
  learner: Profile;
  scheduled_at: string;
  duration_hours: number;
  status: string;
  notes: string | null;
  created_at: string;
}

export default function AdminDisputedSessionsPage() {
  const [sessions, setSessions] = useState<Session[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Fetch disputed sessions
  const fetchDisputedSessions = async () => {
    try {
      setLoading(true);
      
      const response = await fetch('/api/admin/sessions?status=disputed&limit=100');
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch sessions');
      }
      
      const data = await response.json();
      setSessions(data.sessions);
    } catch (error: any) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };
  
  useEffect(() => {
    fetchDisputedSessions();
  }, []);
  
  const columns = [
    {
      key: 'skill',
      label: 'Skill',
      render: (skill: Skill) => (
        <div className="font-medium text-gray-900 dark:text-white">
          {skill?.title || 'Unknown Skill'}
        </div>
      ),
    },
    {
      key: 'teacher',
      label: 'Teacher',
      render: (teacher: Profile) => (
        <div className="flex items-center">
          <div className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 flex-shrink-0 overflow-hidden mr-3">
            {teacher?.avatar_url ? (
              <img
                src={teacher.avatar_url}
                alt={teacher?.display_name || 'User'}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {teacher?.display_name?.charAt(0) || teacher?.email?.charAt(0) || '?'}
                </span>
              </div>
            )}
          </div>
          <div className="text-sm text-gray-900 dark:text-white">
            {teacher?.display_name || teacher?.email || 'Unknown User'}
          </div>
        </div>
      ),
    },
    {
      key: 'learner',
      label: 'Learner',
      render: (learner: Profile) => (
        <div className="flex items-center">
          <div className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 flex-shrink-0 overflow-hidden mr-3">
            {learner?.avatar_url ? (
              <img
                src={learner.avatar_url}
                alt={learner?.display_name || 'User'}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {learner?.display_name?.charAt(0) || learner?.email?.charAt(0) || '?'}
                </span>
              </div>
            )}
          </div>
          <div className="text-sm text-gray-900 dark:text-white">
            {learner?.display_name || learner?.email || 'Unknown User'}
          </div>
        </div>
      ),
    },
    {
      key: 'scheduled_at',
      label: 'Date',
      sortable: true,
      render: (value: string) => (
        <div className="text-gray-900 dark:text-white">
          {new Date(value).toLocaleDateString()}
        </div>
      ),
    },
    {
      key: 'duration_hours',
      label: 'Duration',
      render: (hours: number) => (
        <div className="text-gray-900 dark:text-white">
          {hours} hour{hours !== 1 ? 's' : ''}
        </div>
      ),
    },
    {
      key: 'notes',
      label: 'Notes',
      render: (notes: string | null) => (
        <div className="text-gray-700 dark:text-gray-300 line-clamp-2">
          {notes || 'No notes'}
        </div>
      ),
    },
  ];
  
  const renderActions = (session: Session) => (
    <div className="flex items-center space-x-3">
      <Link
        href={`/admin/sessions/${session.id}`}
        className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
        title="View details"
      >
        <Eye className="h-5 w-5" />
      </Link>
      
      <Link
        href={`/admin/sessions/${session.id}?resolve=true`}
        className="text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300"
        title="Resolve dispute"
      >
        <AlertTriangle className="h-5 w-5" />
      </Link>
    </div>
  );
  
  return (
    <div>
      <AdminPageHeader
        title="Disputed Sessions"
        description="Resolve sessions that have been marked as disputed"
        backHref="/admin/sessions"
      />
      
      {error && (
        <div className="bg-red-100 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 px-4 py-3 rounded-lg mb-6">
          {error}
        </div>
      )}
      
      <AdminDataTable
        columns={columns}
        data={sessions}
        searchable={false}
        actions={renderActions}
        loading={loading}
        emptyState={
          <div className="text-center">
            <p className="text-gray-500 dark:text-gray-400 mb-4">No disputed sessions found</p>
            <Link
              href="/admin/sessions"
              className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Back to Sessions
            </Link>
          </div>
        }
      />
    </div>
  );
}
