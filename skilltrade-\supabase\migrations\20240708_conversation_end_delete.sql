-- Migration to add functions for ending and deleting conversations

-- Drop existing functions if they exist
DROP FUNCTION IF EXISTS end_conversation(UUID, UUID);
DROP FUNCTION IF EXISTS delete_conversation(U<PERSON>D, UUID);

-- Create a function to end a conversation
CREATE OR REPLACE FUNCTION end_conversation(conversation_id_param UUID, user_id_param UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  is_participant BOOLEAN;
  other_user_id UUID;
  conversation_status TEXT;
BEGIN
  -- Check if the user is a participant in the conversation
  SELECT EXISTS (
    SELECT 1 FROM conversation_participants
    WHERE conversation_id = conversation_id_param AND user_id = user_id_param
  ) INTO is_participant;

  IF NOT is_participant THEN
    RAISE EXCEPTION 'User is not a participant in this conversation';
  END IF;

  -- Check the current status of the conversation
  SELECT status INTO conversation_status
  FROM conversations
  WHERE id = conversation_id_param;

  -- Only update if the status is accepted
  IF conversation_status = 'accepted' THEN
    -- Update the conversation status to ended
    UPDATE conversations
    SET status = 'ended'
    WHERE id = conversation_id_param;

    -- Get the other participant's ID
    SELECT cp.user_id INTO other_user_id
    FROM conversation_participants cp
    WHERE cp.conversation_id = conversation_id_param AND cp.user_id != user_id_param
    LIMIT 1;

    -- Create a notification for the other user
    PERFORM create_notification(
      other_user_id,
      'conversation_ended',
      'Conversation ended',
      'The other participant has ended this conversation',
      '/dashboard/messages?id=' || conversation_id_param
    );
  END IF;

  RETURN TRUE;
END;
$$;

-- Create a function to delete a conversation
CREATE OR REPLACE FUNCTION delete_conversation(conversation_id_param UUID, user_id_param UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  is_participant BOOLEAN;
  other_user_id UUID;
  other_participant_deleted BOOLEAN;
BEGIN
  -- Check if the user is a participant in the conversation
  SELECT EXISTS (
    SELECT 1 FROM conversation_participants
    WHERE conversation_id = conversation_id_param AND user_id = user_id_param
  ) INTO is_participant;

  IF NOT is_participant THEN
    RAISE EXCEPTION 'User is not a participant in this conversation';
  END IF;

  -- Get the other participant's ID
  SELECT cp.user_id INTO other_user_id
  FROM conversation_participants cp
  WHERE cp.conversation_id = conversation_id_param AND cp.user_id != user_id_param
  LIMIT 1;

  -- Check if the other participant has already deleted the conversation
  SELECT deleted = TRUE INTO other_participant_deleted
  FROM conversation_participants
  WHERE conversation_id = conversation_id_param AND user_id = other_user_id;

  -- Mark the conversation as deleted for this user
  UPDATE conversation_participants
  SET deleted = TRUE
  WHERE conversation_id = conversation_id_param AND user_id = user_id_param;

  -- If both participants have deleted the conversation, delete it completely
  IF other_participant_deleted = TRUE THEN
    -- Delete all messages
    DELETE FROM direct_messages
    WHERE conversation_id = conversation_id_param;

    -- Delete all participants
    DELETE FROM conversation_participants
    WHERE conversation_id = conversation_id_param;

    -- Delete the conversation
    DELETE FROM conversations
    WHERE id = conversation_id_param;
  ELSE
    -- Create a notification for the other user
    PERFORM create_notification(
      other_user_id,
      'conversation_deleted',
      'Conversation deleted',
      'The other participant has deleted this conversation',
      '/dashboard/messages'
    );
  END IF;

  RETURN TRUE;
END;
$$;

-- Add a deleted column to the conversation_participants table if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'conversation_participants' AND column_name = 'deleted'
  ) THEN
    ALTER TABLE conversation_participants ADD COLUMN deleted BOOLEAN DEFAULT FALSE;
  END IF;
END
$$;
