# Avatar Upload Fix for Skilltrade

This document provides instructions for fixing the avatar upload, update, and deletion functionality in the Skilltrade platform.

## The Problem

The avatar upload functionality was not working due to several issues:

1. The path extraction from the URL in the `deleteAvatar` function was incorrect
2. The Next.js Image component needed the `unoptimized` property to handle Supabase storage URLs
3. The Supabase storage policies might not be correctly configured

## The Solution

The following changes have been made to fix the issues:

1. Updated the `AvatarUpload` component to properly extract the path from the URL
2. Added the `unoptimized` property to the Next.js Image component
3. Updated the `next.config.js` file to allow unoptimized images
4. Created a SQL script to fix the Supabase storage policies

## How to Apply the Fix

### 1. Update the Supabase Storage Policies

You have two options for fixing the storage policies. Try Option 1 first, and if that doesn't work, try Option 2.

#### Option 1: Using Direct SQL

Run the following SQL in the Supabase SQL Editor:

```sql
-- Fix Avatar Storage Policies for Skilltrade
-- This script updates the storage policies to ensure avatar uploads and deletions work correctly

-- Create the avatars bucket if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM storage.buckets WHERE name = 'avatars') THEN
    INSERT INTO storage.buckets (id, name, public)
    VALUES ('avatars', 'avatars', true);
  END IF;
END $$;

-- First, delete any existing policies for the avatars bucket
DELETE FROM storage.policies
WHERE bucket_id = (SELECT id FROM storage.buckets WHERE name = 'avatars');

-- Create policy for viewing avatars (public access)
INSERT INTO storage.policies (name, bucket_id, operation, definition)
VALUES (
  'Avatars are viewable by everyone',
  (SELECT id FROM storage.buckets WHERE name = 'avatars'),
  'SELECT',
  'true'
);

-- Create policy for uploading avatars (authenticated users only)
INSERT INTO storage.policies (name, bucket_id, operation, definition)
VALUES (
  'Users can upload their own avatars',
  (SELECT id FROM storage.buckets WHERE name = 'avatars'),
  'INSERT',
  'auth.role() = ''authenticated'''
);

-- Create policy for updating avatars (authenticated users only)
INSERT INTO storage.policies (name, bucket_id, operation, definition)
VALUES (
  'Users can update their own avatars',
  (SELECT id FROM storage.buckets WHERE name = 'avatars'),
  'UPDATE',
  'auth.role() = ''authenticated'''
);

-- Create policy for deleting avatars (authenticated users only)
INSERT INTO storage.policies (name, bucket_id, operation, definition)
VALUES (
  'Users can delete their own avatars',
  (SELECT id FROM storage.buckets WHERE name = 'avatars'),
  'DELETE',
  'auth.role() = ''authenticated'''
);
```

#### Option 2: Using RLS Approach

If Option 1 doesn't work, try this alternative approach using Row-Level Security (RLS):

```sql
-- Fix Avatar Storage Policies for Skilltrade using RLS approach
-- This script updates the storage policies to ensure avatar uploads and deletions work correctly

-- Create the avatars bucket if it doesn't exist
BEGIN;

-- Check if the bucket exists
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM storage.buckets WHERE name = 'avatars') THEN
    -- Create the bucket
    INSERT INTO storage.buckets (id, name, public)
    VALUES ('avatars', 'avatars', true);
  END IF;
END $$;

-- Remove existing policies
DROP POLICY IF EXISTS "Avatars are viewable by everyone" ON storage.objects;
DROP POLICY IF EXISTS "Users can upload their own avatars" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own avatars" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own avatars" ON storage.objects;

-- Create policies using RLS
-- Policy for viewing avatars (public access)
CREATE POLICY "Avatars are viewable by everyone"
ON storage.objects FOR SELECT
USING (bucket_id = 'avatars');

-- Policy for uploading avatars (authenticated users only)
CREATE POLICY "Users can upload their own avatars"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (bucket_id = 'avatars');

-- Policy for updating avatars (authenticated users only)
CREATE POLICY "Users can update their own avatars"
ON storage.objects FOR UPDATE
TO authenticated
USING (bucket_id = 'avatars');

-- Policy for deleting avatars (authenticated users only)
CREATE POLICY "Users can delete their own avatars"
ON storage.objects FOR DELETE
TO authenticated
USING (bucket_id = 'avatars');

COMMIT;
```

#### Option 3: Using the Supabase Dashboard UI

If the SQL approaches don't work, you can set up the storage policies through the Supabase Dashboard UI:

1. Log in to the [Supabase Dashboard](https://app.supabase.com/)
2. Select your project
3. Navigate to "Storage" in the left sidebar
4. If the "avatars" bucket doesn't exist:
   - Click "New Bucket"
   - Name it "avatars"
   - Check "Public bucket" to make it public
   - Click "Create bucket"
5. Click on the "avatars" bucket
6. Go to the "Policies" tab
7. Create the following policies:
   - **Policy Name**: "Avatars are viewable by everyone"
     - **Operation**: SELECT
     - **Target roles**: Public
     - **Policy definition**: `true`
   - **Policy Name**: "Users can upload their own avatars"
     - **Operation**: INSERT
     - **Target roles**: Authenticated users only
     - **Policy definition**: `true`
   - **Policy Name**: "Users can update their own avatars"
     - **Operation**: UPDATE
     - **Target roles**: Authenticated users only
     - **Policy definition**: `true`
   - **Policy Name**: "Users can delete their own avatars"
     - **Operation**: DELETE
     - **Target roles**: Authenticated users only
     - **Policy definition**: `true`

### 2. Restart the Development Server

After making these changes, restart your development server:

```bash
npm run dev
```

## Testing the Fix

To test if the avatar upload functionality is working correctly:

1. Sign in to your account
2. Go to your profile page
3. Click on your avatar to open the upload dialog
4. Upload a new avatar image
5. Verify that the avatar is displayed correctly
6. Try removing the avatar
7. Verify that the avatar is removed correctly

## Troubleshooting

If you're still experiencing issues:

1. Check the browser console for any errors
2. Verify that the Supabase storage policies are correctly configured
3. Make sure the avatars bucket exists and is set to public
4. Check that the Next.js Image component is configured correctly
5. Verify that the Supabase client is initialized correctly

## Additional Notes

- The `unoptimized` property in the Next.js Image component bypasses the built-in image optimization, which can cause issues with external URLs like Supabase storage URLs.
- The path extraction from the URL is now more robust and handles different URL formats.
- The Supabase storage policies are now configured to allow authenticated users to upload, update, and delete their own avatars.
