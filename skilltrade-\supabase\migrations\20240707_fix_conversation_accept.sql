-- Fix the conversation request functions to resolve the ambiguous column reference

-- Drop the existing functions if they exist
DROP FUNCTION IF EXISTS accept_conversation_request(U<PERSON>D, UUID);
DROP FUNCTION IF EXISTS reject_conversation_request(UUID, UUID);

-- Create a fixed version of the function
CREATE OR REPLACE FUNCTION accept_conversation_request(conversation_id_param UUID, user_id_param UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  is_participant BOOLEAN;
  other_user_id UUID;
  conversation_status TEXT;
BEGIN
  -- Check if the user is a participant in the conversation
  SELECT EXISTS (
    SELECT 1 FROM conversation_participants
    WHERE conversation_id = conversation_id_param AND user_id = user_id_param
  ) INTO is_participant;

  IF NOT is_participant THEN
    RAISE EXCEPTION 'User is not a participant in this conversation';
  END IF;

  -- Check the current status of the conversation
  SELECT status INTO conversation_status
  FROM conversations
  WHERE id = conversation_id_param;

  -- Only update if the status is pending
  IF conversation_status = 'pending' THEN
    -- Update the conversation status to accepted
    UPDATE conversations
    SET status = 'accepted'
    WHERE id = conversation_id_param;

    -- Get the other participant's ID
    SELECT cp.user_id INTO other_user_id
    FROM conversation_participants cp
    WHERE cp.conversation_id = conversation_id_param AND cp.user_id != user_id_param
    LIMIT 1;

    -- Create a notification for the other user
    PERFORM create_notification(
      other_user_id,
      'conversation_accepted',
      'Conversation request accepted',
      'Your conversation request has been accepted',
      '/dashboard/messages?id=' || conversation_id_param
    );
  END IF;

  RETURN TRUE;
END;
$$;

-- Create a fixed version of the reject function
CREATE OR REPLACE FUNCTION reject_conversation_request(conversation_id_param UUID, user_id_param UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  is_participant BOOLEAN;
  other_user_id UUID;
  conversation_status TEXT;
BEGIN
  -- Check if the user is a participant in the conversation
  SELECT EXISTS (
    SELECT 1 FROM conversation_participants
    WHERE conversation_id = conversation_id_param AND user_id = user_id_param
  ) INTO is_participant;

  IF NOT is_participant THEN
    RAISE EXCEPTION 'User is not a participant in this conversation';
  END IF;

  -- Check the current status of the conversation
  SELECT status INTO conversation_status
  FROM conversations
  WHERE id = conversation_id_param;

  -- Only update if the status is pending
  IF conversation_status = 'pending' THEN
    -- Update the conversation status to rejected
    UPDATE conversations
    SET status = 'rejected'
    WHERE id = conversation_id_param;

    -- Get the other participant's ID
    SELECT cp.user_id INTO other_user_id
    FROM conversation_participants cp
    WHERE cp.conversation_id = conversation_id_param AND cp.user_id != user_id_param
    LIMIT 1;

    -- Create a notification for the other user
    PERFORM create_notification(
      other_user_id,
      'conversation_rejected',
      'Conversation request declined',
      'Your conversation request has been declined',
      '/dashboard/messages'
    );
  END IF;

  RETURN TRUE;
END;
$$;
