'use client';

import { useState, useEffect } from 'react';
import { createClientSide } from '@/lib/supabase';
import { User } from 'lucide-react';

interface ProfileAvatarUploadProps {
  userId: string;
  url?: string | null;
  size?: number;
  onUpload?: (url: string) => void;
}

export default function ProfileAvatarUpload({ userId, url, size = 150, onUpload }: ProfileAvatarUploadProps) {
  const [avatarUrl, setAvatarUrl] = useState<string | null>(null);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showOptions, setShowOptions] = useState(false);
  const supabase = createClientSide();

  useEffect(() => {
    if (url) setAvatarUrl(url);
  }, [url]);

  async function handleUpload(event: React.ChangeEvent<HTMLInputElement>) {
    try {
      setUploading(true);
      setError(null);

      if (!event.target.files || event.target.files.length === 0) {
        throw new Error('You must select an image to upload.');
      }

      const file = event.target.files[0];
      
      // Validate file type
      if (!file.type.startsWith('image/')) {
        throw new Error('File must be an image');
      }

      // Limit file size (2MB)
      const MAX_SIZE = 2 * 1024 * 1024; // 2MB
      if (file.size > MAX_SIZE) {
        throw new Error('File size must be less than 2MB');
      }

      // Create a unique file name
      const fileExt = file.name.split('.').pop();
      const fileName = `${userId}-${Date.now()}.${fileExt}`;
      const filePath = `${userId}/${fileName}`;

      console.log('Uploading to path:', filePath);

      // Upload the file
      const { data, error: uploadError } = await supabase.storage
        .from('avatars')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: true
        });

      if (uploadError) {
        console.error('Storage upload error:', uploadError);
        throw uploadError;
      }

      // Get the public URL
      const { data: { publicUrl } } = supabase.storage
        .from('avatars')
        .getPublicUrl(filePath);

      console.log('Public URL:', publicUrl);

      // Update the user's profile with the new avatar URL
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ avatar_url: publicUrl })
        .eq('id', userId);

      if (updateError) {
        console.error('Profile update error:', updateError);
        throw updateError;
      }

      setAvatarUrl(publicUrl);
      if (onUpload) onUpload(publicUrl);
      setShowOptions(false);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Error uploading avatar');
      console.error('Error uploading avatar:', error);
    } finally {
      setUploading(false);
    }
  }

  async function handleRemove() {
    try {
      setUploading(true);
      setError(null);

      if (!avatarUrl) return;

      // Extract the path from the URL
      console.log('Avatar URL:', avatarUrl);
      const urlParts = avatarUrl.split('/');
      const publicIndex = urlParts.indexOf('public');
      
      if (publicIndex <= 0 || publicIndex + 2 >= urlParts.length) {
        throw new Error('Invalid avatar URL format');
      }
      
      const bucket = urlParts[publicIndex + 1];
      const path = urlParts.slice(publicIndex + 2).join('/');
      
      console.log('Bucket:', bucket);
      console.log('Path:', path);
      
      if (!path || path.trim() === '') {
        throw new Error('Invalid file path');
      }
      
      // Delete the file
      const { error: deleteError } = await supabase.storage
        .from('avatars')
        .remove([path]);

      if (deleteError) {
        console.error('Storage removal error:', deleteError);
        throw deleteError;
      }

      // Update the user's profile to remove the avatar URL
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ avatar_url: null })
        .eq('id', userId);

      if (updateError) {
        console.error('Profile update error:', updateError);
        throw updateError;
      }

      setAvatarUrl(null);
      if (onUpload) onUpload('');
      setShowOptions(false);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Error removing avatar');
      console.error('Error removing avatar:', error);
    } finally {
      setUploading(false);
    }
  }

  return (
    <div className="relative">
      <div
        className="relative cursor-pointer"
        onClick={() => setShowOptions(!showOptions)}
      >
        {avatarUrl ? (
          <div className="relative">
            <img
              src={avatarUrl}
              alt="Avatar"
              className="rounded-full object-cover border-4 border-white dark:border-gray-700 shadow-lg"
              width={size}
              height={size}
              style={{ width: size, height: size }}
            />
            <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 rounded-full transition-all flex items-center justify-center">
              <span className="text-white opacity-0 hover:opacity-100">Change</span>
            </div>
          </div>
        ) : (
          <div
            className="flex items-center justify-center bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-300 rounded-full border-4 border-white dark:border-gray-700 shadow-lg"
            style={{ width: size, height: size }}
          >
            <User size={size/3} />
          </div>
        )}

        {uploading && (
          <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded-full">
            <div className="w-8 h-8 border-4 border-white border-t-transparent rounded-full animate-spin"></div>
          </div>
        )}
      </div>

      {showOptions && (
        <div className="absolute mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg z-10 overflow-hidden">
          <div className="py-1">
            <label className="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer">
              {uploading ? 'Uploading...' : 'Upload New Photo'}
              <input
                type="file"
                accept="image/*"
                onChange={handleUpload}
                disabled={uploading}
                className="hidden"
              />
            </label>

            {avatarUrl && (
              <button
                onClick={handleRemove}
                disabled={uploading}
                className="block w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                Remove Photo
              </button>
            )}

            <button
              onClick={() => setShowOptions(false)}
              className="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              Cancel
            </button>
          </div>
        </div>
      )}

      {error && (
        <p className="text-red-500 text-sm mt-2">{error}</p>
      )}
    </div>
  );
}
