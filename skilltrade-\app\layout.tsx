import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/contexts/AuthContext";
import { NotificationsProvider } from "@/contexts/NotificationsContext";
import { Toaster } from "@/components/ui/toaster";
import EarlyAccessBanner from "@/components/EarlyAccessBanner";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-sans",
});

export const metadata: Metadata = {
  metadataBase: new URL('https://skilltrade.xyz'),
  title: "Skilltrade - Exchange Skills, Learn Together",
  description: "A community time-bank where you can teach skills to earn credits and learn new skills from others.",
  keywords: ["skill exchange", "time bank", "community learning", "skill sharing", "teach and learn"],
  authors: [{ name: "Skilltrade Team" }],
  icons: {
    icon: [
      { url: '/favicon.ico' },
      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
    ],
    apple: [
      { url: '/apple-touch-icon.png' },
    ],
    other: [
      {
        rel: 'manifest',
        url: '/site.webmanifest',
      },
    ],
  },
  manifest: '/site.webmanifest',
  openGraph: {
    title: "Skilltrade - Exchange Skills, Learn Together",
    description: "A community time-bank where you can teach skills to earn credits and learn new skills from others.",
    url: "https://skilltrade.xyz",
    siteName: "Skilltrade",
    locale: "en_US",
    type: "website",
    images: [
      {
        url: '/android-chrome-512x512.png',
        width: 512,
        height: 512,
        alt: 'Skilltrade Logo',
      }
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Skilltrade - Exchange Skills, Learn Together",
    description: "A community time-bank where you can teach skills to earn credits and learn new skills from others.",
    images: ['/android-chrome-512x512.png'],
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <body className={`${inter.variable} font-sans antialiased`}>
        <AuthProvider>
          <NotificationsProvider>
            <EarlyAccessBanner />
            {children}
            <Toaster />
          </NotificationsProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
