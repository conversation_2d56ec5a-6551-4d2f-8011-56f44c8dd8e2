// <PERSON>ript to apply UI polish to the application
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const originalDir = path.join(process.cwd(), 'components', 'profile');
const polishedDir = path.join(process.cwd(), 'components', 'profile', 'polished');
const backupDir = path.join(process.cwd(), 'components', 'profile', 'backup');

// Create backup directory if it doesn't exist
if (!fs.existsSync(backupDir)) {
  fs.mkdirSync(backupDir, { recursive: true });
}

// Get all component files
const componentFiles = fs.readdirSync(originalDir)
  .filter(file => file.endsWith('.tsx') && file !== 'index.tsx');

// Function to backup original components
function backupComponents() {
  console.log('Backing up original components...');
  
  componentFiles.forEach(file => {
    const originalPath = path.join(originalDir, file);
    const backupPath = path.join(backupDir, file);
    
    fs.copyFileSync(originalPath, backupPath);
    console.log(`Backed up ${file}`);
  });
  
  console.log('Backup complete!');
}

// Function to apply polished components
function applyPolishedComponents() {
  console.log('Applying polished components...');
  
  componentFiles.forEach(file => {
    const originalPath = path.join(originalDir, file);
    const polishedPath = path.join(polishedDir, file);
    
    if (!fs.existsSync(polishedPath)) {
      console.log(`Polished version of ${file} not found, skipping...`);
      return;
    }
    
    fs.copyFileSync(polishedPath, originalPath);
    console.log(`Applied polished version of ${file}`);
  });
  
  // Copy animations.css to styles directory
  const animationsSource = path.join(polishedDir, 'animations.css');
  const animationsTarget = path.join(process.cwd(), 'styles', 'animations.css');
  
  if (fs.existsSync(animationsSource)) {
    fs.copyFileSync(animationsSource, animationsTarget);
    console.log('Copied animations.css to styles directory');
  }
  
  console.log('UI polish applied successfully!');
}

// Function to restore original components
function restoreComponents() {
  console.log('Restoring original components...');
  
  componentFiles.forEach(file => {
    const originalPath = path.join(originalDir, file);
    const backupPath = path.join(backupDir, file);
    
    if (!fs.existsSync(backupPath)) {
      console.log(`Backup of ${file} not found, skipping...`);
      return;
    }
    
    fs.copyFileSync(backupPath, originalPath);
    console.log(`Restored original version of ${file}`);
  });
  
  console.log('Restoration complete!');
}

// Main function
function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  switch (command) {
    case 'backup':
      backupComponents();
      break;
      
    case 'apply':
      backupComponents();
      applyPolishedComponents();
      break;
      
    case 'restore':
      restoreComponents();
      break;
      
    default:
      console.log('Usage: node apply-ui-polish.js [backup|apply|restore]');
      console.log('  backup: Backup original components');
      console.log('  apply: Apply polished components (includes backup)');
      console.log('  restore: Restore original components from backup');
      break;
  }
}

main();
