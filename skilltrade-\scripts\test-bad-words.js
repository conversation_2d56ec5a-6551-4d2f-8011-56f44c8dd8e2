// <PERSON><PERSON>t to test the bad words filter database functions
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Read environment variables from .env.local
const envPath = path.resolve(process.cwd(), '.env.local');
const envContent = fs.readFileSync(envPath, 'utf8');
const envVars = {};

envContent.split('\n').forEach(line => {
  const match = line.match(/^([^=]+)=(.*)$/);
  if (match) {
    const key = match[1].trim();
    const value = match[2].trim();
    envVars[key] = value;
    process.env[key] = value;
  }
});

console.log('Environment variables loaded');
console.log('SUPABASE_URL:', process.env.NEXT_PUBLIC_SUPABASE_URL);

// Create a Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function testBadWordsFilter() {
  console.log('Testing bad words filter...');

  try {
    // Get all bad words
    console.log('Getting all bad words...');
    const { data: badWords, error: getBadWordsError } = await supabase
      .from('bad_words')
      .select('*')
      .order('severity', { ascending: false })
      .order('word');

    if (getBadWordsError) {
      console.error('Error getting bad words:', getBadWordsError);
    } else {
      console.log(`Found ${badWords.length} bad words:`);
      console.table(badWords);
    }

    // Test the get_bad_words function
    console.log('\nTesting get_bad_words function...');
    const { data: functionBadWords, error: functionError } = await supabase
      .rpc('get_bad_words');

    if (functionError) {
      console.error('Error calling get_bad_words function:', functionError);
    } else {
      console.log(`Function returned ${functionBadWords.length} bad words:`);
      console.table(functionBadWords);
    }

    // Test adding a bad word
    console.log('\nTesting add_bad_word function...');
    const testWord = 'testword' + Math.floor(Math.random() * 1000);
    const { data: addResult, error: addError } = await supabase
      .rpc('add_bad_word', {
        word_text: testWord,
        word_severity: 1
      });

    if (addError) {
      console.error('Error adding bad word:', addError);
    } else {
      console.log(`Added bad word '${testWord}' with ID:`, addResult);
    }

    // Get the bad words again to verify the addition
    console.log('\nVerifying addition...');
    const { data: updatedBadWords, error: updatedError } = await supabase
      .from('bad_words')
      .select('*')
      .eq('word', testWord);

    if (updatedError) {
      console.error('Error getting updated bad words:', updatedError);
    } else {
      console.log('Found added word:');
      console.table(updatedBadWords);
    }

    // Clean up by deleting the test word
    if (updatedBadWords && updatedBadWords.length > 0) {
      console.log('\nCleaning up by deleting test word...');
      const { data: deleteResult, error: deleteError } = await supabase
        .rpc('delete_bad_word', {
          word_id: updatedBadWords[0].id
        });

      if (deleteError) {
        console.error('Error deleting bad word:', deleteError);
      } else {
        console.log('Delete result:', deleteResult);
      }
    }

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

testBadWordsFilter();
