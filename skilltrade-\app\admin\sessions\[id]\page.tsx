'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Save, ArrowLeft, AlertTriangle, CheckCircle, XCircle } from 'lucide-react';
import AdminPageHeader from '@/components/admin/AdminPageHeader';
import Link from 'next/link';

interface Skill {
  id: string;
  title: string;
  description: string | null;
  tags: string[];
}

interface Profile {
  id: string;
  display_name: string | null;
  email: string | null;
  avatar_url: string | null;
}

interface Session {
  id: string;
  skill: Skill;
  teacher: Profile;
  learner: Profile;
  teacher_id: string;
  learner_id: string;
  scheduled_at: string;
  duration_hours: number;
  status: string;
  notes: string | null;
  created_at: string;
}

interface Review {
  id: string;
  session_id: string;
  reviewer_id: string;
  rating: number;
  comment: string | null;
  created_at: string;
  reviewer: {
    display_name: string | null;
  };
}

export default function AdminSessionDetailPage({
  params,
}: {
  params: { id: string };
}) {
  const sessionId = params.id;
  const router = useRouter();
  const searchParams = useSearchParams();
  const showResolveForm = searchParams.get('resolve') === 'true';
  
  const [session, setSession] = useState<Session | null>(null);
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Form state
  const [formData, setFormData] = useState({
    status: '',
    notes: '',
    scheduled_at: '',
    duration_hours: 1,
  });
  
  // Resolve dispute form state
  const [resolveData, setResolveData] = useState({
    resolution: 'completed',
    adminNotes: '',
    creditAdjustment: {
      userId: '',
      hours: 0,
    },
  });
  
  // Fetch session data
  useEffect(() => {
    const fetchSessionData = async () => {
      try {
        setLoading(true);
        
        const response = await fetch(`/api/admin/sessions/${sessionId}`);
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to fetch session');
        }
        
        const data = await response.json();
        setSession(data.session);
        setReviews(data.reviews);
        
        // Initialize form data
        setFormData({
          status: data.session.status || '',
          notes: data.session.notes || '',
          scheduled_at: data.session.scheduled_at ? new Date(data.session.scheduled_at).toISOString().split('T')[0] : '',
          duration_hours: data.session.duration_hours || 1,
        });
        
        // Initialize resolve data
        setResolveData({
          resolution: 'completed',
          adminNotes: '',
          creditAdjustment: {
            userId: '',
            hours: 0,
          },
        });
      } catch (error: any) {
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };
    
    fetchSessionData();
  }, [sessionId]);
  
  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };
  
  // Handle number input changes
  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: parseFloat(value),
    });
  };
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setSaving(true);
      
      const response = await fetch(`/api/admin/sessions/${sessionId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update session');
      }
      
      const data = await response.json();
      setSession(data.session);
      
      // Show success message
      alert('Session updated successfully');
    } catch (error: any) {
      setError(error.message);
    } finally {
      setSaving(false);
    }
  };
  
  // Handle resolve dispute form input changes
  const handleResolveInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    if (name.startsWith('creditAdjustment.')) {
      const field = name.split('.')[1];
      setResolveData({
        ...resolveData,
        creditAdjustment: {
          ...resolveData.creditAdjustment,
          [field]: field === 'hours' ? parseFloat(value) : value,
        },
      });
    } else {
      setResolveData({
        ...resolveData,
        [name]: value,
      });
    }
  };
  
  // Handle resolve dispute form submission
  const handleResolveSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setSaving(true);
      
      const response = await fetch(`/api/admin/sessions/${sessionId}/resolve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(resolveData),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to resolve session');
      }
      
      const data = await response.json();
      setSession(data.session);
      
      // Show success message
      alert('Session dispute resolved successfully');
      
      // Redirect back to the session detail page without the resolve parameter
      router.push(`/admin/sessions/${sessionId}`);
    } catch (error: any) {
      setError(error.message);
    } finally {
      setSaving(false);
    }
  };
  
  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p className="ml-4 text-gray-700 dark:text-gray-300">Loading session data...</p>
      </div>
    );
  }
  
  return (
    <div>
      <AdminPageHeader
        title={`Session: ${session?.skill?.title || 'Unknown Skill'}`}
        backHref="/admin/sessions"
        actions={
          session?.status === 'disputed' && !showResolveForm ? (
            <Link
              href={`/admin/sessions/${sessionId}?resolve=true`}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
            >
              <AlertTriangle className="h-5 w-5 mr-2" />
              Resolve Dispute
            </Link>
          ) : null
        }
      />
      
      {error && (
        <div className="bg-red-100 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 px-4 py-3 rounded-lg mb-6">
          {error}
        </div>
      )}
      
      {showResolveForm && session?.status === 'disputed' ? (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Resolve Disputed Session</h2>
          <form onSubmit={handleResolveSubmit}>
            <div className="space-y-6">
              <div>
                <label htmlFor="resolution" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Resolution
                </label>
                <select
                  id="resolution"
                  name="resolution"
                  value={resolveData.resolution}
                  onChange={handleResolveInputChange}
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="completed">Mark as Completed</option>
                  <option value="cancelled">Mark as Cancelled</option>
                </select>
              </div>
              
              <div>
                <label htmlFor="adminNotes" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Admin Notes
                </label>
                <textarea
                  id="adminNotes"
                  name="adminNotes"
                  value={resolveData.adminNotes}
                  onChange={handleResolveInputChange}
                  rows={4}
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  placeholder="Explain the reason for this resolution"
                  required
                />
              </div>
              
              <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                <h3 className="text-lg font-medium mb-2 text-gray-900 dark:text-white">Credit Adjustment (Optional)</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                  If needed, you can adjust credits for one of the participants.
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="creditAdjustment.userId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      User
                    </label>
                    <select
                      id="creditAdjustment.userId"
                      name="creditAdjustment.userId"
                      value={resolveData.creditAdjustment.userId}
                      onChange={handleResolveInputChange}
                      className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="">Select a user</option>
                      <option value={session?.teacher_id}>
                        {session?.teacher?.display_name || session?.teacher?.email || 'Teacher'}
                      </option>
                      <option value={session?.learner_id}>
                        {session?.learner?.display_name || session?.learner?.email || 'Learner'}
                      </option>
                    </select>
                  </div>
                  
                  <div>
                    <label htmlFor="creditAdjustment.hours" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Hours (positive or negative)
                    </label>
                    <input
                      type="number"
                      id="creditAdjustment.hours"
                      name="creditAdjustment.hours"
                      value={resolveData.creditAdjustment.hours}
                      onChange={handleResolveInputChange}
                      step="0.5"
                      className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>
                </div>
              </div>
              
              <div className="flex justify-end space-x-3">
                <Link
                  href={`/admin/sessions/${sessionId}`}
                  className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Cancel
                </Link>
                <button
                  type="submit"
                  disabled={saving}
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {saving ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                      Resolving...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-5 w-5 mr-2" />
                      Resolve Dispute
                    </>
                  )}
                </button>
              </div>
            </div>
          </form>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          <div className="lg:col-span-2">
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Session Information</h2>
              <form onSubmit={handleSubmit}>
                <div className="space-y-6">
                  <div>
                    <label htmlFor="status" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Status
                    </label>
                    <select
                      id="status"
                      name="status"
                      value={formData.status}
                      onChange={handleInputChange}
                      className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="pending">Pending</option>
                      <option value="confirmed">Confirmed</option>
                      <option value="completed">Completed</option>
                      <option value="cancelled">Cancelled</option>
                      <option value="disputed">Disputed</option>
                    </select>
                  </div>
                  
                  <div>
                    <label htmlFor="scheduled_at" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Scheduled Date
                    </label>
                    <input
                      type="date"
                      id="scheduled_at"
                      name="scheduled_at"
                      value={formData.scheduled_at}
                      onChange={handleInputChange}
                      className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="duration_hours" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Duration (hours)
                    </label>
                    <input
                      type="number"
                      id="duration_hours"
                      name="duration_hours"
                      value={formData.duration_hours}
                      onChange={handleNumberChange}
                      min="0.5"
                      step="0.5"
                      className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="notes" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Notes
                    </label>
                    <textarea
                      id="notes"
                      name="notes"
                      value={formData.notes}
                      onChange={handleInputChange}
                      rows={4}
                      className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>
                  
                  <div className="flex justify-end">
                    <button
                      type="submit"
                      disabled={saving}
                      className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {saving ? (
                        <>
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                          Saving...
                        </>
                      ) : (
                        <>
                          <Save className="h-5 w-5 mr-2" />
                          Save Changes
                        </>
                      )}
                    </button>
                  </div>
                </div>
              </form>
            </div>
            
            {reviews.length > 0 && (
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mt-6">
                <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Reviews</h2>
                <div className="space-y-4">
                  {reviews.map((review) => (
                    <div key={review.id} className="border-b border-gray-200 dark:border-gray-700 pb-4 last:border-0 last:pb-0">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center">
                          <span className="font-medium text-gray-900 dark:text-white">
                            {review.reviewer?.display_name || 'Anonymous'}
                          </span>
                          <span className="text-gray-500 dark:text-gray-400 text-sm ml-2">
                            {new Date(review.created_at).toLocaleDateString()}
                          </span>
                        </div>
                        <div className="flex items-center">
                          <span className="text-yellow-500 mr-1">★</span>
                          <span className="text-gray-900 dark:text-white">{review.rating}</span>
                          <span className="text-gray-500 dark:text-gray-400">/5</span>
                        </div>
                      </div>
                      {review.comment && (
                        <p className="text-gray-700 dark:text-gray-300">{review.comment}</p>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
          
          <div>
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Skill</h2>
              {session?.skill ? (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    {session.skill.title}
                  </h3>
                  <p className="text-gray-700 dark:text-gray-300 mb-4">
                    {session.skill.description || 'No description provided'}
                  </p>
                  <div className="flex flex-wrap gap-1 mb-4">
                    {session.skill.tags && session.skill.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 text-xs rounded-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                  <Link
                    href={`/admin/skills/${session.skill.id}`}
                    className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm"
                  >
                    View Skill Details
                  </Link>
                </div>
              ) : (
                <p className="text-gray-500 dark:text-gray-400">Skill information not available</p>
              )}
            </div>
            
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mt-6">
              <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Teacher</h2>
              {session?.teacher ? (
                <div>
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 rounded-full bg-gray-200 dark:bg-gray-700 flex-shrink-0 overflow-hidden mr-4">
                      {session.teacher.avatar_url ? (
                        <img
                          src={session.teacher.avatar_url}
                          alt={session.teacher.display_name || 'User'}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <span className="text-lg text-gray-500 dark:text-gray-400">
                            {session.teacher.display_name?.charAt(0) || session.teacher.email?.charAt(0) || '?'}
                          </span>
                        </div>
                      )}
                    </div>
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                        {session.teacher.display_name || 'Unnamed User'}
                      </h3>
                      <p className="text-gray-500 dark:text-gray-400">{session.teacher.email}</p>
                    </div>
                  </div>
                  <Link
                    href={`/admin/users/${session.teacher.id}`}
                    className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm"
                  >
                    View Teacher Profile
                  </Link>
                </div>
              ) : (
                <p className="text-gray-500 dark:text-gray-400">Teacher information not available</p>
              )}
            </div>
            
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mt-6">
              <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Learner</h2>
              {session?.learner ? (
                <div>
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 rounded-full bg-gray-200 dark:bg-gray-700 flex-shrink-0 overflow-hidden mr-4">
                      {session.learner.avatar_url ? (
                        <img
                          src={session.learner.avatar_url}
                          alt={session.learner.display_name || 'User'}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <span className="text-lg text-gray-500 dark:text-gray-400">
                            {session.learner.display_name?.charAt(0) || session.learner.email?.charAt(0) || '?'}
                          </span>
                        </div>
                      )}
                    </div>
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                        {session.learner.display_name || 'Unnamed User'}
                      </h3>
                      <p className="text-gray-500 dark:text-gray-400">{session.learner.email}</p>
                    </div>
                  </div>
                  <Link
                    href={`/admin/users/${session.learner.id}`}
                    className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm"
                  >
                    View Learner Profile
                  </Link>
                </div>
              ) : (
                <p className="text-gray-500 dark:text-gray-400">Learner information not available</p>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
