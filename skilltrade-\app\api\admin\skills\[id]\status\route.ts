import { NextRequest, NextResponse } from 'next/server';
import { createAdminClient } from '@/lib/supabase-admin';
import { isAdmin } from '@/lib/admin-utils';

// POST /api/admin/skills/[id]/status - Approve/reject a skill
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if the user is an admin
    const isUserAdmin = await isAdmin();
    if (!isUserAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    const skillId = params.id;
    const supabase = createAdminClient();
    const body = await request.json();
    
    const { status } = body;
    
    if (status !== 'approve' && status !== 'reject') {
      return NextResponse.json(
        { error: 'Invalid status. Must be "approve" or "reject"' },
        { status: 400 }
      );
    }
    
    // Update the skill's active status
    const { data, error } = await supabase
      .from('skills')
      .update({
        is_active: status === 'approve',
      })
      .eq('id', skillId)
      .select()
      .single();
    
    if (error) {
      throw error;
    }
    
    return NextResponse.json({
      skill: data,
      message: `Skill ${status === 'approve' ? 'approved' : 'rejected'} successfully`,
    });
  } catch (error: any) {
    console.error(`Error updating skill status ${params.id}:`, error);
    return NextResponse.json(
      { error: error.message || 'Failed to update skill status' },
      { status: 500 }
    );
  }
}
