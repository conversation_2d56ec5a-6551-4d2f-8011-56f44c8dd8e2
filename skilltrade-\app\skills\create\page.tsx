'use client';

import { useState } from 'react';
import { createClientSide } from '@/lib/supabase';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import MainHeader from '@/components/MainHeader';
import MainFooter from '@/components/MainFooter';
import { XCircle } from 'lucide-react';
import { validateText } from '@/lib/bad-words-filter-pages';

interface AvailableDate {
  id: string;
  date: string;
  time: string;
  duration: number;
}

export default function CreateSkill() {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [tags, setTags] = useState('');
  const [availableDates, setAvailableDates] = useState<AvailableDate[]>([]);
  const [newDate, setNewDate] = useState('');
  const [newTime, setNewTime] = useState('');
  const [newDuration, setNewDuration] = useState(1);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [message, setMessage] = useState<string | null>(null);
  const router = useRouter();
  const supabase = createClientSide();

  const handleAddAvailableDate = () => {
    // Validate inputs
    if (!newDate || !newTime) {
      setError('Please select both date and time');
      return;
    }

    // Check if date is in the future
    const dateTime = new Date(`${newDate}T${newTime}`);
    if (dateTime <= new Date()) {
      setError('Please select a future date and time');
      return;
    }

    // Check if we already have 3 dates
    if (availableDates.length >= 3) {
      setError('You can only add up to 3 available dates');
      return;
    }

    // Check if this date/time is already added
    const isDuplicate = availableDates.some(
      date => date.date === newDate && date.time === newTime
    );

    if (isDuplicate) {
      setError('This date and time is already added');
      return;
    }

    // Add the new date
    setAvailableDates([
      ...availableDates,
      {
        id: crypto.randomUUID(),
        date: newDate,
        time: newTime,
        duration: newDuration
      }
    ]);

    // Clear inputs and error
    setNewDate('');
    setNewTime('');
    setNewDuration(1);
    setError(null);
  };

  const handleRemoveAvailableDate = (id: string) => {
    setAvailableDates(availableDates.filter(date => date.id !== id));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setMessage(null);

    try {
      // Validate title and description for bad words
      const titleValidation = await validateText(title);
      if (!titleValidation.isValid) {
        throw new Error(`Your skill title contains inappropriate language (${titleValidation.badWords.join(', ')}). Please remove these words.`);
      }

      const descriptionValidation = await validateText(description);
      if (!descriptionValidation.isValid) {
        throw new Error(`Your skill description contains inappropriate language (${descriptionValidation.badWords.join(', ')}). Please remove these words.`);
      }

      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        router.push('/login');
        return;
      }

      // Parse tags from comma-separated string to array
      const tagsArray = tags
        .split(',')
        .map(tag => tag.trim())
        .filter(tag => tag !== '');

      // Validate each tag for bad words
      for (const tag of tagsArray) {
        const tagValidation = await validateText(tag);
        if (!tagValidation.isValid) {
          throw new Error(`One of your tags contains inappropriate language (${tagValidation.badWords.join(', ')}). Please remove these words.`);
        }
      }

      // Insert the skill
      const { data, error } = await supabase
        .from('skills')
        .insert({
          owner_id: user.id,
          title,
          description,
          tags: tagsArray,
          is_active: true,
        })
        .select();

      if (error) {
        throw error;
      }

      // Insert available dates if any
      if (availableDates.length > 0) {
        const skillId = data[0].id;

        // Prepare available dates for insertion
        const availableDatesForInsert = availableDates.map(date => ({
          skill_id: skillId,
          date_time: new Date(`${date.date}T${date.time}`).toISOString(),
          duration_hours: date.duration,
          is_booked: false
        }));

        // Insert available dates
        const { error: datesError } = await supabase
          .from('skill_available_dates')
          .insert(availableDatesForInsert);

        if (datesError) {
          console.error('Error inserting available dates:', datesError);
          // We'll continue even if there's an error with dates
        }
      }

      setMessage('Skill created successfully!');

      // Clear the form
      setTitle('');
      setDescription('');
      setTags('');
      setAvailableDates([]);

      // Redirect to the skill page after a short delay
      setTimeout(() => {
        router.push(`/skills/${data[0].id}`);
      }, 1500);
    } catch (error: any) {
      setError(error.message || 'Failed to create skill');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <MainHeader />

      <main className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Share a Skill</h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Share your knowledge and earn time credits by teaching others
            </p>
          </div>

          {error && (
            <div className="bg-red-100 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 px-4 py-3 rounded-lg mb-6">
              {error}
            </div>
          )}

          {message && (
            <div className="bg-green-100 dark:bg-green-900/30 border border-green-200 dark:border-green-800 text-green-800 dark:text-green-300 px-4 py-3 rounded-lg mb-6">
              {message}
            </div>
          )}

          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
            <form onSubmit={handleSubmit}>
              <div className="mb-6">
                <label htmlFor="title" className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                  Skill Title
                </label>
                <input
                  id="title"
                  type="text"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  className="w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="e.g., JavaScript Programming, Guitar Lessons, French Conversation"
                  required
                />
              </div>

              <div className="mb-6">
                <label htmlFor="description" className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                  Description
                </label>
                <textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  rows={6}
                  className="w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Describe what you can teach, your experience level, and what learners can expect"
                  required
                ></textarea>
              </div>

              <div className="mb-6">
                <label htmlFor="tags" className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                  Tags
                </label>
                <input
                  id="tags"
                  type="text"
                  value={tags}
                  onChange={(e) => setTags(e.target.value)}
                  className="w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="e.g., programming, music, language (comma separated)"
                />
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Add relevant tags to help others find your skill
                </p>
              </div>

              <div className="mb-6">
                <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                  Available Dates (up to 3)
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400 mb-4">
                  Add dates when you're available to teach this skill. Learners will be able to choose from these dates.
                </p>

                {/* Available dates list */}
                {availableDates.length > 0 && (
                  <div className="mb-4 space-y-2">
                    {availableDates.map((date) => (
                      <div
                        key={date.id}
                        className="flex items-center justify-between bg-gray-50 dark:bg-gray-700 p-3 rounded-lg"
                      >
                        <div>
                          <span className="text-gray-900 dark:text-white font-medium">
                            {new Date(`${date.date}T${date.time}`).toLocaleDateString()} at {new Date(`${date.date}T${date.time}`).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                          </span>
                          <span className="text-gray-500 dark:text-gray-400 ml-2">
                            ({date.duration} {date.duration === 1 ? 'hour' : 'hours'})
                          </span>
                        </div>
                        <button
                          type="button"
                          onClick={() => handleRemoveAvailableDate(date.id)}
                          className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                        >
                          <XCircle size={18} />
                        </button>
                      </div>
                    ))}
                  </div>
                )}

                {/* Add new date form */}
                <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div>
                      <label htmlFor="new-date" className="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">
                        Date
                      </label>
                      <input
                        id="new-date"
                        type="date"
                        value={newDate}
                        onChange={(e) => setNewDate(e.target.value)}
                        min={new Date().toISOString().split('T')[0]}
                        className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label htmlFor="new-time" className="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">
                        Time
                      </label>
                      <input
                        id="new-time"
                        type="time"
                        value={newTime}
                        onChange={(e) => setNewTime(e.target.value)}
                        className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label htmlFor="new-duration" className="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">
                        Duration
                      </label>
                      <select
                        id="new-duration"
                        value={newDuration}
                        onChange={(e) => setNewDuration(Number(e.target.value))}
                        className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value={1}>1 hour</option>
                        <option value={2}>2 hours</option>
                        <option value={3}>3 hours</option>
                      </select>
                    </div>
                  </div>
                  <div className="flex justify-end">
                    <button
                      type="button"
                      onClick={handleAddAvailableDate}
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition text-sm"
                      disabled={availableDates.length >= 3}
                    >
                      Add Date
                    </button>
                  </div>
                </div>
                {availableDates.length >= 3 && (
                  <p className="text-xs text-amber-500 dark:text-amber-400 mt-2">
                    You've reached the maximum of 3 available dates.
                  </p>
                )}
              </div>

              <div className="flex flex-col sm:flex-row justify-end gap-4 mt-8">
                <Link
                  href="/skills"
                  className="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition text-center"
                >
                  Cancel
                </Link>
                <button
                  type="submit"
                  className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition font-medium"
                  disabled={loading}
                >
                  {loading ? 'Creating...' : 'Create Skill'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </main>

      <MainFooter />
    </div>
  );
}
