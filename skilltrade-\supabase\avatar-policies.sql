-- Avatar Storage Policies for Skilltrade
-- Run this SQL in the Supabase SQL Editor to set up storage policies for the avatars bucket

-- First, remove any existing policies for the avatars bucket
BEGIN;

DELETE FROM storage.policies 
WHERE bucket_id = (SELECT id FROM storage.buckets WHERE name = 'avatars');

-- 1. Policy for viewing avatars (public access)
INSERT INTO storage.policies (name, bucket_id, operation, definition)
VALUES (
  'Avatars are viewable by everyone',
  (SELECT id FROM storage.buckets WHERE name = 'avatars'),
  'SELECT',
  'true'
);

-- 2. Policy for uploading avatars (authenticated users only)
INSERT INTO storage.policies (name, bucket_id, operation, definition)
VALUES (
  'Users can upload their own avatars',
  (SELECT id FROM storage.buckets WHERE name = 'avatars'),
  'INSERT',
  '(auth.uid() = auth.uid())'
);

-- 3. Policy for updating avatars (own avatars only)
INSERT INTO storage.policies (name, bucket_id, operation, definition)
VALUES (
  'Users can update their own avatars',
  (SELECT id FROM storage.buckets WHERE name = 'avatars'),
  'UPDATE',
  '(auth.uid() = auth.uid())'
);

-- 4. Policy for deleting avatars (own avatars only)
INSERT INTO storage.policies (name, bucket_id, operation, definition)
VALUES (
  'Users can delete their own avatars',
  (SELECT id FROM storage.buckets WHERE name = 'avatars'),
  'DELETE',
  '(auth.uid() = auth.uid())'
);

COMMIT;
