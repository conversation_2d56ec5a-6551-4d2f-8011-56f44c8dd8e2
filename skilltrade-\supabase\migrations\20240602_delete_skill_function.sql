-- Function to safely delete a skill and all its related data
CREATE OR REPLACE FUNCTION delete_skill_with_dependencies(skill_id_param UUID)
RETURNS boolean AS $$
DECLARE
  current_session_id UUID;
BEGIN
  -- Check for active or upcoming sessions
  PERFORM id FROM sessions
  WHERE skill_id = skill_id_param
  AND status IN ('pending', 'accepted');

  IF FOUND THEN
    RAISE EXCEPTION 'Cannot delete skill with active or upcoming sessions. Please cancel all sessions first.';
  END IF;

  -- Delete ledger entries and reviews for all sessions of this skill
  -- First, get all session IDs for this skill
  FOR current_session_id IN
    SELECT id FROM sessions WHERE skill_id = skill_id_param
  LOOP
    -- Delete ledger entries for this session
    DELETE FROM ledger WHERE session_id = current_session_id;

    -- Delete reviews for this session
    DELETE FROM reviews WHERE session_id = current_session_id;
  END LOOP;

  -- Now delete all sessions for this skill
  DELETE FROM sessions WHERE skill_id = skill_id_param;

  -- Delete all available dates for this skill
  DELETE FROM skill_available_dates WHERE skill_id = skill_id_param;

  -- Finally delete the skill itself
  DELETE FROM skills WHERE id = skill_id_param;

  RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
