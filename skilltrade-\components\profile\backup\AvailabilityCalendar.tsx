'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Pencil, Plus, X, Calendar as CalendarIcon } from 'lucide-react';

interface TimeSlot {
  id: string;
  dayOfWeek: number;
  startTime: string;
  endTime: string;
  isRecurring: boolean;
  specificDate: string | null;
}

interface AvailabilityCalendarProps {
  availabilitySlots: TimeSlot[];
  isEditable?: boolean;
  onSave?: (slots: TimeSlot[]) => Promise<void>;
}

export default function AvailabilityCalendar({
  availabilitySlots = [],
  isEditable = false,
  onSave,
}: AvailabilityCalendarProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [slots, setSlots] = useState<TimeSlot[]>(availabilitySlots);
  const [newSlot, setNewSlot] = useState<Partial<TimeSlot>>({
    dayOfWeek: 1, // Monday
    startTime: '09:00',
    endTime: '10:00',
    isRecurring: true,
    specificDate: null,
  });
  const [isAddingSlot, setIsAddingSlot] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [view, setView] = useState<'week' | 'month'>('week');
  const [currentDate, setCurrentDate] = useState(new Date());

  const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  const shortDayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = async () => {
    if (!onSave) return;
    
    try {
      setIsSaving(true);
      await onSave(slots);
      setIsEditing(false);
    } catch (error) {
      console.error('Error saving availability slots:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setSlots(availabilitySlots);
    setNewSlot({
      dayOfWeek: 1,
      startTime: '09:00',
      endTime: '10:00',
      isRecurring: true,
      specificDate: null,
    });
    setIsAddingSlot(false);
    setIsEditing(false);
  };

  const handleAddSlot = () => {
    if (!newSlot.startTime || !newSlot.endTime) return;
    
    const slot: TimeSlot = {
      id: `temp-${Date.now()}`, // Temporary ID, will be replaced on save
      dayOfWeek: newSlot.dayOfWeek || 1,
      startTime: newSlot.startTime,
      endTime: newSlot.endTime,
      isRecurring: newSlot.isRecurring || true,
      specificDate: newSlot.specificDate || null,
    };
    
    setSlots([...slots, slot]);
    setNewSlot({
      dayOfWeek: 1,
      startTime: '09:00',
      endTime: '10:00',
      isRecurring: true,
      specificDate: null,
    });
    setIsAddingSlot(false);
  };

  const handleRemoveSlot = (index: number) => {
    setSlots(slots.filter((_, i) => i !== index));
  };

  const formatTime = (timeString: string) => {
    const [hours, minutes] = timeString.split(':');
    const hour = parseInt(hours, 10);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const hour12 = hour % 12 || 12;
    return `${hour12}:${minutes} ${ampm}`;
  };

  const getWeekDays = () => {
    const days = [];
    const startOfWeek = new Date(currentDate);
    startOfWeek.setDate(currentDate.getDate() - currentDate.getDay()); // Start from Sunday
    
    for (let i = 0; i < 7; i++) {
      const day = new Date(startOfWeek);
      day.setDate(startOfWeek.getDate() + i);
      days.push(day);
    }
    
    return days;
  };

  const weekDays = getWeekDays();

  const getSlotsByDay = (day: number) => {
    return slots.filter(slot => 
      slot.isRecurring && slot.dayOfWeek === day
    );
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Availability</h3>
        <div className="flex items-center space-x-2">
          {!isEditing && (
            <div className="flex border border-gray-300 dark:border-gray-600 rounded-md overflow-hidden">
              <button
                onClick={() => setView('week')}
                className={`px-3 py-1 text-sm ${
                  view === 'week'
                    ? 'bg-blue-500 text-white'
                    : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300'
                }`}
              >
                Week
              </button>
              <button
                onClick={() => setView('month')}
                className={`px-3 py-1 text-sm ${
                  view === 'month'
                    ? 'bg-blue-500 text-white'
                    : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300'
                }`}
              >
                Month
              </button>
            </div>
          )}
          {isEditable && !isEditing && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleEdit}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
            >
              <Pencil className="h-4 w-4 mr-2" />
              Edit
            </Button>
          )}
        </div>
      </div>

      {isEditing ? (
        <div className="space-y-4">
          {!isAddingSlot ? (
            <Button 
              onClick={() => setIsAddingSlot(true)}
              className="w-full"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Availability Slot
            </Button>
          ) : (
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Day of Week
                </label>
                <select
                  value={newSlot.dayOfWeek}
                  onChange={(e) => setNewSlot({ ...newSlot, dayOfWeek: parseInt(e.target.value) })}
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                >
                  {dayNames.map((day, index) => (
                    <option key={index} value={index}>
                      {day}
                    </option>
                  ))}
                </select>
              </div>
              
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Start Time
                  </label>
                  <input
                    type="time"
                    value={newSlot.startTime}
                    onChange={(e) => setNewSlot({ ...newSlot, startTime: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    End Time
                  </label>
                  <input
                    type="time"
                    value={newSlot.endTime}
                    onChange={(e) => setNewSlot({ ...newSlot, endTime: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                  />
                </div>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isRecurring"
                  checked={newSlot.isRecurring}
                  onChange={(e) => setNewSlot({ ...newSlot, isRecurring: e.target.checked })}
                  className="h-4 w-4 rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500 bg-white dark:bg-gray-700"
                />
                <label htmlFor="isRecurring" className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                  Recurring weekly
                </label>
              </div>
              
              {!newSlot.isRecurring && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Specific Date
                  </label>
                  <input
                    type="date"
                    value={newSlot.specificDate || ''}
                    onChange={(e) => setNewSlot({ ...newSlot, specificDate: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                  />
                </div>
              )}
              
              <div className="flex space-x-2">
                <Button 
                  onClick={handleAddSlot} 
                  disabled={!newSlot.startTime || !newSlot.endTime}
                >
                  Add Slot
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => {
                    setNewSlot({
                      dayOfWeek: 1,
                      startTime: '09:00',
                      endTime: '10:00',
                      isRecurring: true,
                      specificDate: null,
                    });
                    setIsAddingSlot(false);
                  }}
                >
                  Cancel
                </Button>
              </div>
            </div>
          )}

          <div className="mt-6 space-y-4">
            <h4 className="font-medium text-gray-900 dark:text-white">Your Availability Slots</h4>
            {slots.length > 0 ? (
              <div className="space-y-2">
                {slots.map((slot, index) => (
                  <div 
                    key={index} 
                    className="flex items-center justify-between bg-gray-100 dark:bg-gray-700 p-3 rounded-lg"
                  >
                    <div>
                      <div className="font-medium text-gray-800 dark:text-gray-200">
                        {slot.isRecurring ? dayNames[slot.dayOfWeek] : new Date(slot.specificDate!).toLocaleDateString()}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        {formatTime(slot.startTime)} - {formatTime(slot.endTime)}
                      </div>
                      {slot.isRecurring && (
                        <div className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                          Recurring weekly
                        </div>
                      )}
                    </div>
                    <button
                      onClick={() => handleRemoveSlot(index)}
                      className="text-gray-500 hover:text-red-500 dark:text-gray-400 dark:hover:text-red-400"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 dark:text-gray-400 italic">
                No availability slots added yet. Add slots to show when you're available to teach.
              </p>
            )}
          </div>

          <div className="flex space-x-2">
            <Button onClick={handleSave} disabled={isSaving}>
              {isSaving ? 'Saving...' : 'Save'}
            </Button>
            <Button variant="outline" onClick={handleCancel} disabled={isSaving}>
              Cancel
            </Button>
          </div>
        </div>
      ) : (
        <div>
          {view === 'week' ? (
            <div className="overflow-x-auto">
              <div className="grid grid-cols-7 gap-2 min-w-[700px]">
                {weekDays.map((day, index) => (
                  <div key={index} className="text-center">
                    <div className="font-medium text-gray-800 dark:text-gray-200 mb-1">
                      {shortDayNames[day.getDay()]}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400 mb-2">
                      {day.getDate()}
                    </div>
                    <div className="space-y-1">
                      {getSlotsByDay(day.getDay()).map((slot, slotIndex) => (
                        <div 
                          key={slotIndex}
                          className="bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 text-xs p-1 rounded"
                        >
                          {formatTime(slot.startTime)} - {formatTime(slot.endTime)}
                        </div>
                      ))}
                      {getSlotsByDay(day.getDay()).length === 0 && (
                        <div className="text-xs text-gray-400 dark:text-gray-500 py-1">
                          Not available
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center py-8">
              <CalendarIcon className="h-16 w-16 text-gray-300 dark:text-gray-600" />
              <p className="ml-4 text-gray-500 dark:text-gray-400">
                Month view coming soon
              </p>
            </div>
          )}

          {slots.length === 0 && (
            <p className="text-center text-gray-500 dark:text-gray-400 italic mt-4">
              No availability information provided yet.
              {isEditable && ' Click Edit to add your teaching availability.'}
            </p>
          )}
        </div>
      )}
    </div>
  );
}
