-- Migration to fix the global messaging system
-- This migration simplifies the schema and adds real-time capabilities

-- 1. First, let's back up the existing data
CREATE TABLE IF NOT EXISTS conversations_backup AS SELECT * FROM conversations;
CREATE TABLE IF NOT EXISTS conversation_participants_backup AS SELECT * FROM conversation_participants;
CREATE TABLE IF NOT EXISTS direct_messages_backup AS SELECT * FROM direct_messages;

-- 2. Drop existing triggers and functions
DROP TRIGGER IF EXISTS on_new_direct_message ON direct_messages;
DROP TRIGGER IF EXISTS on_new_direct_message_notification ON direct_messages;
DROP FUNCTION IF EXISTS update_conversation_on_new_message();
DROP FUNCTION IF EXISTS create_direct_message_notification();
DROP FUNCTION IF EXISTS get_or_create_conversation(UUID, UUID);
DROP FUNCTION IF EXISTS accept_conversation_request(UUID, UUID);
DROP FUNCTION IF EXISTS reject_conversation_request(UUID, UUID);
DROP FUNCTION IF EXISTS end_conversation(U<PERSON><PERSON>, UUID);
DROP FUNCTION IF EXISTS delete_conversation(UUID, UUID);

-- 3. Drop existing policies
DROP POLICY IF EXISTS "Users can view their own conversations" ON conversations;
DROP POLICY IF EXISTS "Users can insert conversations" ON conversations;
DROP POLICY IF EXISTS "Users can update their own conversations" ON conversations;
DROP POLICY IF EXISTS "Users can delete their own conversations" ON conversations;

DROP POLICY IF EXISTS "Users can view their own conversation participants" ON conversation_participants;
DROP POLICY IF EXISTS "Users can insert their own conversation participants" ON conversation_participants;
DROP POLICY IF EXISTS "Users can update their own conversation participant record" ON conversation_participants;
DROP POLICY IF EXISTS "Users can delete their own conversation participants" ON conversation_participants;

DROP POLICY IF EXISTS "Users can view messages in conversations they are participants in" ON direct_messages;
DROP POLICY IF EXISTS "Users can insert messages in conversations they are participants in" ON direct_messages;
DROP POLICY IF EXISTS "Users can update their own messages" ON direct_messages;
DROP POLICY IF EXISTS "Users can delete their own messages" ON direct_messages;

DROP POLICY IF EXISTS "Direct messages are viewable by participants" ON direct_messages;
DROP POLICY IF EXISTS "Direct messages can be inserted by participants" ON direct_messages;
DROP POLICY IF EXISTS "Direct messages can be updated by sender" ON direct_messages;
DROP POLICY IF EXISTS "Direct messages can be deleted by sender" ON direct_messages;

-- 4. Drop and recreate the tables with a simplified schema
DROP TABLE IF EXISTS direct_messages;
DROP TABLE IF EXISTS conversation_participants;
DROP TABLE IF EXISTS conversations;

-- Create simplified conversations table
CREATE TABLE conversations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user1_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  user2_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  status TEXT NOT NULL DEFAULT 'active', -- 'active' or 'archived'
  last_message_preview TEXT,
  last_message_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user1_id, user2_id)
);

-- Create simplified direct_messages table
CREATE TABLE direct_messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
  sender_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  is_read BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Create new functions for conversation management
-- Function to get or create a conversation between two users
CREATE OR REPLACE FUNCTION get_or_create_conversation(user1_id UUID, user2_id UUID)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  existing_conversation_id UUID;
  new_conversation_id UUID;
BEGIN
  -- Check if a conversation already exists between these users
  SELECT c.id INTO existing_conversation_id
  FROM conversations c
  WHERE (c.user1_id = user1_id AND c.user2_id = user2_id)
     OR (c.user1_id = user2_id AND c.user2_id = user1_id);
  
  -- If a conversation exists, return it
  IF existing_conversation_id IS NOT NULL THEN
    RETURN existing_conversation_id;
  END IF;
  
  -- Otherwise, create a new conversation
  INSERT INTO conversations (user1_id, user2_id, created_at, updated_at)
  VALUES (user1_id, user2_id, NOW(), NOW())
  RETURNING id INTO new_conversation_id;
  
  -- Create a notification for the recipient
  PERFORM create_notification(
    user2_id,
    'conversation_new',
    'New conversation',
    'Someone started a conversation with you',
    '/dashboard/messages?id=' || new_conversation_id
  );
  
  RETURN new_conversation_id;
END;
$$;

-- Function to archive a conversation
CREATE OR REPLACE FUNCTION archive_conversation(conversation_id UUID, user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  is_participant BOOLEAN;
BEGIN
  -- Check if the user is a participant in the conversation
  SELECT EXISTS (
    SELECT 1 FROM conversations
    WHERE id = conversation_id
    AND (user1_id = user_id OR user2_id = user_id)
  ) INTO is_participant;
  
  IF NOT is_participant THEN
    RAISE EXCEPTION 'User is not a participant in this conversation';
  END IF;
  
  -- Update the conversation status to archived
  UPDATE conversations
  SET status = 'archived'
  WHERE id = conversation_id;
  
  RETURN TRUE;
END;
$$;

-- 6. Create triggers for real-time updates
-- Trigger to update conversation when a new message is sent
CREATE OR REPLACE FUNCTION update_conversation_on_new_message()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
  -- Update the conversation with the new message preview and timestamp
  UPDATE conversations
  SET 
    last_message_preview = substring(NEW.content from 1 for 100),
    last_message_at = NEW.created_at,
    updated_at = NOW()
  WHERE id = NEW.conversation_id;
  
  RETURN NEW;
END;
$$;

-- Create the trigger
CREATE TRIGGER on_new_direct_message
AFTER INSERT ON direct_messages
FOR EACH ROW
EXECUTE FUNCTION update_conversation_on_new_message();

-- Trigger to create a notification when a new message is sent
CREATE OR REPLACE FUNCTION create_message_notification()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
DECLARE
  conversation_record RECORD;
  recipient_id UUID;
  sender_name TEXT;
  unread_count INTEGER;
BEGIN
  -- Get the conversation
  SELECT * INTO conversation_record FROM conversations WHERE id = NEW.conversation_id;
  
  -- Determine the recipient
  IF NEW.sender_id = conversation_record.user1_id THEN
    recipient_id := conversation_record.user2_id;
  ELSE
    recipient_id := conversation_record.user1_id;
  END IF;
  
  -- Get the sender's name
  SELECT display_name INTO sender_name FROM profiles WHERE id = NEW.sender_id;
  
  -- Count unread messages for badge
  SELECT COUNT(*) INTO unread_count
  FROM direct_messages
  WHERE conversation_id = NEW.conversation_id
  AND sender_id != recipient_id
  AND is_read = FALSE;
  
  -- Create a notification for the recipient
  PERFORM create_notification(
    recipient_id,
    'direct_message',
    'New message from ' || COALESCE(sender_name, 'Unknown User'),
    substring(NEW.content from 1 for 100),
    '/dashboard/messages?id=' || NEW.conversation_id,
    unread_count
  );
  
  RETURN NEW;
END;
$$;

-- Create the trigger
CREATE TRIGGER on_new_message_notification
AFTER INSERT ON direct_messages
FOR EACH ROW
EXECUTE FUNCTION create_message_notification();

-- 7. Set up RLS policies
ALTER TABLE conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE direct_messages ENABLE ROW LEVEL SECURITY;

-- Conversations policies
CREATE POLICY "Users can view their own conversations"
ON conversations
FOR SELECT
USING (user1_id = auth.uid() OR user2_id = auth.uid());

CREATE POLICY "Users can insert conversations"
ON conversations
FOR INSERT
WITH CHECK (user1_id = auth.uid() OR user2_id = auth.uid());

CREATE POLICY "Users can update their own conversations"
ON conversations
FOR UPDATE
USING (user1_id = auth.uid() OR user2_id = auth.uid());

-- Direct messages policies
CREATE POLICY "Users can view messages in their conversations"
ON direct_messages
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM conversations
    WHERE conversations.id = direct_messages.conversation_id
    AND (conversations.user1_id = auth.uid() OR conversations.user2_id = auth.uid())
  )
);

CREATE POLICY "Users can insert messages in their conversations"
ON direct_messages
FOR INSERT
WITH CHECK (
  sender_id = auth.uid() AND
  EXISTS (
    SELECT 1 FROM conversations
    WHERE conversations.id = direct_messages.conversation_id
    AND (conversations.user1_id = auth.uid() OR conversations.user2_id = auth.uid())
    AND conversations.status = 'active'
  )
);

-- 8. Create function to mark messages as read
CREATE OR REPLACE FUNCTION mark_conversation_messages_read(conversation_id_param UUID, user_id_param UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  is_participant BOOLEAN;
BEGIN
  -- Check if the user is a participant in the conversation
  SELECT EXISTS (
    SELECT 1 FROM conversations
    WHERE id = conversation_id_param
    AND (user1_id = user_id_param OR user2_id = user_id_param)
  ) INTO is_participant;
  
  IF NOT is_participant THEN
    RAISE EXCEPTION 'User is not a participant in this conversation';
  END IF;
  
  -- Mark all messages from the other user as read
  UPDATE direct_messages
  SET is_read = TRUE
  WHERE conversation_id = conversation_id_param
  AND sender_id != user_id_param
  AND is_read = FALSE;
  
  RETURN TRUE;
END;
$$;

-- 9. Create function to get unread message count
CREATE OR REPLACE FUNCTION get_unread_direct_message_count(user_id_param UUID)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  count_result INTEGER;
BEGIN
  SELECT COUNT(*) INTO count_result
  FROM direct_messages dm
  JOIN conversations c ON c.id = dm.conversation_id
  WHERE (c.user1_id = user_id_param OR c.user2_id = user_id_param)
  AND dm.sender_id != user_id_param
  AND dm.is_read = FALSE;
  
  RETURN count_result;
END;
$$;
