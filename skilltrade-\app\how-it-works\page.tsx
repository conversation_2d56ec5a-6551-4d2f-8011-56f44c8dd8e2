import Link from 'next/link';
import { Metadata } from 'next';
import MainHeader from '@/components/MainHeader';
import MainFooter from '@/components/MainFooter';

export const metadata: Metadata = {
  title: 'How It Works - Skilltrade',
  description: 'Learn how Skilltrade works as a community time-bank where you can teach skills to earn credits and learn new skills from others.',
  keywords: ['how it works', 'time bank', 'skill exchange', 'community learning'],
};

export default function HowItWorksPage() {
  return (
    <div className="min-h-screen bg-gray-950">
      <MainHeader />

      <main>
        {/* Hero Section */}
        <section className="py-16 bg-gradient-to-b from-gray-900 to-gray-950">
          <div className="container mx-auto px-4 text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">How Skilltrade Works</h1>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Skilltrade is a community "time-bank" where the currency isn't dollars but hours of help.
              Everyone brings something they know how to do, and in exchange they earn time to learn from someone else.
            </p>
          </div>
        </section>

        {/* Step-by-Step Guide */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-3xl font-bold mb-12 text-center">Your Journey with Skilltrade</h2>

              <div className="space-y-16">
                {/* Step 1 */}
                <div className="flex flex-col md:flex-row items-center gap-8">
                  <div className="md:w-1/3 flex justify-center">
                    <div className="w-24 h-24 bg-blue-600 rounded-full flex items-center justify-center text-3xl font-bold">
                      1
                    </div>
                  </div>
                  <div className="md:w-2/3">
                    <h3 className="text-2xl font-semibold mb-4">Creating Your Account & Profile</h3>
                    <ul className="space-y-3 text-gray-300">
                      <li className="flex items-start">
                        <span className="text-blue-400 mr-2">•</span>
                        <span>Sign up with your email</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-400 mr-2">•</span>
                        <span>Build your profile with a friendly photo and short bio</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-400 mr-2">•</span>
                        <span>Add your hobbies and interests</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-400 mr-2">•</span>
                        <span>Start with 1 hour of time credit</span>
                      </li>
                    </ul>
                  </div>
                </div>

                {/* Step 2 */}
                <div className="flex flex-col md:flex-row items-center gap-8">
                  <div className="md:w-1/3 flex justify-center md:order-last">
                    <div className="w-24 h-24 bg-blue-600 rounded-full flex items-center justify-center text-3xl font-bold">
                      2
                    </div>
                  </div>
                  <div className="md:w-2/3">
                    <h3 className="text-2xl font-semibold mb-4">Offering a Skill ("Teaching")</h3>
                    <ul className="space-y-3 text-gray-300">
                      <li className="flex items-start">
                        <span className="text-blue-400 mr-2">•</span>
                        <span>Decide what you can teach—e.g. "Beginner German conversation"</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-400 mr-2">•</span>
                        <span>Post that skill with a title, description, and tags</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-400 mr-2">•</span>
                        <span>Your listing goes live for other members to find</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-400 mr-2">•</span>
                        <span>Each hour you teach earns you one hour of credit</span>
                      </li>
                    </ul>
                  </div>
                </div>

                {/* Step 3 */}
                <div className="flex flex-col md:flex-row items-center gap-8">
                  <div className="md:w-1/3 flex justify-center">
                    <div className="w-24 h-24 bg-blue-600 rounded-full flex items-center justify-center text-3xl font-bold">
                      3
                    </div>
                  </div>
                  <div className="md:w-2/3">
                    <h3 className="text-2xl font-semibold mb-4">Finding a Skill to Learn ("Booking")</h3>
                    <ul className="space-y-3 text-gray-300">
                      <li className="flex items-start">
                        <span className="text-blue-400 mr-2">•</span>
                        <span>Browse available skills or search for a specific topic</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-400 mr-2">•</span>
                        <span>When you find something you'd like to learn, click "Book Session"</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-400 mr-2">•</span>
                        <span>Pick a time that fits both you and the teacher</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-400 mr-2">•</span>
                        <span>You need enough time-credits to book a session</span>
                      </li>
                    </ul>
                  </div>
                </div>

                {/* Step 4 */}
                <div className="flex flex-col md:flex-row items-center gap-8">
                  <div className="md:w-1/3 flex justify-center md:order-last">
                    <div className="w-24 h-24 bg-blue-600 rounded-full flex items-center justify-center text-3xl font-bold">
                      4
                    </div>
                  </div>
                  <div className="md:w-2/3">
                    <h3 className="text-2xl font-semibold mb-4">Conducting a Session</h3>
                    <ul className="space-y-3 text-gray-300">
                      <li className="flex items-start">
                        <span className="text-blue-400 mr-2">•</span>
                        <span>Sessions can happen online (Zoom, Discord, Teams) or in person</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-400 mr-2">•</span>
                        <span>The duration is fixed (e.g. 1 hour)</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-400 mr-2">•</span>
                        <span>Both teacher and learner attend and spend that hour teaching or learning</span>
                      </li>
                    </ul>
                  </div>
                </div>

                {/* Step 5 */}
                <div className="flex flex-col md:flex-row items-center gap-8">
                  <div className="md:w-1/3 flex justify-center">
                    <div className="w-24 h-24 bg-blue-600 rounded-full flex items-center justify-center text-3xl font-bold">
                      5
                    </div>
                  </div>
                  <div className="md:w-2/3">
                    <h3 className="text-2xl font-semibold mb-4">Session Completion & Reviews</h3>
                    <ul className="space-y-3 text-gray-300">
                      <li className="flex items-start">
                        <span className="text-blue-400 mr-2">•</span>
                        <span>Both teacher and learner must mark the session as complete</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-400 mr-2">•</span>
                        <span>After completion, the learner rates the session on a 5-star scale</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-400 mr-2">•</span>
                        <span>Ratings of 3-5 stars are considered positive, and the teacher earns +1 hour</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-400 mr-2">•</span>
                        <span>Ratings below 3 stars trigger a dispute that can be resolved by both parties</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-400 mr-2">•</span>
                        <span>Reviews and ratings help maintain quality and build trust in the community</span>
                      </li>
                    </ul>
                  </div>
                </div>

                {/* Step 6 */}
                <div className="flex flex-col md:flex-row items-center gap-8 mt-16">
                  <div className="md:w-1/3 flex justify-center md:order-last">
                    <div className="w-24 h-24 bg-blue-600 rounded-full flex items-center justify-center text-3xl font-bold">
                      6
                    </div>
                  </div>
                  <div className="md:w-2/3">
                    <h3 className="text-2xl font-semibold mb-4">Communication & Notifications</h3>
                    <ul className="space-y-3 text-gray-300">
                      <li className="flex items-start">
                        <span className="text-blue-400 mr-2">•</span>
                        <span>Use in-app messaging to communicate with your teacher or learner</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-400 mr-2">•</span>
                        <span>Chat is available at all stages: before, during, and after sessions</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-400 mr-2">•</span>
                        <span>Receive notifications for new messages, session updates, and reviews</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-400 mr-2">•</span>
                        <span>If a session is disputed, use the chat to resolve it directly with the other person</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-400 mr-2">•</span>
                        <span>All communication stays on the platform for transparency and safety</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-16 bg-gray-900">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold mb-12 text-center">Benefits of Skilltrade</h2>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              <div className="bg-gray-800 p-6 rounded-lg">
                <div className="w-12 h-12 bg-blue-600/20 rounded-lg flex items-center justify-center mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold mb-3">Cost-free Learning</h3>
                <p className="text-gray-300">
                  No money changes hands—just time. Learn new skills without financial barriers.
                </p>
              </div>

              <div className="bg-gray-800 p-6 rounded-lg">
                <div className="w-12 h-12 bg-blue-600/20 rounded-lg flex items-center justify-center mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold mb-3">Community Building</h3>
                <p className="text-gray-300">
                  Meet fellow enthusiasts and build relationships with people who share your interests.
                </p>
              </div>

              <div className="bg-gray-800 p-6 rounded-lg">
                <div className="w-12 h-12 bg-blue-600/20 rounded-lg flex items-center justify-center mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold mb-3">Fair Exchange</h3>
                <p className="text-gray-300">
                  Everyone's contributions are valued equally by the hour, creating a balanced system.
                </p>
              </div>

              <div className="bg-gray-800 p-6 rounded-lg">
                <div className="w-12 h-12 bg-blue-600/20 rounded-lg flex items-center justify-center mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold mb-3">Skill Diversity</h3>
                <p className="text-gray-300">
                  From languages to crafts, coding to cooking—you'll find a wide range of skills to learn.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Example Journey */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="max-w-3xl mx-auto">
              <h2 className="text-3xl font-bold mb-8 text-center">Example Journey</h2>

              <div className="bg-gray-800 p-8 rounded-lg">
                <ol className="space-y-6 relative border-l border-gray-700 pl-8">
                  <li className="relative">
                    <div className="absolute -left-4 mt-1.5 h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center">
                      <span className="text-sm font-bold">1</span>
                    </div>
                    <p className="text-gray-300">
                      <span className="font-semibold text-white">Anna</span> signs up and lists "Yoga basics" as her skill.
                    </p>
                  </li>

                  <li className="relative">
                    <div className="absolute -left-4 mt-1.5 h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center">
                      <span className="text-sm font-bold">2</span>
                    </div>
                    <p className="text-gray-300">
                      <span className="font-semibold text-white">Ben</span> has 0 credits, so he first teaches "Intro to Guitar" to Cara.
                    </p>
                  </li>

                  <li className="relative">
                    <div className="absolute -left-4 mt-1.5 h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center">
                      <span className="text-sm font-bold">3</span>
                    </div>
                    <p className="text-gray-300">
                      After the session, both <span className="font-semibold text-white">Ben</span> and <span className="font-semibold text-white">Cara</span> mark it as complete. Cara gives Ben a 5-star review → Ben earns 1 hour.
                    </p>
                  </li>

                  <li className="relative">
                    <div className="absolute -left-4 mt-1.5 h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center">
                      <span className="text-sm font-bold">4</span>
                    </div>
                    <p className="text-gray-300">
                      Now <span className="font-semibold text-white">Ben</span> books Anna's yoga session. They use the in-app chat to coordinate details. After class, both mark it complete and Ben gives Anna 4 stars → Anna +1 hour, Ben -1 hour.
                    </p>
                  </li>

                  <li className="relative">
                    <div className="absolute -left-4 mt-1.5 h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center">
                      <span className="text-sm font-bold">5</span>
                    </div>
                    <p className="text-gray-300">
                      <span className="font-semibold text-white">Anna</span> can now use her hour to learn "French pronunciation" from David, or save it for later.
                    </p>
                  </li>
                </ol>
              </div>

              <p className="text-center text-gray-300 mt-8">
                By thinking of hours as your "money," Skilltrade makes it simple: you teach to earn, and you learn by spending—all backed by clear reviews and a friendly community.
              </p>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-gradient-to-r from-blue-900/40 to-purple-900/40">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl font-bold mb-6">Ready to Start Your Skilltrade Journey?</h2>
            <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
              Join our community today and start sharing your knowledge while learning new skills.
            </p>
            <Link
              href="/signup"
              className="px-8 py-4 rounded-md bg-blue-600 hover:bg-blue-700 transition text-lg font-medium inline-block"
            >
              Sign Up Now
            </Link>
          </div>
        </section>
      </main>

      <MainFooter />
    </div>
  );
}
