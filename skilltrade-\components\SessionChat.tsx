'use client';

import { useState, useEffect, useRef } from 'react';
import { createClientSide } from '@/lib/supabase';
import { User } from '@supabase/supabase-js';
import { formatDistanceToNow } from 'date-fns';
import Image from 'next/image';
import { validateText, censorText } from '@/lib/bad-word-filter';

interface Message {
  id: string;
  session_id: string;
  sender_id: string;
  content: string;
  created_at: string;
  sender: {
    display_name: string | null;
    avatar_url: string | null;
  };
}

interface SessionChatProps {
  sessionId: string;
  teacherId: string;
  learnerId: string;
  currentUser: User;
}

export default function SessionChat({ sessionId, teacherId, learnerId, currentUser }: SessionChatProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const supabase = createClientSide();

  // Fetch messages on component mount
  useEffect(() => {
    fetchMessages();

    // Set up real-time subscription
    const channel = supabase
      .channel(`session_messages:${sessionId}`)
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'session_messages',
        filter: `session_id=eq.${sessionId}`
      }, (payload) => {
        // Fetch the new message with sender info
        fetchNewMessage(payload.new.id);
      })
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [sessionId]);

  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const fetchMessages = async () => {
    try {
      setLoading(true);

      // Use the server-side API endpoint to fetch messages
      const response = await fetch(`/api/sessions/messages?session_id=${sessionId}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to load messages');
      }

      const data = await response.json();
      setMessages(data.messages || []);
    } catch (error: any) {
      setError(error.message || 'Failed to load messages');
    } finally {
      setLoading(false);
    }
  };

  const fetchNewMessage = async (messageId: string) => {
    try {
      const { data, error } = await supabase
        .from('session_messages')
        .select(`
          *,
          sender:profiles(display_name, avatar_url)
        `)
        .eq('id', messageId)
        .single();

      if (error) {
        throw error;
      }

      if (data) {
        // Check if the message already exists in the state to avoid duplicates
        setMessages(prev => {
          // If the message already exists, don't add it again
          if (prev.some(msg => msg.id === data.id)) {
            return prev;
          }
          return [...prev, data];
        });
      }
    } catch (error: any) {
      console.error('Error fetching new message:', error);
    }
  };

  const sendMessage = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newMessage.trim()) return;

    try {
      setSending(true);
      setError(null);

      const messageContent = newMessage.trim();

      // Use the server-side API endpoint for message creation with bad word filtering
      const response = await fetch('/api/sessions/messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          session_id: sessionId,
          content: messageContent,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to send message');
      }

      const data = await response.json();
      const insertedMessage = data.message;

      // If the message was censored, show a warning
      if (data.censored) {
        setError(`Your message contained inappropriate language (${data.badWords.join(', ')}) and has been censored.`);
      }

      // Get the current user's profile info
      const { data: senderProfile, error: profileError } = await supabase
        .from('profiles')
        .select('display_name, avatar_url')
        .eq('id', currentUser.id)
        .single();

      if (profileError) {
        console.error('Error fetching sender profile:', profileError);
      }

      // Add the message to the UI immediately without waiting for the real-time subscription
      if (insertedMessage) {
        const newMessageWithSender = {
          ...insertedMessage,
          sender: {
            display_name: senderProfile?.display_name || null,
            avatar_url: senderProfile?.avatar_url || null
          }
        };

        setMessages(prev => [...prev, newMessageWithSender]);
      }

      // Note: We're relying on the database trigger to create the notification
      // No need to manually create a notification here

      setNewMessage('');

      // If we're showing a censorship warning, keep it visible for a few seconds then clear it
      if (data.censored) {
        setTimeout(() => {
          setError(null);
        }, 5000);
      }
    } catch (error: any) {
      setError(error.message || 'Failed to send message');
    } finally {
      setSending(false);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const isCurrentUser = (senderId: string) => {
    return senderId === currentUser.id;
  };

  const formatMessageTime = (timestamp: string) => {
    return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden flex flex-col h-[500px]">
      <div className="p-4 bg-gray-100 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Session Chat</h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Chat with the {currentUser.id === teacherId ? 'learner' : 'teacher'} about this session
        </p>
      </div>

      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {loading ? (
          <div className="flex justify-center items-center h-full">
            <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
            <p className="ml-2 text-gray-600 dark:text-gray-400">Loading messages...</p>
          </div>
        ) : messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-gray-500 dark:text-gray-400">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
            <p className="text-center">No messages yet. Start the conversation!</p>
          </div>
        ) : (
          <>
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${isCurrentUser(message.sender_id) ? 'justify-end' : 'justify-start'} mb-4`}
              >
                {!isCurrentUser(message.sender_id) && (
                  <div className="flex-shrink-0 mr-2">
                    {message.sender.avatar_url ? (
                      <div className="w-8 h-8 rounded-full overflow-hidden">
                        <Image
                          src={message.sender.avatar_url}
                          alt={message.sender.display_name || 'User'}
                          width={32}
                          height={32}
                          className="object-cover w-full h-full"
                        />
                      </div>
                    ) : (
                      <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                        <span className="text-xs text-gray-600 dark:text-gray-300">
                          {(message.sender.display_name || 'U').charAt(0)}
                        </span>
                      </div>
                    )}
                  </div>
                )}
                <div
                  className={`max-w-[80%] rounded-lg p-3 ${
                    isCurrentUser(message.sender_id)
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
                  }`}
                >
                  <div className="flex items-center mb-1">
                    <span className={`text-xs font-medium ${isCurrentUser(message.sender_id) ? 'text-blue-200' : 'text-gray-500 dark:text-gray-400'}`}>
                      {isCurrentUser(message.sender_id) ? 'You' : message.sender.display_name || 'User'}
                    </span>
                    <span className={`text-xs ml-2 ${isCurrentUser(message.sender_id) ? 'text-blue-200' : 'text-gray-500 dark:text-gray-400'}`}>
                      {formatMessageTime(message.created_at)}
                    </span>
                  </div>
                  <p className="whitespace-pre-line">{message.content}</p>
                </div>
                {isCurrentUser(message.sender_id) && (
                  <div className="flex-shrink-0 ml-2">
                    {message.sender.avatar_url ? (
                      <div className="w-8 h-8 rounded-full overflow-hidden">
                        <Image
                          src={message.sender.avatar_url}
                          width={32}
                          height={32}
                          alt="You"
                          className="object-cover w-full h-full"
                        />
                      </div>
                    ) : (
                      <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                        <span className="text-xs text-white">
                          {(message.sender.display_name || 'Y').charAt(0)}
                        </span>
                      </div>
                    )}
                  </div>
                )}
              </div>
            ))}
            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      <form onSubmit={sendMessage} className="p-4 border-t border-gray-200 dark:border-gray-600">
        {error && (
          <div className="mb-3 p-2 text-sm bg-red-100 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 rounded-lg">
            {error}
          </div>
        )}
        <div className="flex">
          <input
            type="text"
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            placeholder="Type your message..."
            className="flex-1 px-4 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={sending}
          />
          <button
            type="submit"
            className="px-4 py-2 bg-blue-600 text-white rounded-r-lg hover:bg-blue-700 transition disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={sending || !newMessage.trim()}
          >
            {sending ? (
              <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            ) : (
              <span>Send</span>
            )}
          </button>
        </div>
      </form>
    </div>
  );
}
