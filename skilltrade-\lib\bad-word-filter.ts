/**
 * Bad Word Filter Utility for Skilltrade
 * 
 * This utility provides functions to detect and censor bad words in text.
 * It can be used for validating skill titles, descriptions, and chat messages.
 */

// List of bad words to filter
// This is a small sample list - in a production environment, you would want a more comprehensive list
// You might also consider using a third-party library or API for more robust filtering
const BAD_WORDS: string[] = [
  'fuck', 'shit', 'ass', 'bitch', 'dick', 'pussy', 'cock', 'cunt', 'whore',
  'bastard', 'damn', 'asshole', 'piss', 'slut', 'tits', 'wanker', 'twat',
  // Add more words as needed
];

// Interface for validation results
export interface ValidationResult {
  isValid: boolean;
  badWords: string[];
  censoredText: string;
}

/**
 * Check if text contains any bad words
 * @param text The text to check
 * @returns True if the text contains bad words, false otherwise
 */
export function containsBadWords(text: string): boolean {
  if (!text) return false;
  
  const lowerText = text.toLowerCase();
  
  // Check if any bad word is found in the text
  return BAD_WORDS.some(word => {
    // Create a regex that matches the word as a whole word (not as part of another word)
    // This prevents false positives like 'class' matching 'ass'
    const regex = new RegExp(`\\b${word}\\b`, 'i');
    return regex.test(lowerText);
  });
}

/**
 * Find all bad words in the text
 * @param text The text to check
 * @returns Array of bad words found in the text
 */
export function findBadWords(text: string): string[] {
  if (!text) return [];
  
  const lowerText = text.toLowerCase();
  
  // Find all bad words in the text
  return BAD_WORDS.filter(word => {
    const regex = new RegExp(`\\b${word}\\b`, 'i');
    return regex.test(lowerText);
  });
}

/**
 * Censor bad words in text by replacing them with asterisks
 * @param text The text to censor
 * @returns The censored text
 */
export function censorText(text: string): string {
  if (!text) return text;
  
  let censoredText = text;
  
  // Replace each bad word with asterisks
  BAD_WORDS.forEach(word => {
    // Create a regex that matches the word as a whole word
    const regex = new RegExp(`\\b${word}\\b`, 'gi');
    
    // Replace the word with asterisks of the same length
    censoredText = censoredText.replace(regex, '*'.repeat(word.length));
  });
  
  return censoredText;
}

/**
 * Validate text for bad words and return validation results
 * @param text The text to validate
 * @returns Validation results including whether the text is valid, 
 *          which bad words were found, and a censored version of the text
 */
export function validateText(text: string): ValidationResult {
  if (!text) {
    return {
      isValid: true,
      badWords: [],
      censoredText: text
    };
  }
  
  const badWords = findBadWords(text);
  const isValid = badWords.length === 0;
  const censoredText = censorText(text);
  
  return {
    isValid,
    badWords,
    censoredText
  };
}
