'use client';

import { useState, useEffect } from 'react';
import { createClientSide } from '@/lib/supabase';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import DashboardNav from '@/components/DashboardNav';

interface Review {
  id: string;
  rating: number;
  comment: string | null;
  created_at: string;
  reviewer: {
    display_name: string | null;
  };
}

interface Session {
  id: string;
  scheduled_at: string;
  status: string;
}

interface AvailableDate {
  id: string;
  date_time: string;
  duration_hours: number;
  is_booked: boolean;
}

interface Skill {
  id: string;
  title: string;
  description: string | null;
  tags: string[];
  is_active: boolean;
  created_at: string;
  reviews?: Review[];
  sessions?: Session[];
  session_count?: number;
  avg_rating?: number;
  last_session_date?: string | null;
  available_dates?: AvailableDate[];
}

export default function SkillDetailPage({ params }: { params: { id: string } }) {
  const id = params.id;
  const [skill, setSkill] = useState<Skill | null>(null);
  const [loading, setLoading] = useState(true);
  const [deleting, setDeleting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const supabase = createClientSide();

  const handleDeleteSkill = async () => {
    setDeleting(true);
    setError(null);

    try {
      // Use the database function to delete the skill and all its dependencies
      const { data, error } = await supabase
        .rpc('delete_skill_with_dependencies', { skill_id_param: id });

      if (error) {
        throw error;
      }

      // Redirect to skills page
      router.push('/dashboard/skills');
      router.refresh();
    } catch (error: any) {
      setError(error.message || 'Failed to delete skill');
      setDeleting(false);
      setShowDeleteConfirm(false);
    }
  };

  useEffect(() => {
    const fetchSkill = async () => {
      try {
        setLoading(true);

        // Get the current user
        const { data: { user } } = await supabase.auth.getUser();

        if (!user) {
          router.push('/login');
          return;
        }

        // Get the skill
        const { data: skillData, error: skillError } = await supabase
          .from('skills')
          .select('*')
          .eq('id', id)
          .single();

        if (skillError) {
          throw skillError;
        }

        // Check if the skill belongs to the user
        if (skillData.owner_id !== user.id) {
          router.push('/dashboard/skills');
          return;
        }

        // Get reviews for this skill
        const { data: reviewsData, error: reviewsError } = await supabase
          .from('reviews')
          .select(`
            id,
            rating,
            comment,
            created_at,
            reviewer:profiles!reviews_reviewer_id_fkey(display_name)
          `)
          .eq('session_id', id)
          .order('created_at', { ascending: false });

        if (reviewsError) {
          console.error('Error fetching reviews:', reviewsError);
        }

        // Get sessions for this skill
        const { data: sessionsData, error: sessionsError } = await supabase
          .from('sessions')
          .select(`
            id,
            scheduled_at,
            status
          `)
          .eq('skill_id', id)
          .order('scheduled_at', { ascending: false });

        if (sessionsError) {
          console.error('Error fetching sessions:', sessionsError);
        }

        // Get session count
        const { count: sessionCount, error: countError } = await supabase
          .from('sessions')
          .select('id', { count: 'exact', head: true })
          .eq('skill_id', id);

        if (countError) {
          console.error('Error fetching session count:', countError);
        }

        // Calculate average rating
        let avgRating = 0;
        if (reviewsData && reviewsData.length > 0) {
          const sum = reviewsData.reduce((acc, review) => acc + review.rating, 0);
          avgRating = Math.round((sum / reviewsData.length) * 20); // Convert to percentage
        }

        // Get last session date
        let lastSessionDate = null;
        if (sessionsData && sessionsData.length > 0) {
          lastSessionDate = sessionsData[0].scheduled_at;
        }

        // Get available dates
        const { data: availableDates, error: availableDatesError } = await supabase
          .from('skill_available_dates')
          .select('*')
          .eq('skill_id', id)
          .order('date_time', { ascending: true });

        if (availableDatesError) {
          console.error('Error fetching available dates:', availableDatesError);
        }

        // Create enhanced skill object
        const enhancedSkill = {
          ...skillData,
          reviews: reviewsData || [],
          sessions: sessionsData || [],
          session_count: sessionCount || 0,
          avg_rating: avgRating,
          last_session_date: lastSessionDate,
          available_dates: availableDates || []
        };

        setSkill(enhancedSkill);
      } catch (error: any) {
        setError(error.message || 'Failed to load skill');
      } finally {
        setLoading(false);
      }
    };

    fetchSkill();
  }, [id, router, supabase]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p className="ml-4 text-gray-700 dark:text-gray-300">Loading skill...</p>
      </div>
    );
  }

  if (!skill) {
    return (
      <div className="bg-gray-800 rounded-lg p-8 text-center">
        <h2 className="text-xl font-semibold mb-4">Skill not found</h2>
        <p className="text-gray-300 mb-6">
          The skill you're looking for doesn't exist or you don't have permission to view it.
        </p>
        <Link
          href="/dashboard/skills"
          className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-6 rounded-md transition inline-block"
        >
          Back to My Skills
        </Link>
      </div>
    );
  }

  return (
    <main className="container mx-auto px-4 py-8">
      {error && (
        <div className="bg-red-900/30 border border-red-800 text-red-300 px-4 py-3 rounded-lg mb-6">
          {error}
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full">
            <h3 className="text-xl font-bold mb-4">Delete Skill</h3>
            <p className="mb-6">
              Are you sure you want to delete this skill? This action cannot be undone.
              {skill && skill.available_dates && skill.available_dates.some(date => date.is_booked) && (
                <span className="block mt-2 text-yellow-400">
                  Note: This skill has booked sessions. Deleting it will affect existing bookings.
                </span>
              )}
            </p>
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => setShowDeleteConfirm(false)}
                className="px-4 py-2 bg-gray-700 text-white rounded-md hover:bg-gray-600 transition"
                disabled={deleting}
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleDeleteSkill}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition"
                disabled={deleting}
              >
                {deleting ? 'Deleting...' : 'Delete'}
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="mb-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">{skill.title}</h1>
          <div className="flex space-x-2">
            <button
              onClick={() => setShowDeleteConfirm(true)}
              className="bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-md transition"
            >
              Delete
            </button>
            <Link
              href={`/dashboard/skills/${skill.id}/edit`}
              className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md transition"
            >
              Edit Skill
            </Link>
            <Link
              href="/dashboard/skills"
              className="bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-md transition"
            >
              Back to Skills
            </Link>
          </div>
        </div>
        <div className="mt-2 flex items-center">
          <span className={`px-2 py-1 rounded-full text-xs ${skill.is_active ? 'bg-green-900/50 text-green-300' : 'bg-gray-700 text-gray-300'}`}>
            {skill.is_active ? 'Active' : 'Inactive'}
          </span>
          {skill.reviews && skill.reviews.length > 0 && (
            <div className="ml-2 bg-gray-700 rounded-full px-3 py-1 flex items-center">
              <span className="text-yellow-500 mr-1">★</span>
              <span className="text-xs font-medium">
                {skill.avg_rating}% positive
              </span>
            </div>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <div className="bg-gray-800 rounded-lg p-6 shadow-lg">
            <h2 className="text-xl font-semibold mb-4">Description</h2>
            <p className="text-gray-300 whitespace-pre-wrap">
              {skill.description || 'No description provided'}
            </p>

            {skill.tags && skill.tags.length > 0 && (
              <div className="mt-6">
                <h3 className="text-lg font-semibold mb-2">Tags</h3>
                <div className="flex flex-wrap gap-2">
                  {skill.tags.map((tag, index) => (
                    <span key={index} className="bg-gray-700 px-3 py-1 rounded-full text-sm">
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {skill.available_dates && skill.available_dates.length > 0 && (
              <div className="mt-6">
                <h3 className="text-lg font-semibold mb-2">Available Dates</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {skill.available_dates.map((date) => {
                    const dateObj = new Date(date.date_time);
                    return (
                      <div
                        key={date.id}
                        className={`p-3 rounded-lg border ${date.is_booked ? 'bg-gray-700 border-gray-600' : 'bg-gray-700/50 border-gray-600'}`}
                      >
                        <div className="flex justify-between items-center">
                          <div>
                            <div className="font-medium">
                              {dateObj.toLocaleDateString(undefined, { weekday: 'short', month: 'short', day: 'numeric' })}
                            </div>
                            <div className="text-sm text-gray-400">
                              {dateObj.toLocaleTimeString(undefined, { hour: '2-digit', minute: '2-digit' })}
                              {' • '}
                              {date.duration_hours} {date.duration_hours === 1 ? 'hour' : 'hours'}
                            </div>
                          </div>
                          <span className={`text-xs px-2 py-1 rounded-full ${date.is_booked ? 'bg-yellow-900/50 text-yellow-300' : 'bg-green-900/50 text-green-300'}`}>
                            {date.is_booked ? 'Booked' : 'Available'}
                          </span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
          </div>
        </div>

        <div>
          <div className="bg-gray-800 rounded-lg p-6 shadow-lg mb-6">
            <h2 className="text-xl font-semibold mb-4">Stats</h2>
            <div className="space-y-4">
              <div>
                <div className="text-sm text-gray-400">Total Sessions</div>
                <div className="text-2xl font-bold">{skill.session_count}</div>
              </div>
              <div>
                <div className="text-sm text-gray-400">Last Session</div>
                <div className="text-lg">
                  {skill.last_session_date
                    ? new Date(skill.last_session_date).toLocaleDateString()
                    : 'No sessions yet'}
                </div>
              </div>
              <div>
                <div className="text-sm text-gray-400">Created</div>
                <div className="text-lg">
                  {new Date(skill.created_at).toLocaleDateString()}
                </div>
              </div>
            </div>
          </div>

          {skill.reviews && skill.reviews.length > 0 && (
            <div className="bg-gray-800 rounded-lg p-6 shadow-lg">
              <h2 className="text-xl font-semibold mb-4">Recent Reviews</h2>
              <div className="space-y-4">
                {skill.reviews.slice(0, 3).map((review) => (
                  <div key={review.id} className="border-b border-gray-700 pb-4 last:border-0 last:pb-0">
                    <div className="flex items-center justify-between mb-2">
                      <div className="font-medium">
                        {review.reviewer.display_name || 'Anonymous User'}
                      </div>
                      <div className="flex items-center">
                        <span className="text-yellow-500 mr-1">★</span>
                        <span>{review.rating}/5</span>
                      </div>
                    </div>
                    {review.comment && (
                      <p className="text-gray-300 text-sm">{review.comment}</p>
                    )}
                    <div className="text-xs text-gray-500 mt-1">
                      {new Date(review.created_at).toLocaleDateString()}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </main>
  );
}
