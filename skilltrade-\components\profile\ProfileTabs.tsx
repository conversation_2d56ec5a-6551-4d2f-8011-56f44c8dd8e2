'use client';

import React, { useState } from 'react';
import { cn } from '@/lib/utils';

export type TabItem = {
  id: string;
  label: string;
  icon?: React.ReactNode;
};

interface ProfileTabsProps {
  tabs: TabItem[];
  defaultTab?: string;
  onChange?: (tabId: string) => void;
  className?: string;
}

export function ProfileTabs({
  tabs,
  defaultTab,
  onChange,
  className,
}: ProfileTabsProps) {
  const [activeTab, setActiveTab] = useState<string>(defaultTab || tabs[0]?.id || '');

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    if (onChange) {
      onChange(tabId);
    }
  };

  return (
    <div className={cn('w-full', className)}>
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-4 sm:space-x-8 overflow-x-auto pb-1 scrollbar-none">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => handleTabChange(tab.id)}
              className={cn(
                'py-3 sm:py-4 px-1 border-b-2 font-medium text-xs sm:text-sm whitespace-nowrap flex items-center',
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600'
              )}
              aria-current={activeTab === tab.id ? 'page' : undefined}
            >
              {tab.icon && <span className="mr-2">{tab.icon}</span>}
              {tab.label}
            </button>
          ))}
        </nav>
      </div>
    </div>
  );
}

interface ProfileTabsContentProps {
  tabs: TabItem[];
  activeTab: string;
  children: React.ReactNode;
  className?: string;
}

export function ProfileTabsContent({
  tabs,
  activeTab,
  children,
  className,
}: ProfileTabsContentProps) {
  // Convert children to array to filter them
  const childrenArray = React.Children.toArray(children);

  return (
    <div className={cn('mt-4', className)}>
      {childrenArray.map((child, index) => {
        // Check if the child corresponds to the active tab
        if (index < tabs.length && tabs[index].id === activeTab) {
          return (
            <div key={tabs[index].id} className="animate-in fade-in-50 duration-300">
              {child}
            </div>
          );
        }
        return null;
      })}
    </div>
  );
}

interface ProfileTabProps {
  id: string;
  children: React.ReactNode;
}

export function ProfileTab({ id, children }: ProfileTabProps) {
  return <div id={id}>{children}</div>;
}
