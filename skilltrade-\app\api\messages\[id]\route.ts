import { NextRequest, NextResponse } from 'next/server';
import { createServerSide } from '@/lib/supabase-server';
import { createAdminClient } from '@/lib/supabase-admin';

export const dynamic = 'force-dynamic';

// GET /api/messages/[id] - Get a single message
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createServerSide();

    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const messageId = params.id;
    console.log('Fetching message:', messageId);
    console.log('User ID:', user.id);

    // Use the admin client to bypass RLS
    const adminSupabase = createAdminClient();

    // Get the message with sender info
    const { data: message, error: messageError } = await adminSupabase
      .from('direct_messages')
      .select(`
        *,
        sender:profiles(
          id,
          display_name,
          avatar_url
        )
      `)
      .eq('id', messageId)
      .single();

    if (messageError) {
      console.error('Error fetching message:', messageError);
      return NextResponse.json(
        { error: 'Message not found' },
        { status: 404 }
      );
    }

    // Check if the user is a participant in the conversation
    const { data: participant, error: participantError } = await adminSupabase
      .from('conversation_participants')
      .select('*')
      .eq('conversation_id', message.conversation_id)
      .eq('user_id', user.id)
      .single();

    if (participantError) {
      console.error('Participant check error:', participantError);
      return NextResponse.json(
        { error: 'You are not a participant in this conversation' },
        { status: 403 }
      );
    }

    return NextResponse.json({ message });
  } catch (error: any) {
    console.error('Error fetching message:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch message' },
      { status: 500 }
    );
  }
}
