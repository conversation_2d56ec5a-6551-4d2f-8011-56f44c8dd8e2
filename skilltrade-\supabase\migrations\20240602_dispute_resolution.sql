-- Create dispute_resolutions table
CREATE TABLE IF NOT EXISTS dispute_resolutions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  session_id UUID NOT NULL REFERENCES sessions(id) ON DELETE CASCADE,
  proposed_by UUID NOT NULL REFERENCES profiles(id),
  resolution_type TEXT NOT NULL,
  credit_adjustment INTEGER,
  description TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'proposed',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON>reate function to adjust user credits
CREATE OR REPLACE FUNCTION adjust_user_credits(user_id_param UUID, amount_param INTEGER)
RETURNS void AS $$
BEGIN
  UPDATE profiles
  SET credit_balance = credit_balance + amount_param
  WHERE id = user_id_param;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
