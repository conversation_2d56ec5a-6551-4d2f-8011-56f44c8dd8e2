import { createServerSide } from '@/lib/supabase-server';
import Link from 'next/link';
import Image from 'next/image';
import { redirect } from 'next/navigation';

export default async function Dashboard() {
  const supabase = await createServerSide();

  // Check if user is logged in
  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    redirect('/login');
  }

  // Get user profile
  const { data: profile } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single();

  // Get user's skills
  const { data: skills } = await supabase
    .from('skills')
    .select('id, title, is_active')
    .eq('owner_id', user.id)
    .order('created_at', { ascending: false })
    .limit(3);

  // Get upcoming sessions (pending and accepted)
  const { data: upcomingSessions } = await supabase
    .from('sessions')
    .select(`
      id,
      skill:skills(title),
      scheduled_at,
      status,
      teacher_id,
      learner_id,
      teacher:profiles!sessions_teacher_id_fkey(display_name, avatar_url, email),
      learner:profiles!sessions_learner_id_fkey(display_name, avatar_url, email)
    `)
    .or(`teacher_id.eq.${user.id},learner_id.eq.${user.id}`)
    .in('status', ['pending', 'accepted'])
    .order('scheduled_at', { ascending: true })
    .limit(5);

  // Get past sessions (completed, cancelled, reviewed, disputed)
  const { data: pastSessions } = await supabase
    .from('sessions')
    .select(`
      id,
      skill:skills(title),
      scheduled_at,
      status,
      teacher_id,
      learner_id,
      teacher:profiles!sessions_teacher_id_fkey(display_name, avatar_url, email),
      learner:profiles!sessions_learner_id_fkey(display_name, avatar_url, email)
    `)
    .or(`teacher_id.eq.${user.id},learner_id.eq.${user.id}`)
    .in('status', ['completed', 'cancelled', 'reviewed', 'disputed'])
    .order('scheduled_at', { ascending: false })
    .limit(5);

  return (
    <main className="container mx-auto px-4 py-8">
      {/* Welcome Banner */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 mb-8">
        <div className="flex flex-col md:flex-row items-center md:items-start gap-8">
          <div className="flex-shrink-0">
            {profile?.avatar_url ? (
              <Image
                src={profile.avatar_url}
                alt={profile?.display_name || 'User'}
                width={120}
                height={120}
                className="rounded-full object-cover border-4 border-white dark:border-gray-700 shadow-lg"
              />
            ) : (
              <div className="w-32 h-32 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center border-4 border-white dark:border-gray-700 shadow-lg">
                <span className="text-4xl text-gray-500 dark:text-gray-300">
                  {profile?.display_name?.charAt(0) || user.email?.charAt(0)}
                </span>
              </div>
            )}
          </div>

          <div className="flex-grow text-center md:text-left">
            <h1 className="text-3xl font-bold mb-2">
              Welcome, {profile?.display_name || profile?.email?.split('@')[0] || 'Friend'}!
            </h1>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              {profile?.bio || 'Share your skills and learn from others in our community.'}
            </p>

            <div className="flex flex-wrap gap-4 mb-6">
              <div className="flex items-center">
                <svg className="w-5 h-5 mr-2 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span className="font-semibold">Time-Credits: {profile?.credit_balance || 0}h</span>
              </div>

              <div className="flex items-center">
                <svg className="w-5 h-5 mr-2 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
                <span className="font-semibold">Skills Offered: {skills?.length || 0}</span>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <Link
                href="/profile"
                className="w-full sm:w-auto px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition font-medium text-center"
              >
                Profile
              </Link>

              <Link
                href="/dashboard/skills/new"
                className="w-full sm:w-auto px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition font-medium text-center"
              >
                Offer a New Skill
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* My Skills */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-bold">My Skills</h2>
              <Link href="/dashboard/skills" className="text-blue-500 hover:text-blue-600 text-sm">
                View All
              </Link>
            </div>
          </div>

          <div className="p-6">
            {skills && skills.length > 0 ? (
              <ul className="space-y-4">
                {skills.map(skill => (
                  <li key={skill.id} className="flex items-center justify-between">
                    <div className="flex items-center">
                      <span className={`w-3 h-3 rounded-full mr-3 ${skill.is_active ? 'bg-green-500' : 'bg-gray-400'}`}></span>
                      <Link href={`/dashboard/skills/${skill.id}`} className="hover:text-blue-500 transition">
                        {skill.title}
                      </Link>
                    </div>
                    <Link href={`/dashboard/skills/${skill.id}/edit`} className="text-sm text-gray-500 hover:text-blue-500">
                      Edit
                    </Link>
                  </li>
                ))}
              </ul>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-500 dark:text-gray-400 mb-4">You haven't added any skills yet</p>
                <Link
                  href="/skills/create"
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition inline-block"
                >
                  Add Your First Skill
                </Link>
              </div>
            )}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-xl font-bold">Quick Actions</h2>
          </div>

          <div className="p-6">
            <div className="space-y-4">
              <Link
                href="/explore"
                className="flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition"
              >
                <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 flex items-center justify-center mr-4">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                  </svg>
                </div>
                <div>
                  <h3 className="font-medium text-gray-900 dark:text-white">Explore Skills</h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Discover skills you can learn</p>
                </div>
              </Link>

              <Link
                href="/credits"
                className="flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition"
              >
                <div className="w-10 h-10 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 flex items-center justify-center mr-4">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                <div>
                  <h3 className="font-medium text-gray-900 dark:text-white">Manage Credits</h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">View your credit history</p>
                </div>
              </Link>

              <Link
                href="/sessions"
                className="flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition"
              >
                <div className="w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 flex items-center justify-center mr-4">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                </div>
                <div>
                  <h3 className="font-medium text-gray-900 dark:text-white">All Sessions</h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Manage your teaching and learning</p>
                </div>
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Upcoming Sessions */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-bold">Upcoming Sessions</h2>
              <Link href="/sessions" className="text-blue-500 hover:text-blue-600 text-sm">
                View All
              </Link>
            </div>
          </div>

          <div className="p-6">
            {upcomingSessions && upcomingSessions.length > 0 ? (
              <ul className="space-y-4">
                {upcomingSessions.map(session => {
                  const date = new Date(session.scheduled_at);
                  const isTeacher = session.teacher_id === user.id;
                  const otherPerson = isTeacher ? session.learner : session.teacher;

                  return (
                    <li key={session.id} className="border-b border-gray-200 dark:border-gray-700 pb-4 last:border-0 last:pb-0">
                      <Link href={`/sessions/${session.id}`} className="block hover:bg-gray-50 dark:hover:bg-gray-700 -mx-4 px-4 py-2 rounded-lg">
                        <div className="flex justify-between items-center mb-1">
                          <span className="font-medium">
                            {(() => {
                              // Extract skill title safely
                              let skillTitle = 'Unknown Skill';
                              try {
                                if (Array.isArray(session.skill) && session.skill.length > 0) {
                                  skillTitle = String(session.skill[0].title || 'Unknown Skill');
                                } else if (session.skill && typeof session.skill === 'object') {
                                  // @ts-ignore - Handle both possible structures
                                  skillTitle = String(session.skill.title || 'Unknown Skill');
                                }
                              } catch (e) {
                                console.error('Error extracting skill title:', e);
                              }
                              return skillTitle;
                            })()}
                          </span>
                          <span className={`text-xs px-2 py-1 rounded-full ${
                            session.status === 'pending'
                              ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
                              : 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
                          }`}>
                            {session.status}
                          </span>
                        </div>
                        <div className="text-sm text-gray-600 dark:text-gray-300">
                          {date.toLocaleDateString()} at {date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                        </div>
                        <div className="flex items-center mt-2 text-sm text-gray-500 dark:text-gray-400">
                          <span>
                            {isTeacher ? 'Teaching' : 'Learning'} with {
                              (() => {
                                try {
                                  if (Array.isArray(otherPerson) && otherPerson.length > 0) {
                                    return String(otherPerson[0].display_name || 'Unknown');
                                  } else if (otherPerson && typeof otherPerson === 'object') {
                                    // @ts-ignore - Handle both possible structures
                                    return String(otherPerson.display_name || 'Unknown');
                                  }
                                  return 'Unknown';
                                } catch (e) {
                                  console.error('Error extracting display name:', e);
                                  return 'Unknown';
                                }
                              })()
                            }
                          </span>
                        </div>
                      </Link>
                    </li>
                  );
                })}
              </ul>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-500 dark:text-gray-400 mb-4">No upcoming sessions</p>
                <Link
                  href="/explore"
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition inline-block"
                >
                  Find Skills to Learn
                </Link>
              </div>
            )}
          </div>
        </div>

        {/* Past Sessions */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-bold">Past Sessions</h2>
              <Link href="/sessions" className="text-blue-500 hover:text-blue-600 text-sm">
                View All
              </Link>
            </div>
          </div>

          <div className="p-6">
            {pastSessions && pastSessions.length > 0 ? (
              <ul className="space-y-4">
                {pastSessions.map(session => {
                  const date = new Date(session.scheduled_at);
                  const isTeacher = session.teacher_id === user.id;
                  const otherPerson = isTeacher ? session.learner : session.teacher;

                  return (
                    <li key={session.id} className="border-b border-gray-200 dark:border-gray-700 pb-4 last:border-0 last:pb-0">
                      <Link href={`/sessions/${session.id}`} className="block hover:bg-gray-50 dark:hover:bg-gray-700 -mx-4 px-4 py-2 rounded-lg">
                        <div className="flex justify-between items-center mb-1">
                          <span className="font-medium">
                            {(() => {
                              // Extract skill title safely
                              let skillTitle = 'Unknown Skill';
                              try {
                                if (Array.isArray(session.skill) && session.skill.length > 0) {
                                  skillTitle = String(session.skill[0].title || 'Unknown Skill');
                                } else if (session.skill && typeof session.skill === 'object') {
                                  // @ts-ignore - Handle both possible structures
                                  skillTitle = String(session.skill.title || 'Unknown Skill');
                                }
                              } catch (e) {
                                console.error('Error extracting skill title:', e);
                              }
                              return skillTitle;
                            })()}
                          </span>
                          <span className={`text-xs px-2 py-1 rounded-full ${
                            session.status === 'completed' || session.status === 'reviewed'
                              ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                              : session.status === 'disputed'
                              ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                              : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                          }`}>
                            {session.status}
                          </span>
                        </div>
                        <div className="text-sm text-gray-600 dark:text-gray-300">
                          {date.toLocaleDateString()} at {date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                        </div>
                        <div className="flex items-center mt-2 text-sm text-gray-500 dark:text-gray-400">
                          <span>
                            {isTeacher ? 'Taught' : 'Learned'} with {
                              (() => {
                                try {
                                  if (Array.isArray(otherPerson) && otherPerson.length > 0) {
                                    return String(otherPerson[0].display_name || 'Unknown');
                                  } else if (otherPerson && typeof otherPerson === 'object') {
                                    // @ts-ignore - Handle both possible structures
                                    return String(otherPerson.display_name || 'Unknown');
                                  }
                                  return 'Unknown';
                                } catch (e) {
                                  console.error('Error extracting display name:', e);
                                  return 'Unknown';
                                }
                              })()
                            }
                          </span>
                        </div>
                      </Link>
                    </li>
                  );
                })}
              </ul>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-500 dark:text-gray-400 mb-4">No past sessions</p>
                <Link
                  href="/explore"
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition inline-block"
                >
                  Find Skills to Learn
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </main>
  );
}