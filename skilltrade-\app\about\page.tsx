import Link from 'next/link';
import { Metadata } from 'next';
import Image from 'next/image';
import MainHeader from '@/components/MainHeader';
import MainFooter from '@/components/MainFooter';

export const metadata: Metadata = {
  title: 'About Us - Skilltrade',
  description: 'Learn about Skilltrade, our mission, values, and the team behind the community time-bank platform.',
  keywords: ['about us', 'skilltrade', 'time bank', 'skill exchange', 'community learning', 'mission', 'values'],
};

export default function AboutUsPage() {
  return (
    <div className="min-h-screen bg-gray-950">
      <MainHeader />

      <main>
        {/* Hero Section */}
        <section className="py-16 bg-gradient-to-b from-gray-900 to-gray-950">
          <div className="container mx-auto px-4 text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">About Skilltrade</h1>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              We're building a community where knowledge is the currency and everyone has something valuable to share.
            </p>
          </div>
        </section>

        {/* Our Story Section */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-3xl font-bold mb-8 text-center">Our Story</h2>

              <div className="bg-gray-800 p-8 rounded-lg mb-12">
                <p className="text-gray-300 mb-4">
                  Skilltrade was born from a simple observation: in every community, there exists an abundance of untapped knowledge and skills. While traditional education can be expensive and inaccessible, we all have something valuable we could teach others.
                </p>
                <p className="text-gray-300 mb-4">
                  Founded in 2023, our platform reimagines how skills are shared and acquired. We believe that time—not money—should be the currency of learning. By creating a system where an hour of teaching equals an hour of learning, we've built a balanced ecosystem that values everyone's contributions equally.
                </p>
                <p className="text-gray-300">
                  Today, Skilltrade connects people from diverse backgrounds who might never have met otherwise, creating not just learning opportunities but meaningful community connections.
                </p>
              </div>

              <h2 className="text-3xl font-bold mb-8 text-center">Our Mission & Values</h2>

              <div className="grid md:grid-cols-2 gap-8 mb-12">
                <div className="bg-gray-800 p-6 rounded-lg">
                  <h3 className="text-xl font-semibold mb-4 flex items-center">
                    <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                    </div>
                    Our Mission
                  </h3>
                  <p className="text-gray-300">
                    To democratize learning by creating a platform where knowledge is freely exchanged, skills are valued equally, and everyone has the opportunity to both teach and learn.
                  </p>
                </div>

                <div className="bg-gray-800 p-6 rounded-lg">
                  <h3 className="text-xl font-semibold mb-4 flex items-center">
                    <div className="w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z" />
                      </svg>
                    </div>
                    Our Vision
                  </h3>
                  <p className="text-gray-300">
                    A world where everyone recognizes their capacity to teach, values their ability to learn, and participates in knowledge exchange as a fundamental part of community life.
                  </p>
                </div>
              </div>

              <div className="space-y-6">
                <h3 className="text-2xl font-semibold mb-4">Our Core Values</h3>

                <div className="flex items-start">
                  <div className="w-12 h-12 bg-blue-600/20 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-lg font-medium mb-2">Community First</h4>
                    <p className="text-gray-300">
                      We believe that learning happens best in supportive communities. Every feature and policy is designed to foster connection, trust, and mutual respect.
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="w-12 h-12 bg-blue-600/20 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-lg font-medium mb-2">Equal Value</h4>
                    <p className="text-gray-300">
                      Whether you're teaching rocket science or basic cooking, your time is valued equally on our platform. We believe all skills have worth and everyone has something to contribute.
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="w-12 h-12 bg-blue-600/20 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-lg font-medium mb-2">Trust & Safety</h4>
                    <p className="text-gray-300">
                      Our platform is built on trust. We provide the tools and systems to ensure safe interactions, fair exchanges, and honest feedback.
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="w-12 h-12 bg-blue-600/20 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-lg font-medium mb-2">Continuous Growth</h4>
                    <p className="text-gray-300">
                      We believe in lifelong learning and that everyone can be both teacher and student. Our platform encourages continuous personal development and skill expansion.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-gradient-to-r from-blue-900/40 to-purple-900/40">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl font-bold mb-6">Join Our Community</h2>
            <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
              Become part of the Skilltrade community today and start your journey of teaching and learning.
            </p>
            <Link
              href="/signup"
              className="px-8 py-4 rounded-md bg-blue-600 hover:bg-blue-700 transition text-lg font-medium inline-block"
            >
              Sign Up Now
            </Link>
          </div>
        </section>
      </main>

      <MainFooter />
    </div>
  );
}
