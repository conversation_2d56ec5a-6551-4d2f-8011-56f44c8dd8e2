'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { uploadAvatar, deleteAvatar } from '@/utils/avatar-upload';
import { createClient } from '@supabase/supabase-js';

// Create a Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabase = createClient(supabaseUrl, supabaseAnonKey);

interface AvatarUploadProps {
  userId: string;
  url?: string | null;
  size?: number;
  onUpload?: (url: string) => void;
}

export default function AvatarUpload({
  userId,
  url: initialUrl = null,
  size = 150,
  onUpload
}: AvatarUploadProps) {
  const [avatarUrl, setAvatarUrl] = useState<string | null>(initialUrl);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showOptions, setShowOptions] = useState(false);

  useEffect(() => {
    if (initialUrl) setAvatarUrl(initialUrl);
  }, [initialUrl]);

  async function handleUpload(event: React.ChangeEvent<HTMLInputElement>) {
    try {
      setUploading(true);
      setError(null);

      if (!event.target.files || event.target.files.length === 0) {
        throw new Error('You must select an image to upload.');
      }

      const file = event.target.files[0];
      const result = await uploadAvatar(userId, file);

      setAvatarUrl(result.url);
      if (onUpload) onUpload(result.url);
      setShowOptions(false);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Error uploading avatar');
      console.error('Error uploading avatar:', error);
    } finally {
      setUploading(false);
    }
  }

  async function handleRemove() {
    try {
      setUploading(true);
      setError(null);

      if (!avatarUrl) return;

      // Extract the path from the URL
      // The URL format is like: https://iidqtbyxltqnhgyrpofd.supabase.co/storage/v1/object/public/avatars/userId/filename
      const urlParts = avatarUrl.split('/');
      const bucketIndex = urlParts.indexOf('public') + 1;
      if (bucketIndex > 0 && bucketIndex < urlParts.length) {
        const bucket = urlParts[bucketIndex];
        const path = urlParts.slice(bucketIndex + 1).join('/');
        console.log('Deleting avatar from path:', path);
        await deleteAvatar(userId, path);
      } else {
        throw new Error('Invalid avatar URL format');
      }

      setAvatarUrl(null);
      if (onUpload) onUpload('');
      setShowOptions(false);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Error removing avatar');
      console.error('Error removing avatar:', error);
    } finally {
      setUploading(false);
    }
  }

  return (
    <div className="relative">
      <div
        className="relative cursor-pointer"
        onClick={() => setShowOptions(!showOptions)}
      >
        {avatarUrl ? (
          <div className="relative">
            <img
              src={avatarUrl}
              alt="Avatar"
              className="rounded-full object-cover border-4 border-white dark:border-gray-700 shadow-lg"
              width={size}
              height={size}
              style={{ width: size, height: size }}
            />
            <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 rounded-full transition-all flex items-center justify-center">
              <span className="text-white opacity-0 hover:opacity-100">Change</span>
            </div>
          </div>
        ) : (
          <div
            className="flex items-center justify-center bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-300 rounded-full border-4 border-white dark:border-gray-700 shadow-lg"
            style={{ width: size, height: size }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width={size/3} height={size/3} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
              <circle cx="12" cy="7" r="4"></circle>
            </svg>
          </div>
        )}
      </div>

      {showOptions && (
        <div className="absolute mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg z-10 overflow-hidden">
          <div className="py-1">
            <label className="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer">
              {uploading ? 'Uploading...' : 'Upload New Photo'}
              <input
                type="file"
                accept="image/*"
                onChange={handleUpload}
                disabled={uploading}
                className="hidden"
              />
            </label>

            {avatarUrl && (
              <button
                onClick={handleRemove}
                disabled={uploading}
                className="block w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                Remove Photo
              </button>
            )}

            <button
              onClick={() => setShowOptions(false)}
              className="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              Cancel
            </button>
          </div>
        </div>
      )}

      {error && (
        <p className="text-red-500 text-sm mt-2">{error}</p>
      )}
    </div>
  );
}
