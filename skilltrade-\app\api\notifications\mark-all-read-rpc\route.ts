import { createServerSide } from '@/lib/supabase-server';
import { createAdminClient } from '@/lib/supabase-admin';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerSide();

    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log('Marking all notifications as read for user using RPC:', user.id);

    // Use the admin client to bypass RLS
    const adminSupabase = createAdminClient();
    
    // Call the RPC function to mark all notifications as read
    const { data: updatedNotifications, error: rpcError } = await adminSupabase
      .rpc('mark_all_notifications_as_read', { user_id_param: user.id });

    if (rpcError) {
      console.error('Error calling mark_all_notifications_as_read RPC:', rpcError);
      return NextResponse.json(
        { error: 'Failed to mark all notifications as read', details: rpcError.message },
        { status: 500 }
      );
    }

    console.log(`Successfully marked ${updatedNotifications?.length || 0} notifications as read using RPC`);

    return NextResponse.json({
      success: true,
      updatedNotifications: updatedNotifications || []
    });
  } catch (error: any) {
    console.error('Error marking all notifications as read:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}
