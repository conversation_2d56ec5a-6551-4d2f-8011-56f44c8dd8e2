'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { createClientSide } from '@/lib/supabase';
import { useAuth } from './AuthContext';

export interface Notification {
  id: string;
  user_id: string;
  type: string;
  title: string;
  message?: string;
  content?: string;
  link?: string | null;
  is_read: boolean;
  created_at: string;
  badge_count?: number;
}

interface NotificationsContextType {
  notifications: Notification[];
  unreadCount: number;
  unreadMessageCount: number;
  totalUnreadCount: number;
  loading: boolean;
  error: string | null;
  fetchNotifications: () => Promise<void>;
  fetchUnreadCounts: () => Promise<void>;
  markAsRead: (notificationId: string) => Promise<boolean>;
  markAllAsRead: () => Promise<boolean>;
}

const NotificationsContext = createContext<NotificationsContextType | undefined>(undefined);

export function NotificationsProvider({ children }: { children: ReactNode }) {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [unreadMessageCount, setUnreadMessageCount] = useState(0);
  const [totalUnreadCount, setTotalUnreadCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();
  const supabase = createClientSide();

  const fetchUnreadCounts = async () => {
    if (!user) {
      setUnreadCount(0);
      setUnreadMessageCount(0);
      setTotalUnreadCount(0);
      return;
    }

    try {
      // Get notification count
      const { data: notificationCount, error: notificationError } = await supabase
        .rpc('get_unread_notification_count', { user_id_param: user.id });

      if (notificationError) {
        throw notificationError;
      }

      // Get message count
      const { data: messageCount, error: messageError } = await supabase
        .rpc('get_unread_direct_message_count', { user_id_param: user.id });

      if (messageError) {
        throw messageError;
      }

      // Get total count
      const { data: totalCount, error: totalError } = await supabase
        .rpc('get_total_unread_count', { user_id_param: user.id });

      if (totalError) {
        throw totalError;
      }

      setUnreadCount(notificationCount || 0);
      setUnreadMessageCount(messageCount || 0);
      setTotalUnreadCount(totalCount || 0);
    } catch (error: any) {
      console.error('Error fetching unread counts:', error);
    }
  };

  const fetchNotifications = async () => {
    if (!user) {
      setNotifications([]);
      setUnreadCount(0);
      setUnreadMessageCount(0);
      setTotalUnreadCount(0);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);

      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) {
        throw error;
      }

      setNotifications(data || []);
      setUnreadCount(data?.filter(n => !n.is_read).length || 0);

      // Also fetch unread counts
      await fetchUnreadCounts();
    } catch (error: any) {
      setError(error.message || 'Failed to load notifications');
      console.error('Error fetching notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  const markAsRead = async (notificationId: string): Promise<boolean> => {
    if (!user) {
      console.log('Cannot mark as read: No user logged in');
      return false;
    }

    try {
      console.log('Marking notification as read:', notificationId);

      // Try the RPC endpoint first
      let response = await fetch('/api/notifications/mark-read-rpc', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ notificationId }),
      });

      // If the RPC endpoint fails, fall back to the regular endpoint
      if (!response.ok) {
        console.log('RPC endpoint failed, falling back to regular endpoint');
        response = await fetch('/api/notifications/mark-read', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ notificationId }),
        });
      }

      const result = await response.json();
      console.log('Mark as read response:', result);

      if (!response.ok) {
        throw new Error(result.error || 'Failed to mark notification as read');
      }

      // Update local state
      setNotifications(prev => {
        const updatedNotifications = prev.map(n =>
          n.id === notificationId ? { ...n, is_read: true } : n
        );
        // Update unread count based on the updated notifications
        setUnreadCount(updatedNotifications.filter(n => !n.is_read).length);
        return updatedNotifications;
      });

      // Force a refresh of notifications
      setTimeout(() => {
        fetchNotifications();
      }, 300);

      return true;
    } catch (error: any) {
      console.error('Error marking notification as read:', error);
      return false;
    }
  };

  const markAllAsRead = async (): Promise<boolean> => {
    if (!user) {
      console.log('Cannot mark all as read: No user logged in');
      return false;
    }

    try {
      console.log('Attempting to mark all notifications as read');

      // Try the RPC endpoint first
      let response = await fetch('/api/notifications/mark-all-read-rpc', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      // If the RPC endpoint fails, fall back to the regular endpoint
      if (!response.ok) {
        console.log('RPC endpoint failed, falling back to regular endpoint');
        response = await fetch('/api/notifications/mark-all-read', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        });
      }

      const result = await response.json();
      console.log('Mark all as read response:', result);

      if (!response.ok) {
        throw new Error(result.error || 'Failed to mark all notifications as read');
      }

      // Update local state
      setNotifications(prev => {
        const updatedNotifications = prev.map(n => ({ ...n, is_read: true }));
        setUnreadCount(0);
        return updatedNotifications;
      });

      // Force a refresh of notifications
      setTimeout(() => {
        fetchNotifications();
      }, 300);

      return true;
    } catch (error: any) {
      console.error('Error marking all notifications as read:', error);
      return false;
    }
  };

  // Set up real-time subscription when user changes
  useEffect(() => {
    if (!user) {
      setNotifications([]);
      setUnreadCount(0);
      setUnreadMessageCount(0);
      setTotalUnreadCount(0);
      setLoading(false);
      return;
    }

    fetchNotifications();

    // Set up interval to fetch unread counts every minute
    const intervalId = setInterval(() => {
      fetchUnreadCounts();
    }, 60000); // 1 minute

    return () => {
      clearInterval(intervalId);
    };

    // Set up real-time subscription
    const channel = supabase
      .channel(`user_notifications:${user?.id || 'anonymous'}`)
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'notifications',
        filter: `user_id=eq.${user?.id || 'anonymous'}`
      }, (payload) => {
        // Add the new notification to the state
        const newNotification = payload.new as Notification;
        setNotifications(prev => {
          // Check if notification already exists to avoid duplicates
          if (prev.some(n => n.id === newNotification.id)) {
            return prev;
          }
          const updatedNotifications = [newNotification, ...prev];
          // Update unread count based on the new notifications array
          setUnreadCount(updatedNotifications.filter(n => !n.is_read).length);
          return updatedNotifications;
        });
      })
      .on('postgres_changes', {
        event: 'UPDATE',
        schema: 'public',
        table: 'notifications',
        filter: `user_id=eq.${user?.id || 'anonymous'}`
      }, (payload) => {
        // Update the notification in the state
        const updatedNotification = payload.new as Notification;
        setNotifications(prev => {
          const updatedNotifications = prev.map(n =>
            n.id === updatedNotification.id ? updatedNotification : n
          );
          // Update unread count based on the updated notifications array
          setUnreadCount(updatedNotifications.filter(n => !n.is_read).length);
          return updatedNotifications;
        });
      })
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [user?.id]);

  return (
    <NotificationsContext.Provider
      value={{
        notifications,
        unreadCount,
        unreadMessageCount,
        totalUnreadCount,
        loading,
        error,
        fetchNotifications,
        fetchUnreadCounts,
        markAsRead,
        markAllAsRead,
      }}
    >
      {children}
    </NotificationsContext.Provider>
  );
}

export function useNotifications() {
  const context = useContext(NotificationsContext);

  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationsProvider');
  }

  return context;
}
