import { NextRequest, NextResponse } from 'next/server';
import { createServerSide } from '@/lib/supabase-server';
import { validateText } from '@/lib/bad-words-filter';
import { isAdmin } from '@/lib/admin-utils';

export const dynamic = 'force-dynamic';

// POST /api/admin/content-filter/test - Test the content filter
export async function POST(request: NextRequest) {
  try {
    // Check if the user is an admin
    const isUserAdmin = await isAdmin();
    if (!isUserAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { text } = body;

    if (!text || typeof text !== 'string') {
      return NextResponse.json(
        { error: 'Text is required' },
        { status: 400 }
      );
    }

    console.log('Testing text against filter:', text);

    // Test the text against the filter
    const result = await validateText(text);

    console.log('Filter test result:', result);

    return NextResponse.json(result);
  } catch (error: any) {
    console.error('Error in POST /api/admin/content-filter/test:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}
