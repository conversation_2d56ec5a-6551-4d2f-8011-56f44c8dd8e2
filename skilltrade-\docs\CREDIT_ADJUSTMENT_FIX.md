# Credit Adjustment Fix

This document provides instructions for fixing the credit adjustment functionality in the Skilltrade admin dashboard.

## Issue

The credit adjustment feature in the admin dashboard is failing with the following error:

```
null value in column "session_id" of relation "ledger" violates not-null constraint
```

This occurs because the `ledger` table has a not-null constraint on the `session_id` column, but when adjusting credits through the admin panel, we're not providing a session_id value.

## Solution

The solution involves:

1. Modifying the `ledger` table to allow null `session_id` values for admin adjustments
2. Creating helper functions to handle credit adjustments properly
3. Updating the API routes to use these new functions

## Implementation

### 1. Database Changes

Run the following SQL script in the Supabase SQL Editor to make the necessary database changes:

```sql
-- Update the ledger table to allow null session_id for admin adjustments
ALTER TABLE ledger 
ALTER COLUMN session_id DROP NOT NULL;

-- Add a comment to explain the change
COMMENT ON COLUMN ledger.session_id IS 'Session ID (can be null for admin adjustments)';

-- Create a dummy session ID function for admin adjustments
CREATE OR REPLACE FUNCTION get_admin_adjustment_session()
RETURNS UUID AS $$
DECLARE
  admin_session_id UUID;
BEGIN
  -- Check if we already have a dummy session for admin adjustments
  SELECT id INTO admin_session_id
  FROM sessions
  WHERE skill_id = '00000000-0000-0000-0000-000000000000'
  LIMIT 1;
  
  -- If not, create one
  IF admin_session_id IS NULL THEN
    -- First, check if we have the dummy skill
    DECLARE
      dummy_skill_id UUID;
    BEGIN
      -- Try to find the dummy skill
      SELECT id INTO dummy_skill_id
      FROM skills
      WHERE title = 'Admin Adjustment'
      LIMIT 1;
      
      -- If not found, create it
      IF dummy_skill_id IS NULL THEN
        -- Find an admin user
        DECLARE
          admin_user_id UUID;
        BEGIN
          SELECT id INTO admin_user_id
          FROM profiles
          WHERE is_admin = true
          LIMIT 1;
          
          -- If no admin found, use the first user
          IF admin_user_id IS NULL THEN
            SELECT id INTO admin_user_id
            FROM profiles
            LIMIT 1;
          END IF;
          
          -- Create the dummy skill
          INSERT INTO skills (id, owner_id, title, description, is_active)
          VALUES (
            '00000000-0000-0000-0000-000000000000',
            admin_user_id,
            'Admin Adjustment',
            'System skill for admin credit adjustments',
            false
          )
          ON CONFLICT (id) DO NOTHING
          RETURNING id INTO dummy_skill_id;
        END;
      END IF;
      
      -- Create the dummy session
      INSERT INTO sessions (
        skill_id,
        teacher_id,
        learner_id,
        scheduled_at,
        duration_hours,
        status
      )
      SELECT
        dummy_skill_id,
        owner_id,
        owner_id,
        NOW(),
        0,
        'system'
      FROM skills
      WHERE id = dummy_skill_id
      RETURNING id INTO admin_session_id;
    END;
  END IF;
  
  RETURN admin_session_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to adjust credits
CREATE OR REPLACE FUNCTION adjust_credits(
  user_id UUID,
  hours_delta NUMERIC,
  reason TEXT
) RETURNS VOID AS $$
DECLARE
  admin_session_id UUID;
BEGIN
  -- Get or create the admin adjustment session
  admin_session_id := get_admin_adjustment_session();
  
  -- Add the credit adjustment to the ledger
  INSERT INTO ledger(
    user_id,
    session_id,
    hours_delta,
    reason,
    created_at
  )
  VALUES (
    user_id,
    admin_session_id,
    hours_delta,
    reason,
    NOW()
  );
  
  -- Update the user's credit balance
  UPDATE profiles
  SET credit_balance = credit_balance + hours_delta
  WHERE id = user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### 2. Code Changes

The following files have been updated:

1. `app/api/admin/credits/adjust/route.ts` - Updated to use the new `adjust_credits` function
2. `app/api/admin/sessions/[id]/resolve/route.ts` - Updated to use the new `adjust_credits` function for credit adjustments during session resolution

## How It Works

The solution works as follows:

1. We modify the `ledger` table to allow null `session_id` values, which would be the simplest solution.

2. However, to maintain data integrity and consistency, we create a special "dummy" session specifically for admin adjustments.

3. The `get_admin_adjustment_session()` function either retrieves an existing dummy session or creates a new one if needed.

4. The `adjust_credits()` function handles the entire credit adjustment process:
   - Gets or creates the admin adjustment session
   - Adds an entry to the ledger with the appropriate session_id
   - Updates the user's credit balance

5. The API routes now use this function instead of directly inserting into the ledger table.

## Troubleshooting

If you still encounter issues after applying these changes:

1. Check if the SQL script executed successfully by running:
   ```sql
   SELECT routine_name FROM information_schema.routines 
   WHERE routine_schema = 'public' 
   AND routine_name IN ('get_admin_adjustment_session', 'adjust_credits');
   ```

2. Verify that the `ledger` table's `session_id` column allows null values:
   ```sql
   SELECT column_name, is_nullable 
   FROM information_schema.columns 
   WHERE table_name = 'ledger' AND column_name = 'session_id';
   ```

3. Check if the dummy skill and session were created:
   ```sql
   SELECT * FROM skills WHERE title = 'Admin Adjustment';
   SELECT * FROM sessions WHERE status = 'system';
   ```

4. Check the browser console and server logs for any JavaScript errors or backend errors.

## Additional Notes

- The transaction management functions (`begin_transaction`, `commit_transaction`, `rollback_transaction`) are still used in the API routes for consistency, but the actual credit adjustment is now handled by the `adjust_credits` function.
- This approach ensures that all credit adjustments are properly recorded in the ledger with a valid session_id, maintaining data integrity.
