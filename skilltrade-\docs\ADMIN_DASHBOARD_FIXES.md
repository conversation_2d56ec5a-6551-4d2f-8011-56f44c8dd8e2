# Admin Dashboard Fixes

This document provides instructions for fixing issues with the Skilltrade admin dashboard.

## Issues Fixed

### 1. Admin Settings Page 404 Error

The admin dashboard was showing a 404 error when navigating to the Settings page. This has been fixed by:

- Creating the missing `app/admin/settings/page.tsx` file
- Implementing a basic settings page with system configuration options
- Adding a corresponding API route at `app/api/admin/settings/route.ts`

### 2. Credit Adjustment Not Working

The credit adjustment functionality in the admin dashboard was not working. This has been fixed by:

- Creating the missing database functions required for credit adjustments
- Implementing transaction management functions
- Adding a function to calculate a user's new balance after an adjustment

## Installation Instructions

Follow these steps to apply the fixes:

### 1. Update the Codebase

The following files have been added or modified:

- `app/admin/settings/page.tsx` - New admin settings page
- `app/api/admin/settings/route.ts` - API route for managing settings
- `supabase/transaction-functions.sql` - SQL file with required database functions

### 2. Run the SQL Script

To fix the credit adjustment functionality, you need to run the SQL script in the Supabase SQL Editor:

1. Log in to your Supabase dashboard
2. Navigate to the SQL Editor
3. Copy and paste the contents of `supabase/transaction-functions.sql`
4. Run the SQL script

This will:
- Create the transaction management functions
- Add the `get_new_balance` function for credit calculations
- Create the `system_settings` table if it doesn't exist
- Update the `handle_new_user` function to use the initial credit balance from settings

## System Settings

The new admin settings page allows you to configure:

- **Platform Name**: The name of your platform
- **Contact Email**: The main contact email for the platform
- **Initial Credit Balance**: The number of credits given to new users upon registration
- **Email Notifications**: Enable or disable email notifications
- **Maintenance Mode**: Enable or disable maintenance mode

## Troubleshooting

If you encounter issues after applying these fixes:

### Credit Adjustment Still Not Working

- Check if the SQL script was executed successfully
- Verify that the database functions exist by running:
  ```sql
  SELECT routine_name FROM information_schema.routines 
  WHERE routine_schema = 'public' 
  AND routine_name IN ('begin_transaction', 'commit_transaction', 'rollback_transaction', 'get_new_balance');
  ```
- Check the browser console for any JavaScript errors
- Check the server logs for any backend errors

### Settings Page Not Working

- Make sure the `system_settings` table was created
- Check if the API route is accessible by navigating to `/api/admin/settings` (should return a 403 if not logged in as admin)
- Verify that you're logged in as an admin user

## Additional Notes

- The transaction management functions (`begin_transaction`, `commit_transaction`, `rollback_transaction`) are placeholder functions since Supabase handles transactions automatically. They are provided for API consistency.
- The `get_new_balance` function calculates a user's new balance by adding the hours delta to their current balance.
- The system settings are stored in a new `system_settings` table with a single row (id=1).
