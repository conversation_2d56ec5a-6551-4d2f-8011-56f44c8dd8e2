import { NextRequest, NextResponse } from 'next/server';
import { createAdminClient } from '@/lib/supabase-admin';
import { isAdmin } from '@/lib/admin-utils';

// Default settings
const defaultSettings = {
  initialCreditBalance: 1,
  enableEmailNotifications: true,
  maintenanceMode: false,
  platformName: 'Skilltrade',
  contactEmail: '<EMAIL>',
};

// GET /api/admin/settings - Get system settings
export async function GET(request: NextRequest) {
  try {
    // Check if the user is an admin
    const isUserAdmin = await isAdmin();
    if (!isUserAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    const supabase = createAdminClient();

    // Get settings from the database
    const { data: settings, error } = await supabase
      .from('system_settings')
      .select('*')
      .single();

    // If no settings exist or there's an error, return default settings
    if (error || !settings) {
      return NextResponse.json({
        settings: defaultSettings,
      });
    }

    return NextResponse.json({
      settings: {
        initialCreditBalance: settings.initial_credit_balance || defaultSettings.initialCreditBalance,
        enableEmailNotifications: settings.enable_email_notifications || defaultSettings.enableEmailNotifications,
        maintenanceMode: settings.maintenance_mode || defaultSettings.maintenanceMode,
        platformName: settings.platform_name || defaultSettings.platformName,
        contactEmail: settings.contact_email || defaultSettings.contactEmail,
      },
    });
  } catch (error: any) {
    console.error('Error fetching settings:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch settings' },
      { status: 500 }
    );
  }
}

// POST /api/admin/settings - Update system settings
export async function POST(request: NextRequest) {
  try {
    // Check if the user is an admin
    const isUserAdmin = await isAdmin();
    if (!isUserAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    const supabase = createAdminClient();
    const body = await request.json();

    const { settings } = body;

    if (!settings) {
      return NextResponse.json(
        { error: 'Settings are required' },
        { status: 400 }
      );
    }

    // Check if settings table exists
    const { error: tableError } = await supabase
      .from('system_settings')
      .select('*')
      .limit(1);

    // If table doesn't exist, create it
    if (tableError && tableError.message.includes('does not exist')) {
      // Create the table (this would normally be done in a migration)
      // For now, we'll just return an error suggesting to run the SQL script
      return NextResponse.json(
        {
          error: 'System settings table does not exist. Please run the setup SQL script.'
        },
        { status: 500 }
      );
    }

    // Upsert settings
    const { error } = await supabase
      .from('system_settings')
      .upsert({
        id: 1, // Single row for system settings
        initial_credit_balance: settings.initialCreditBalance,
        enable_email_notifications: settings.enableEmailNotifications,
        maintenance_mode: settings.maintenanceMode,
        platform_name: settings.platformName,
        contact_email: settings.contactEmail,
        updated_at: new Date().toISOString(),
      })
      .select();

    if (error) {
      throw error;
    }

    return NextResponse.json({
      settings,
      message: 'Settings updated successfully',
    });
  } catch (error: any) {
    console.error('Error updating settings:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to update settings' },
      { status: 500 }
    );
  }
}
