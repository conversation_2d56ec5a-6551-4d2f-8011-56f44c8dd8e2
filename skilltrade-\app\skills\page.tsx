'use client';

import { useState, useEffect } from 'react';
import { createClientSide } from '@/lib/supabase';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import MainHeader from '@/components/MainHeader';
import MainFooter from '@/components/MainFooter';

interface Skill {
  id: string;
  title: string;
  description: string;
  tags: string[];
  is_active: boolean;
  created_at: string;
}

export default function SkillsPage() {
  const [skills, setSkills] = useState<Skill[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const supabase = createClientSide();

  useEffect(() => {
    const fetchSkills = async () => {
      try {
        setLoading(true);

        // Get the current user
        const { data: { user } } = await supabase.auth.getUser();

        if (!user) {
          router.push('/login');
          return;
        }

        // Get the user's skills
        const { data, error } = await supabase
          .from('skills')
          .select('*')
          .eq('owner_id', user.id)
          .order('created_at', { ascending: false });

        if (error) {
          throw error;
        }

        setSkills(data || []);
      } catch (error: any) {
        setError(error.message || 'Failed to load skills');
      } finally {
        setLoading(false);
      }
    };

    fetchSkills();
  }, [router, supabase]);

  const toggleSkillStatus = async (skillId: string, currentStatus: boolean) => {
    try {
      const { error } = await supabase
        .from('skills')
        .update({ is_active: !currentStatus })
        .eq('id', skillId);

      if (error) {
        throw error;
      }

      // Update the local state
      setSkills(skills.map(skill =>
        skill.id === skillId
          ? { ...skill, is_active: !currentStatus }
          : skill
      ));
    } catch (error: any) {
      setError(error.message || 'Failed to update skill status');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <MainHeader />
        <div className="container mx-auto px-4 py-8">
          <div className="flex justify-center items-center h-64">
            <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
            <p className="ml-4 text-gray-700 dark:text-gray-300">Loading skills...</p>
          </div>
        </div>
        <MainFooter />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <MainHeader />

      <main className="container mx-auto px-4 py-8">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">My Skills</h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Manage the skills you offer to teach
            </p>
          </div>

          <Link
            href="/skills/create"
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition font-medium"
          >
            Share a New Skill
          </Link>
        </div>

        {error && (
          <div className="bg-red-100 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 px-4 py-3 rounded-lg mb-6">
            {error}
          </div>
        )}

        {skills.length === 0 ? (
          <div className="bg-white dark:bg-gray-800 rounded-xl p-8 text-center shadow-lg">
            <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">No skills yet</h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              You haven't shared any skills yet. Share your knowledge and earn time credits!
            </p>
            <Link
              href="/skills/create"
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition inline-block font-medium"
            >
              Share Your First Skill
            </Link>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {skills.map((skill) => (
              <div key={skill.id} className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition">
                <div className="p-6">
                  <div className="flex justify-between items-start mb-4">
                    <Link href={`/skills/${skill.id}`} className="text-lg font-semibold text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 transition">
                      {skill.title}
                    </Link>
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      skill.is_active
                        ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'
                        : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-400'
                    }`}>
                      {skill.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </div>

                  <p className="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-3">
                    {skill.description}
                  </p>

                  {skill.tags.length > 0 && (
                    <div className="flex flex-wrap gap-2 mb-4">
                      {skill.tags.map((tag, index) => (
                        <span key={index} className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full text-xs">
                          {tag}
                        </span>
                      ))}
                    </div>
                  )}

                  <div className="flex justify-between items-center mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                    <div className="flex gap-3">
                      <Link
                        href={`/skills/${skill.id}`}
                        className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium"
                      >
                        View
                      </Link>

                      <Link
                        href={`/skills/${skill.id}/edit`}
                        className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium"
                      >
                        Edit
                      </Link>
                    </div>

                    <button
                      onClick={() => toggleSkillStatus(skill.id, skill.is_active)}
                      className={`text-sm font-medium ${
                        skill.is_active
                          ? 'text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300'
                          : 'text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300'
                      }`}
                    >
                      {skill.is_active ? 'Deactivate' : 'Activate'}
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </main>

      <MainFooter />
    </div>
  );
}
