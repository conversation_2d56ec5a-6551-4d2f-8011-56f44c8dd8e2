// UI Polish Script
// This script applies final UI polish to the profile components

const fs = require('fs');
const path = require('path');

// Configuration
const componentsDir = path.join(process.cwd(), 'components', 'profile');
const outputDir = path.join(process.cwd(), 'components', 'profile', 'polished');

// Create output directory if it doesn't exist
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// UI improvements to apply
const uiImprovements = [
  {
    name: 'Consistent spacing',
    pattern: /className="([^"]*)mb-(\d+)([^"]*)"/g,
    replacement: 'className="$1mb-4$3"',
  },
  {
    name: 'Consistent padding',
    pattern: /className="([^"]*)p-(\d+)([^"]*)"/g,
    replacement: 'className="$1p-6$3"',
  },
  {
    name: 'Consistent border radius',
    pattern: /className="([^"]*)rounded-(\w+)([^"]*)"/g,
    replacement: 'className="$1rounded-xl$3"',
  },
  {
    name: 'Consistent shadow',
    pattern: /className="([^"]*)shadow-(\w+)([^"]*)"/g,
    replacement: 'className="$1shadow-sm$3"',
  },
  {
    name: 'Consistent text colors',
    pattern: /className="([^"]*)text-gray-(\d+)([^"]*)"/g,
    replacement: (match, p1, p2, p3) => {
      // Map different gray shades to consistent values
      const grayMap = {
        '400': '400',
        '500': '500',
        '600': '600',
        '700': '700',
        '800': '800',
        '900': '900',
      };
      return `className="${p1}text-gray-${grayMap[p2] || p2}${p3}"`;
    },
  },
  {
    name: 'Consistent dark mode text colors',
    pattern: /className="([^"]*)dark:text-gray-(\d+)([^"]*)"/g,
    replacement: (match, p1, p2, p3) => {
      // Map different gray shades to consistent values
      const grayMap = {
        '300': '300',
        '400': '400',
        '500': '500',
        '600': '600',
      };
      return `className="${p1}dark:text-gray-${grayMap[p2] || p2}${p3}"`;
    },
  },
  {
    name: 'Consistent background colors',
    pattern: /className="([^"]*)bg-gray-(\d+)([^"]*)"/g,
    replacement: (match, p1, p2, p3) => {
      // Map different gray shades to consistent values
      const grayMap = {
        '50': '50',
        '100': '100',
        '200': '200',
        '700': '700',
        '800': '800',
        '900': '900',
      };
      return `className="${p1}bg-gray-${grayMap[p2] || p2}${p3}"`;
    },
  },
  {
    name: 'Consistent dark mode background colors',
    pattern: /className="([^"]*)dark:bg-gray-(\d+)([^"]*)"/g,
    replacement: (match, p1, p2, p3) => {
      // Map different gray shades to consistent values
      const grayMap = {
        '700': '700',
        '800': '800',
        '900': '900',
      };
      return `className="${p1}dark:bg-gray-${grayMap[p2] || p2}${p3}"`;
    },
  },
  {
    name: 'Consistent button styling',
    pattern: /<Button([^>]*)variant="(\w+)"([^>]*)>/g,
    replacement: '<Button$1variant="$2"$3 className="transition duration-200">',
  },
  {
    name: 'Consistent hover effects',
    pattern: /hover:([a-z-]+)-(\w+)/g,
    replacement: (match, p1, p2) => {
      // Add transition to hover effects
      if (!match.includes('transition')) {
        return `hover:${p1}-${p2} transition duration-200`;
      }
      return match;
    },
  },
  {
    name: 'Consistent focus styling',
    pattern: /focus:outline-none/g,
    replacement: 'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
  },
  {
    name: 'Add responsive design',
    pattern: /<div className="([^"]*)grid grid-cols-(\d+)([^"]*)"/g,
    replacement: '<div className="$1grid grid-cols-1 md:grid-cols-$2$3"',
  },
  {
    name: 'Add loading states',
    pattern: /<Button([^>]*)onClick={(\w+)}([^>]*)>/g,
    replacement: '<Button$1onClick={$2}$3 disabled={loading}>',
  },
  {
    name: 'Add animations',
    pattern: /<div className="([^"]*)bg-([a-z]+-\d+)([^"]*)"/g,
    replacement: '<div className="$1bg-$2$3 transition-colors duration-200"',
  },
  {
    name: 'Add error handling',
    pattern: /async function (\w+)\(\) {/g,
    replacement: 'async function $1() {\n  try {',
  },
  {
    name: 'Add error handling (catch)',
    pattern: /await (\w+)\(([^)]*)\);(\s+)}/g,
    replacement: 'await $1($2);\n  } catch (error) {\n    console.error("Error in $1:", error);\n    // Handle error appropriately\n  }$3}',
  },
];

// Function to apply UI improvements to a component file
function polishComponent(filePath) {
  const fileName = path.basename(filePath);
  const content = fs.readFileSync(filePath, 'utf8');
  
  let polishedContent = content;
  
  // Apply each UI improvement
  uiImprovements.forEach(improvement => {
    polishedContent = polishedContent.replace(improvement.pattern, improvement.replacement);
  });
  
  // Add animation to component
  polishedContent = polishedContent.replace(
    /export default function (\w+)/,
    `// Added animation wrapper
const AnimatedComponent = ({ children }) => (
  <div className="animate-fadeIn">
    {children}
  </div>
);

export default function $1`
  );
  
  // Wrap component return with animation
  polishedContent = polishedContent.replace(
    /return \(/,
    'return (\n    <AnimatedComponent>'
  );
  
  polishedContent = polishedContent.replace(
    /\);(\s+)}$/,
    '\n    </AnimatedComponent>\n  );\n}'
  );
  
  // Add loading skeleton
  polishedContent = polishedContent.replace(
    /const \[loading, setLoading\] = useState\(false\);/,
    `const [loading, setLoading] = useState(false);
  
  // Loading skeleton
  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm animate-pulse">
        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-full mb-2"></div>
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-5/6 mb-2"></div>
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-4/6 mb-4"></div>
        <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
      </div>
    );
  }`
  );
  
  // Add error state
  polishedContent = polishedContent.replace(
    /const \[error, setError\] = useState\(null\);/,
    `const [error, setError] = useState(null);
  
  // Error state
  if (error) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm">
        <div className="text-red-500 dark:text-red-400 mb-4">
          <p className="font-semibold">Error loading component</p>
          <p className="text-sm">{error}</p>
        </div>
        <Button onClick={() => setError(null)}>Try Again</Button>
      </div>
    );
  }`
  );
  
  // Write the polished content to the output directory
  const outputPath = path.join(outputDir, fileName);
  fs.writeFileSync(outputPath, polishedContent);
  
  return {
    fileName,
    original: content,
    polished: polishedContent,
    changes: uiImprovements.length,
  };
}

// Function to generate a CSS animation file
function generateAnimations() {
  const animationsContent = `/* Profile component animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Transition utilities */
.transition-colors {
  transition-property: background-color, border-color, color, fill, stroke;
}

.duration-200 {
  transition-duration: 200ms;
}

/* Responsive utilities */
@media (max-width: 640px) {
  .profile-card {
    padding: 1rem;
  }
  
  .profile-card h3 {
    font-size: 1.1rem;
  }
}

/* Dark mode transitions */
.dark .profile-card {
  transition: background-color 0.3s ease, color 0.3s ease;
}
`;

  const outputPath = path.join(outputDir, 'animations.css');
  fs.writeFileSync(outputPath, animationsContent);
  
  return {
    fileName: 'animations.css',
    content: animationsContent,
  };
}

// Function to generate a report
function generateReport(results, animations) {
  const reportContent = `# UI Polish Report

## Summary
Applied UI polish to ${results.length} components.

## Components
${results.map(result => `- ${result.fileName}: ${result.changes} improvements`).join('\n')}

## Animations
Created animations file: ${animations.fileName}

## Next Steps
1. Import the animations.css file in your global CSS
2. Review the polished components and merge changes as needed
3. Test the components in different screen sizes and color schemes
4. Ensure consistent performance across browsers
`;

  const reportPath = path.join(process.cwd(), 'ui-polish-report.md');
  fs.writeFileSync(reportPath, reportContent);
  
  return reportPath;
}

// Main function
function main() {
  console.log('Applying UI polish to profile components...');
  
  // Get all component files
  const componentFiles = fs.readdirSync(componentsDir)
    .filter(file => file.endsWith('.tsx'))
    .map(file => path.join(componentsDir, file));
  
  // Polish each component
  const results = componentFiles.map(polishComponent);
  
  // Generate animations
  const animations = generateAnimations();
  
  // Generate report
  const reportPath = generateReport(results, animations);
  
  console.log(`UI polish complete! Report written to ${reportPath}`);
  console.log(`Polished components saved to ${outputDir}`);
}

main();
