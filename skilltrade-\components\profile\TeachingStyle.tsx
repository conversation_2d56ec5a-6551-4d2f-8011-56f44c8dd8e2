'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Pencil, Save } from 'lucide-react';

interface TeachingStyleProps {
  teachingStyle: string | null;
  isEditable?: boolean;
  onSave?: (teachingStyle: string) => Promise<void>;
}

export default function TeachingStyle({
  teachingStyle,
  isEditable = false,
  onSave,
}: TeachingStyleProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [value, setValue] = useState(teachingStyle || '');
  const [isSaving, setIsSaving] = useState(false);

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = async () => {
    if (!onSave) return;

    try {
      setIsSaving(true);
      await onSave(value);
      setIsEditing(false);
    } catch (error) {
      console.error('Error saving teaching style:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setValue(teachingStyle || '');
    setIsEditing(false);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm mb-6 w-full max-w-full break-words">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 w-full">
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2 sm:mb-0">Teaching Style</h3>
        {isEditable && !isEditing && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleEdit}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          >
            <Pencil className="h-4 w-4 mr-2" />
            Edit
          </Button>
        )}
      </div>

      {isEditing ? (
        <div className="space-y-4 w-full">
          <textarea
            value={value}
            onChange={(e) => setValue(e.target.value)}
            placeholder="Describe your teaching approach, philosophy, and methods..."
            className="w-full h-40 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white resize-none"
          />
          <div className="flex flex-wrap gap-2">
            <Button onClick={handleSave} disabled={isSaving} className="w-full sm:w-auto">
              {isSaving ? 'Saving...' : 'Save'}
            </Button>
            <Button variant="outline" onClick={handleCancel} disabled={isSaving} className="w-full sm:w-auto">
              Cancel
            </Button>
          </div>
        </div>
      ) : (
        <div className="prose dark:prose-invert max-w-none">
          {teachingStyle ? (
            <p className="text-gray-700 dark:text-gray-300">{teachingStyle}</p>
          ) : (
            <p className="text-gray-500 dark:text-gray-400 italic">
              No teaching style information provided yet.
              {isEditable && ' Click Edit to add your teaching approach.'}
            </p>
          )}
        </div>
      )}
    </div>
  );
}
