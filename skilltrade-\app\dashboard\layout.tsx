import { createServerSide } from '@/lib/supabase-server';
import { redirect } from 'next/navigation';
import DashboardNav from '@/components/DashboardNav';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Dashboard - Skilltrade',
  description: 'Manage your skills, sessions, and credits on Skilltrade',
};

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const supabase = await createServerSide();

  // Check if user is logged in
  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    redirect('/login');
  }
  
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <DashboardNav />
      {children}
    </div>
  );
}
