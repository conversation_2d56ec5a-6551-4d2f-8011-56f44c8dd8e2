/**
 * This script generates favicon files from the logo.png file in the public directory.
 * 
 * To run this script:
 * 1. Install Sharp: npm install sharp
 * 2. Run: node scripts/generate-favicons.js
 */

const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

const SOURCE_LOGO = path.join(__dirname, '../public/logo.png');
const PUBLIC_DIR = path.join(__dirname, '../public');

// Define the favicon sizes to generate
const FAVICON_SIZES = [
  { name: 'favicon-16x16.png', size: 16 },
  { name: 'favicon-32x32.png', size: 32 },
  { name: 'apple-touch-icon.png', size: 180 },
  { name: 'android-chrome-192x192.png', size: 192 },
  { name: 'android-chrome-512x512.png', size: 512 },
];

// Generate ICO file (favicon.ico)
async function generateIco() {
  try {
    // Create a 32x32 PNG first
    await sharp(SOURCE_LOGO)
      .resize(32, 32)
      .toFile(path.join(PUBLIC_DIR, 'temp-favicon.png'));
    
    // Use the 32x32 PNG to create the ICO file
    // Note: For a proper ICO file with multiple sizes, you would need a specialized library
    // This is a simple approach that copies the PNG to ICO
    fs.copyFileSync(
      path.join(PUBLIC_DIR, 'temp-favicon.png'),
      path.join(__dirname, '../app/favicon.ico')
    );
    
    // Clean up the temporary file
    fs.unlinkSync(path.join(PUBLIC_DIR, 'temp-favicon.png'));
    
    console.log('✅ Generated favicon.ico');
  } catch (error) {
    console.error('Error generating favicon.ico:', error);
  }
}

// Generate PNG favicons
async function generatePngFavicons() {
  try {
    for (const { name, size } of FAVICON_SIZES) {
      await sharp(SOURCE_LOGO)
        .resize(size, size)
        .toFile(path.join(PUBLIC_DIR, name));
      
      console.log(`✅ Generated ${name} (${size}x${size})`);
    }
  } catch (error) {
    console.error('Error generating PNG favicons:', error);
  }
}

// Main function
async function main() {
  console.log('🔄 Generating favicons from logo.png...');
  
  if (!fs.existsSync(SOURCE_LOGO)) {
    console.error('❌ Source logo file not found:', SOURCE_LOGO);
    return;
  }
  
  await generatePngFavicons();
  await generateIco();
  
  console.log('✨ All favicons generated successfully!');
}

// Run the script
main().catch(console.error);
