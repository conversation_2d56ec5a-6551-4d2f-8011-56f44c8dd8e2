import { createServerSide } from '@/lib/supabase-server';
import { createAdminClient } from '@/lib/supabase-admin';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerSide();

    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the notification ID from the request body
    const { notificationId } = await request.json();

    if (!notificationId) {
      return NextResponse.json(
        { error: 'Notification ID is required' },
        { status: 400 }
      );
    }

    console.log('Marking notification as read using RPC:', notificationId);

    // Use the admin client to bypass RLS
    const adminSupabase = createAdminClient();
    
    // Call the RPC function to mark the notification as read
    const { data: updatedNotification, error: rpcError } = await adminSupabase
      .rpc('mark_notification_read', { 
        notification_id_param: notificationId,
        user_id_param: user.id
      });

    if (rpcError) {
      console.error('Error calling mark_notification_read RPC:', rpcError);
      return NextResponse.json(
        { error: 'Failed to mark notification as read', details: rpcError.message },
        { status: 500 }
      );
    }

    if (!updatedNotification) {
      return NextResponse.json(
        { error: 'Notification not found or you are not authorized to mark it as read' },
        { status: 404 }
      );
    }

    console.log('Successfully marked notification as read using RPC');

    return NextResponse.json({
      success: true,
      updatedNotification
    });
  } catch (error: any) {
    console.error('Error marking notification as read:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}
