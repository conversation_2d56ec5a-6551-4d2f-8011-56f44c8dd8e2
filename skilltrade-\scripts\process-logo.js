/**
 * This script processes the logo image to round its corners
 * and creates various favicon sizes with rounded corners.
 * 
 * To run this script:
 * 1. Install Sharp: npm install sharp
 * 2. Run: node scripts/process-logo.js
 */

const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

const SOURCE_LOGO = path.join(__dirname, '../public/logo.png');
const PUBLIC_DIR = path.join(__dirname, '../public');

// Create rounded logo
async function createRoundedLogo() {
  try {
    console.log('🔄 Creating rounded logo...');
    
    // Get the dimensions of the original logo
    const metadata = await sharp(SOURCE_LOGO).metadata();
    const { width, height } = metadata;
    
    // Create a rounded rectangle SVG
    const roundedCorners = Buffer.from(`
      <svg>
        <rect 
          x="0" 
          y="0" 
          width="${width}" 
          height="${height}" 
          rx="${Math.min(width, height) * 0.2}" 
          ry="${Math.min(width, height) * 0.2}"
        />
      </svg>
    `);
    
    // Apply the rounded corners to the logo
    await sharp(SOURCE_LOGO)
      .composite([{
        input: roundedCorners,
        blend: 'dest-in'
      }])
      .toFile(path.join(PUBLIC_DIR, 'logo-rounded.png'));
    
    console.log('✅ Created rounded logo: logo-rounded.png');
    
    return path.join(PUBLIC_DIR, 'logo-rounded.png');
  } catch (error) {
    console.error('Error creating rounded logo:', error);
    return SOURCE_LOGO; // Fall back to original logo if there's an error
  }
}

// Define the favicon sizes to generate
const FAVICON_SIZES = [
  { name: 'favicon-16x16.png', size: 16 },
  { name: 'favicon-32x32.png', size: 32 },
  { name: 'apple-touch-icon.png', size: 180 },
  { name: 'android-chrome-192x192.png', size: 192 },
  { name: 'android-chrome-512x512.png', size: 512 },
];

// Generate ICO file (favicon.ico)
async function generateIco(roundedLogoPath) {
  try {
    // Create a 32x32 PNG first
    await sharp(roundedLogoPath)
      .resize(32, 32)
      .toFile(path.join(PUBLIC_DIR, 'temp-favicon.png'));
    
    // Use the 32x32 PNG to create the ICO file
    fs.copyFileSync(
      path.join(PUBLIC_DIR, 'temp-favicon.png'),
      path.join(__dirname, '../app/favicon.ico')
    );
    
    // Clean up the temporary file
    fs.unlinkSync(path.join(PUBLIC_DIR, 'temp-favicon.png'));
    
    console.log('✅ Generated favicon.ico');
  } catch (error) {
    console.error('Error generating favicon.ico:', error);
  }
}

// Generate PNG favicons
async function generatePngFavicons(roundedLogoPath) {
  try {
    for (const { name, size } of FAVICON_SIZES) {
      await sharp(roundedLogoPath)
        .resize(size, size)
        .toFile(path.join(PUBLIC_DIR, name));
      
      console.log(`✅ Generated ${name} (${size}x${size})`);
    }
  } catch (error) {
    console.error('Error generating PNG favicons:', error);
  }
}

// Create a circular version of the logo
async function createCircularLogo(roundedLogoPath) {
  try {
    console.log('🔄 Creating circular logo...');
    
    // Get the dimensions of the rounded logo
    const metadata = await sharp(roundedLogoPath).metadata();
    const { width, height } = metadata;
    const size = Math.min(width, height);
    
    // Create a circle SVG
    const circle = Buffer.from(`
      <svg>
        <circle 
          cx="${size/2}" 
          cy="${size/2}" 
          r="${size/2}"
        />
      </svg>
    `);
    
    // Apply the circle to the logo
    await sharp(roundedLogoPath)
      .resize(size, size)
      .composite([{
        input: circle,
        blend: 'dest-in'
      }])
      .toFile(path.join(PUBLIC_DIR, 'logo-circle.png'));
    
    console.log('✅ Created circular logo: logo-circle.png');
  } catch (error) {
    console.error('Error creating circular logo:', error);
  }
}

// Main function
async function main() {
  console.log('🔄 Processing logo and generating favicons...');
  
  if (!fs.existsSync(SOURCE_LOGO)) {
    console.error('❌ Source logo file not found:', SOURCE_LOGO);
    return;
  }
  
  // Create rounded logo
  const roundedLogoPath = await createRoundedLogo();
  
  // Generate favicons from the rounded logo
  await generatePngFavicons(roundedLogoPath);
  await generateIco(roundedLogoPath);
  
  // Create a circular version as an option
  await createCircularLogo(roundedLogoPath);
  
  console.log('✨ All logo processing and favicon generation completed successfully!');
  console.log('📝 You now have:');
  console.log('  - logo-rounded.png: Logo with rounded corners');
  console.log('  - logo-circle.png: Circular version of the logo');
  console.log('  - Various favicon files with rounded corners');
  console.log('');
  console.log('🔍 To use these new logos, update the Image components in your code to reference the new files.');
}

// Run the script
main().catch(console.error);
