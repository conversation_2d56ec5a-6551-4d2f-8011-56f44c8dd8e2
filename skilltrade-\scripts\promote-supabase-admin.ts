/**
 * This script promotes a user to admin status directly using Supabase functions.
 * It can be run with:
 * npx ts-node scripts/promote-supabase-admin.ts <user_email>
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing environment variables. Make sure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set.');
  process.exit(1);
}

// Create a Supabase client with the service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function promoteUserToAdmin(email: string) {
  try {
    console.log(`Attempting to promote user ${email} to admin...`);
    
    // Step 1: Find the user by email
    const { data: users, error: userError } = await supabase
      .from('profiles')
      .select('id, email, display_name')
      .eq('email', email);
    
    if (userError || !users || users.length === 0) {
      throw new Error(`User with email ${email} not found`);
    }
    
    const user = users[0];
    
    // Step 2: Update the profile to set is_admin to true
    const { error: updateError } = await supabase
      .from('profiles')
      .update({ is_admin: true })
      .eq('id', user.id);
      
    if (updateError) {
      throw updateError;
    }
    
    console.log(`Successfully promoted user ${user.display_name || user.email} to admin!`);
    
    // Step 3: Verify the update
    const { data: updatedUser, error: verifyError } = await supabase
      .from('profiles')
      .select('is_admin')
      .eq('id', user.id)
      .single();
      
    if (verifyError) {
      console.warn('Could not verify the update, but the update command succeeded.');
    } else {
      console.log(`Verification: User is${updatedUser.is_admin ? '' : ' NOT'} an admin.`);
    }
  } catch (error) {
    console.error('Error promoting user to admin:', error);
    process.exit(1);
  }
}

// Get the email from command line arguments
const email = process.argv[2];

if (!email) {
  console.error('Please provide a user email as an argument.');
  console.error('Usage: npx ts-node scripts/promote-supabase-admin.ts <user_email>');
  process.exit(1);
}

// Run the function
promoteUserToAdmin(email);
