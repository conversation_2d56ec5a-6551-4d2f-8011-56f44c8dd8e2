'use client';

import { useState } from 'react';
import { createClientSide } from '@/lib/supabase';
import { useRouter } from 'next/navigation';
import { XCircle } from 'lucide-react';
import { validateText } from '@/lib/bad-words-filter-pages';
import SkillImageUpload from '@/components/SkillImageUpload';

interface AvailableDate {
  id: string;
  date: string;
  time: string;
  duration: number;
}

export default function NewSkillPage() {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState('');
  const [isActive, setIsActive] = useState(true);
  const [difficultyLevel, setDifficultyLevel] = useState<'beginner' | 'intermediate' | 'advanced'>('intermediate');
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [availableDates, setAvailableDates] = useState<AvailableDate[]>([]);
  const [newDate, setNewDate] = useState('');
  const [newTime, setNewTime] = useState('');
  const [newDuration, setNewDuration] = useState(1);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const router = useRouter();
  const supabase = createClientSide();

  const handleAddTag = async () => {
    const trimmedTag = tagInput.trim();
    if (trimmedTag && !tags.includes(trimmedTag)) {
      try {
        // Validate tag for bad words
        const tagValidation = await validateText(trimmedTag);
        if (!tagValidation.isValid) {
          setError(`Tag contains inappropriate language (${tagValidation.badWords.join(', ')}). Please use a different tag.`);
          return;
        }

        setTags([...tags, trimmedTag]);
        setTagInput('');
        setError(null);
      } catch (error: any) {
        setError(error.message || 'Error validating tag');
      }
    }
  };

  const handleRemoveTag = (tag: string) => {
    setTags(tags.filter(t => t !== tag));
  };

  const handleAddAvailableDate = () => {
    // Validate inputs
    if (!newDate || !newTime) {
      setError('Please select both date and time');
      return;
    }

    // Check if date is in the future
    const dateTime = new Date(`${newDate}T${newTime}`);
    if (dateTime <= new Date()) {
      setError('Please select a future date and time');
      return;
    }

    // Check if we already have 3 dates
    if (availableDates.length >= 3) {
      setError('You can only add up to 3 available dates');
      return;
    }

    // Check if this date/time is already added
    const isDuplicate = availableDates.some(
      date => date.date === newDate && date.time === newTime
    );

    if (isDuplicate) {
      setError('This date and time is already added');
      return;
    }

    // Add the new date
    setAvailableDates([
      ...availableDates,
      {
        id: crypto.randomUUID(),
        date: newDate,
        time: newTime,
        duration: newDuration
      }
    ]);

    // Clear inputs and error
    setNewDate('');
    setNewTime('');
    setNewDuration(1);
    setError(null);
  };

  const handleRemoveAvailableDate = (id: string) => {
    setAvailableDates(availableDates.filter(date => date.id !== id));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // Validate title and description for bad words
      const titleValidation = await validateText(title);
      if (!titleValidation.isValid) {
        throw new Error(`Your skill title contains inappropriate language (${titleValidation.badWords.join(', ')}). Please remove these words.`);
      }

      const descriptionValidation = await validateText(description);
      if (!descriptionValidation.isValid) {
        throw new Error(`Your skill description contains inappropriate language (${descriptionValidation.badWords.join(', ')}). Please remove these words.`);
      }

      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        router.push('/login');
        return;
      }

      // Validate form
      if (!title.trim()) {
        throw new Error('Title is required');
      }

      // Insert new skill
      const { data, error: insertError } = await supabase
        .from('skills')
        .insert({
          owner_id: user.id,
          title: title.trim(),
          description: description.trim(),
          tags,
          is_active: isActive,
          difficulty_level: difficultyLevel,
          image_url: imageUrl,
        })
        .select();

      if (insertError) {
        throw insertError;
      }

      // Insert available dates if any
      if (availableDates.length > 0) {
        const skillId = data[0].id;

        // Prepare available dates for insertion
        const availableDatesForInsert = availableDates.map(date => ({
          skill_id: skillId,
          date_time: new Date(`${date.date}T${date.time}`).toISOString(),
          duration_hours: date.duration,
          is_booked: false
        }));

        // Insert available dates
        const { error: datesError } = await supabase
          .from('skill_available_dates')
          .insert(availableDatesForInsert);

        if (datesError) {
          console.error('Error inserting available dates:', datesError);
          // We'll continue even if there's an error with dates
        }
      }

      // Redirect to the skill edit page to add an image
      router.push(`/dashboard/skills/${data[0].id}/edit`);
      router.refresh();
    } catch (error: any) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-950">
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <h1 className="text-2xl font-bold mb-6">Add New Skill</h1>

          <div className="bg-gray-800 rounded-lg p-6 shadow-lg">
            {error && (
              <div className="bg-red-900/30 border border-red-800 text-red-300 px-4 py-3 rounded mb-6">
                {error}
              </div>
            )}

            <form onSubmit={handleSubmit}>
              <div className="mb-6">
                <label htmlFor="title" className="block text-sm font-medium mb-2">
                  Skill Title <span className="text-red-400">*</span>
                </label>
                <input
                  id="title"
                  type="text"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="e.g. Beginner Spanish Conversation"
                  required
                />
              </div>

              <div className="mb-6">
                <label htmlFor="description" className="block text-sm font-medium mb-2">
                  Description
                </label>
                <textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  rows={5}
                  className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Describe what you can teach, your experience level, and what learners can expect"
                ></textarea>
              </div>

              <div className="mb-6">
                <label htmlFor="difficulty" className="block text-sm font-medium mb-2">
                  Difficulty Level
                </label>
                <select
                  id="difficulty"
                  value={difficultyLevel}
                  onChange={(e) => setDifficultyLevel(e.target.value as 'beginner' | 'intermediate' | 'advanced')}
                  className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="beginner">Beginner</option>
                  <option value="intermediate">Intermediate</option>
                  <option value="advanced">Advanced</option>
                </select>
              </div>

              <div className="mb-6">
                <label className="block text-sm font-medium mb-2">
                  Skill Image
                </label>
                <p className="text-sm text-gray-400 mb-2">
                  Upload an image that represents your skill (optional)
                </p>
                <div className="bg-gray-700 p-4 rounded-lg text-center text-gray-400">
                  You'll be able to add an image after creating the skill
                </div>
              </div>

              <div className="mb-6">
                <label className="block text-sm font-medium mb-2">
                  Tags
                </label>
                <div className="flex mb-2">
                  <input
                    type="text"
                    value={tagInput}
                    onChange={(e) => setTagInput(e.target.value)}
                    className="flex-grow px-4 py-2 bg-gray-700 border border-gray-600 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="e.g. language, spanish, beginner"
                  />
                  <button
                    type="button"
                    onClick={handleAddTag}
                    className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-r-md transition"
                  >
                    Add
                  </button>
                </div>
                <p className="text-xs text-gray-400 mb-2">Add tags to help others find your skill</p>
                <div className="flex flex-wrap gap-2 mt-3">
                  {tags.map((tag, index) => (
                    <div key={index} className="bg-gray-700 px-3 py-1 rounded-full flex items-center">
                      <span>{tag}</span>
                      <button
                        type="button"
                        onClick={() => handleRemoveTag(tag)}
                        className="ml-2 text-gray-400 hover:text-gray-200"
                      >
                        &times;
                      </button>
                    </div>
                  ))}
                </div>
              </div>

              <div className="mb-6">
                <label className="block text-sm font-medium mb-2">
                  Available Dates (up to 3)
                </label>
                <p className="text-xs text-gray-400 mb-4">
                  Add dates when you're available to teach this skill. Learners will be able to choose from these dates.
                </p>

                {/* Available dates list */}
                {availableDates.length > 0 && (
                  <div className="mb-4 space-y-2">
                    {availableDates.map((date) => (
                      <div
                        key={date.id}
                        className="flex items-center justify-between bg-gray-700 p-3 rounded-lg"
                      >
                        <div>
                          <span className="text-white font-medium">
                            {new Date(`${date.date}T${date.time}`).toLocaleDateString()} at {new Date(`${date.date}T${date.time}`).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                          </span>
                          <span className="text-gray-400 ml-2">
                            ({date.duration} {date.duration === 1 ? 'hour' : 'hours'})
                          </span>
                        </div>
                        <button
                          type="button"
                          onClick={() => handleRemoveAvailableDate(date.id)}
                          className="text-red-400 hover:text-red-300"
                        >
                          <XCircle size={18} />
                        </button>
                      </div>
                    ))}
                  </div>
                )}

                {/* Add new date form */}
                <div className="bg-gray-700 p-4 rounded-lg">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div>
                      <label htmlFor="new-date" className="block text-sm font-medium mb-1">
                        Date
                      </label>
                      <input
                        id="new-date"
                        type="date"
                        value={newDate}
                        onChange={(e) => setNewDate(e.target.value)}
                        min={new Date().toISOString().split('T')[0]}
                        className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label htmlFor="new-time" className="block text-sm font-medium mb-1">
                        Time
                      </label>
                      <input
                        id="new-time"
                        type="time"
                        value={newTime}
                        onChange={(e) => setNewTime(e.target.value)}
                        className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label htmlFor="new-duration" className="block text-sm font-medium mb-1">
                        Duration
                      </label>
                      <select
                        id="new-duration"
                        value={newDuration}
                        onChange={(e) => setNewDuration(Number(e.target.value))}
                        className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value={1}>1 hour</option>
                        <option value={2}>2 hours</option>
                        <option value={3}>3 hours</option>
                      </select>
                    </div>
                  </div>
                  <div className="flex justify-end">
                    <button
                      type="button"
                      onClick={handleAddAvailableDate}
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition text-sm"
                      disabled={availableDates.length >= 3}
                    >
                      Add Date
                    </button>
                  </div>
                </div>
                {availableDates.length >= 3 && (
                  <p className="text-xs text-amber-400 mt-2">
                    You've reached the maximum of 3 available dates.
                  </p>
                )}
              </div>

              <div className="mb-6">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={isActive}
                    onChange={(e) => setIsActive(e.target.checked)}
                    className="h-4 w-4 rounded border-gray-600 text-blue-600 focus:ring-blue-500 bg-gray-700"
                  />
                  <span className="ml-2 text-sm">Make this skill available for booking</span>
                </label>
              </div>

              <div className="flex justify-end space-x-4">
                <button
                  type="button"
                  onClick={() => router.back()}
                  className="px-4 py-2 border border-gray-600 rounded-md hover:bg-gray-700 transition"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-6 rounded-md transition duration-200 font-medium"
                  disabled={loading}
                >
                  {loading ? 'Creating...' : 'Create Skill'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </main>
    </div>
  );
}
