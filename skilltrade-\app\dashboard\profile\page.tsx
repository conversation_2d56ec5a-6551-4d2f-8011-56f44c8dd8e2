'use client';

import { useState, useEffect } from 'react';
import { createClientSide } from '@/lib/supabase';
import { useRouter } from 'next/navigation';
import DashboardNav from '@/components/DashboardNav';
import LearningGoals from '@/components/profile/LearningGoals';
import AvailabilityCalendar from '@/components/profile/AvailabilityCalendar';

export default function ProfilePage() {
  const [loading, setLoading] = useState(true);
  const [displayName, setDisplayName] = useState('');
  const [bio, setBio] = useState('');
  const [learningGoals, setLearningGoals] = useState<string[]>([]);
  const [newGoal, setNewGoal] = useState('');
  const [availabilitySlots, setAvailabilitySlots] = useState<any[]>([]);
  const [hobbies, setHobbies] = useState<string[]>([]);
  const [hobbyInput, setHobbyInput] = useState('');
  const [avatarUrl, setAvatarUrl] = useState<string | null>(null);
  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const router = useRouter();
  const supabase = createClientSide();

  useEffect(() => {
    async function getProfile() {
      try {
        setLoading(true);

        const { data: { user } } = await supabase.auth.getUser();

        if (!user) {
          router.push('/login');
          return;
        }

        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();

        if (error) {
          throw error;
        }

        if (data) {
          setDisplayName(data.display_name || '');
          setBio(data.bio || '');
          setLearningGoals(data.learning_goals || []);
          setHobbies(data.hobbies || []);
          setAvatarUrl(data.avatar_url);
        }

        // Fetch availability slots
        const { data: availabilityData, error: availabilityError } = await supabase
          .from('user_availability')
          .select('*')
          .eq('user_id', user.id)
          .order('day_of_week', { ascending: true });

        if (availabilityError) {
          console.error('Error fetching availability slots:', availabilityError);
        } else {
          // Transform the data to a more usable format
          const transformedSlots = (availabilityData || []).map(slot => ({
            id: slot.id,
            dayOfWeek: slot.day_of_week,
            startTime: slot.start_time,
            endTime: slot.end_time,
            isRecurring: slot.is_recurring
          }));

          setAvailabilitySlots(transformedSlots);
        }
      } catch (error: any) {
        console.error('Error loading profile:', error.message);
      } finally {
        setLoading(false);
      }
    }

    getProfile();
  }, [router, supabase]);

  const handleAddHobby = () => {
    if (hobbyInput.trim() && !hobbies.includes(hobbyInput.trim())) {
      setHobbies([...hobbies, hobbyInput.trim()]);
      setHobbyInput('');
    }
  };

  const handleRemoveHobby = (hobby: string) => {
    setHobbies(hobbies.filter(h => h !== hobby));
  };

  const handleAddGoal = () => {
    if (newGoal.trim() && !learningGoals.includes(newGoal.trim())) {
      setLearningGoals([...learningGoals, newGoal.trim()]);
      setNewGoal('');
    }
  };

  const handleRemoveGoal = (goal: string) => {
    setLearningGoals(learningGoals.filter(g => g !== goal));
  };

  const handleSaveLearningGoals = async (goals: string[]) => {
    try {
      setLoading(true);

      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        router.push('/login');
        return;
      }

      const { error } = await supabase
        .from('profiles')
        .update({
          learning_goals: goals,
          updated_at: new Date().toISOString(),
        })
        .eq('id', user.id);

      if (error) {
        throw error;
      }

      setLearningGoals(goals);
      setSuccess(true);

      // Hide success message after 3 seconds
      setTimeout(() => {
        setSuccess(false);
      }, 3000);

    } catch (error: any) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSaveAvailabilitySlots = async (slots: any[]) => {
    try {
      setLoading(true);

      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        router.push('/login');
        return;
      }

      // First, delete all existing availability slots
      const { error: deleteError } = await supabase
        .from('user_availability')
        .delete()
        .eq('user_id', user.id);

      if (deleteError) {
        throw deleteError;
      }

      // Then insert the new availability slots
      if (slots.length > 0) {
        const slotsToInsert = slots.map(slot => ({
          user_id: user.id,
          day_of_week: slot.dayOfWeek,
          start_time: slot.startTime,
          end_time: slot.endTime,
          is_recurring: slot.isRecurring || true
        }));

        const { error: insertError } = await supabase
          .from('user_availability')
          .insert(slotsToInsert);

        if (insertError) {
          throw insertError;
        }
      }

      // Get the updated availability slots
      const { data: updatedSlots, error: fetchError } = await supabase
        .from('user_availability')
        .select('*')
        .eq('user_id', user.id)
        .order('day_of_week', { ascending: true });

      if (fetchError) {
        throw fetchError;
      }

      // Transform the data to a more usable format
      const transformedSlots = (updatedSlots || []).map(slot => ({
        id: slot.id,
        dayOfWeek: slot.day_of_week,
        startTime: slot.start_time,
        endTime: slot.end_time,
        isRecurring: slot.is_recurring
      }));

      setAvailabilitySlots(transformedSlots);
      setSuccess(true);

      // Hide success message after 3 seconds
      setTimeout(() => {
        setSuccess(false);
      }, 3000);

    } catch (error: any) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      if (file.size > 2 * 1024 * 1024) {
        setError('Image size should be less than 2MB');
        return;
      }
      setAvatarFile(file);
      setAvatarUrl(URL.createObjectURL(file));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        router.push('/login');
        return;
      }

      let updatedAvatarUrl = avatarUrl;

      // Upload new avatar if selected
      if (avatarFile) {
        const fileExt = avatarFile.name.split('.').pop();
        const fileName = `${user.id}-${Date.now()}.${fileExt}`;

        const { error: uploadError } = await supabase.storage
          .from('avatars')
          .upload(fileName, avatarFile);

        if (uploadError) {
          throw uploadError;
        }

        updatedAvatarUrl = `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/avatars/${fileName}`;
      }

      // Update profile
      const { error: updateError } = await supabase
        .from('profiles')
        .update({
          display_name: displayName,
          bio,
          learning_goals: learningGoals,
          hobbies,
          avatar_url: updatedAvatarUrl,
          updated_at: new Date().toISOString(),
        })
        .eq('id', user.id);

      if (updateError) {
        throw updateError;
      }

      setSuccess(true);
    } catch (error: any) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-950">
      <DashboardNav />

      <main className="px-2 sm:px-4 py-8 pb-16 w-full">
        <div className="w-full max-w-full sm:max-w-[95%] md:max-w-[90%] lg:max-w-[85%] mx-auto mb-16">
          <h1 className="text-2xl font-bold mb-6">Edit Profile</h1>

          <div className="bg-gray-800 rounded-lg p-4 sm:p-6 shadow-lg mb-10 w-full">
            {error && (
              <div className="bg-red-900/30 border border-red-800 text-red-300 px-4 py-3 rounded mb-6">
                {error}
              </div>
            )}

            {success && (
              <div className="bg-green-900/30 border border-green-800 text-green-300 px-4 py-3 rounded mb-6">
                Profile updated successfully!
              </div>
            )}

            <form onSubmit={handleSubmit}>
              <div className="mb-6">
                <label className="block text-sm font-medium mb-2">
                  Profile Picture
                </label>
                <div className="flex items-center space-x-6">
                  <div className="w-20 h-20 rounded-full bg-gray-700 flex items-center justify-center overflow-hidden">
                    {avatarUrl ? (
                      <img
                        src={avatarUrl}
                        alt="Avatar"
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <span className="text-2xl text-gray-400">?</span>
                    )}
                  </div>
                  <label className="cursor-pointer bg-gray-700 hover:bg-gray-600 px-4 py-2 rounded-md transition">
                    <span>Change</span>
                    <input
                      type="file"
                      className="hidden"
                      accept="image/*"
                      onChange={handleAvatarChange}
                    />
                  </label>
                </div>
                <p className="text-xs text-gray-400 mt-2">Max size: 2MB</p>
              </div>

              <div className="mb-6">
                <label htmlFor="displayName" className="block text-sm font-medium mb-2">
                  Display Name
                </label>
                <input
                  id="displayName"
                  type="text"
                  value={displayName}
                  onChange={(e) => setDisplayName(e.target.value)}
                  className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div className="mb-6">
                <label htmlFor="bio" className="block text-sm font-medium mb-2">
                  Bio
                </label>
                <textarea
                  id="bio"
                  value={bio}
                  onChange={(e) => setBio(e.target.value)}
                  rows={4}
                  className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                ></textarea>
                <p className="text-xs text-gray-400 mt-1">Tell others about yourself</p>
              </div>

              <div className="mb-6">
                <label className="block text-sm font-medium mb-2">
                  Learning Goals
                </label>
                <div className="flex flex-col mb-2 gap-2">
                  <input
                    type="text"
                    value={newGoal}
                    onChange={(e) => setNewGoal(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleAddGoal()}
                    className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Add a skill you want to learn"
                  />
                  <button
                    type="button"
                    onClick={handleAddGoal}
                    className="w-full bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-md transition"
                  >
                    Add
                  </button>
                </div>
                <div className="flex flex-wrap gap-2 mt-3">
                  {learningGoals.map((goal, index) => (
                    <div key={index} className="bg-gray-700 px-3 py-1 rounded-full flex items-center">
                      <span>{goal}</span>
                      <button
                        type="button"
                        onClick={() => handleRemoveGoal(goal)}
                        className="ml-2 text-gray-400 hover:text-gray-200"
                      >
                        &times;
                      </button>
                    </div>
                  ))}
                </div>
                <p className="text-xs text-gray-400 mt-2">
                  Add skills you're interested in learning to help others find you for skill exchanges
                </p>
              </div>

              <div className="mb-6">
                <label className="block text-sm font-medium mb-2">
                  Availability Calendar
                </label>
                <AvailabilityCalendar
                  availabilitySlots={availabilitySlots}
                  isEditable={true}
                  onSave={handleSaveAvailabilitySlots}
                />
                <p className="text-xs text-gray-400 mt-2">
                  Set your regular teaching availability to help learners find suitable times
                </p>
              </div>

              <div className="mb-6">
                <label className="block text-sm font-medium mb-2">
                  Hobbies & Interests
                </label>
                <div className="flex flex-col mb-2 gap-2">
                  <input
                    type="text"
                    value={hobbyInput}
                    onChange={(e) => setHobbyInput(e.target.value)}
                    className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Add a hobby or interest"
                  />
                  <button
                    type="button"
                    onClick={handleAddHobby}
                    className="w-full bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-md transition"
                  >
                    Add
                  </button>
                </div>
                <div className="flex flex-wrap gap-2 mt-3">
                  {hobbies.map((hobby, index) => (
                    <div key={index} className="bg-gray-700 px-3 py-1 rounded-full flex items-center">
                      <span>{hobby}</span>
                      <button
                        type="button"
                        onClick={() => handleRemoveHobby(hobby)}
                        className="ml-2 text-gray-400 hover:text-gray-200"
                      >
                        &times;
                      </button>
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex justify-center mt-6 mb-4">
                <button
                  type="submit"
                  className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-6 rounded-md transition duration-200 font-medium"
                  disabled={loading}
                >
                  {loading ? 'Saving...' : 'Save Changes'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </main>
    </div>
  );
}
