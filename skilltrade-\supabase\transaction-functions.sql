-- Transaction management functions for Skilltrade
-- This SQL adds transaction management and credit balance calculation functions
-- Run this SQL in the Supabase SQL Editor

-- Create a function to begin a transaction
CREATE OR REPLACE FUNCTION begin_transaction()
RET<PERSON>NS void AS $$
BEGIN
  -- Begin a transaction
  -- Note: In Supabase, transactions are automatically started with the first write operation
  -- This function is provided for API consistency
  NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to commit a transaction
CREATE OR REPLACE FUNCTION commit_transaction()
RETURNS void AS $$
BEGIN
  -- Commit the transaction
  -- Note: In Supabase, transactions are automatically committed when the function ends
  -- This function is provided for API consistency
  NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to rollback a transaction
CREATE OR REPLACE FUNCTION rollback_transaction()
R<PERSON>URNS void AS $$
BEGIN
  -- Rollback the transaction
  -- Note: In Supabase, you can use RAISE EXCEPTION to trigger a rollback
  -- This function is provided for API consistency
  NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to calculate a user's new balance
CREATE OR REPLACE FUNCTION get_new_balance(user_id UUID, hours_delta NUMERIC)
RETURNS NUMERIC AS $$
DECLARE
  current_balance NUMERIC;
BEGIN
  -- Get the user's current balance
  SELECT credit_balance INTO current_balance
  FROM profiles
  WHERE id = user_id;

  -- Return the new balance
  RETURN current_balance + hours_delta;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create system_settings table if it doesn't exist
CREATE TABLE IF NOT EXISTS system_settings (
  id INTEGER PRIMARY KEY,
  initial_credit_balance NUMERIC NOT NULL DEFAULT 1,
  enable_email_notifications BOOLEAN NOT NULL DEFAULT true,
  maintenance_mode BOOLEAN NOT NULL DEFAULT false,
  platform_name TEXT NOT NULL DEFAULT 'Skilltrade',
  contact_email TEXT NOT NULL DEFAULT '<EMAIL>',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Insert default settings if not exists
INSERT INTO system_settings (id, initial_credit_balance, enable_email_notifications, maintenance_mode, platform_name, contact_email)
VALUES (1, 1, true, false, 'Skilltrade', '<EMAIL>')
ON CONFLICT (id) DO NOTHING;

-- Update the handle_new_user function to use the initial_credit_balance from system_settings
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS trigger AS $$
DECLARE
  initial_credits NUMERIC;
BEGIN
  -- Get the initial credit balance from system settings
  SELECT initial_credit_balance INTO initial_credits
  FROM system_settings
  WHERE id = 1;

  -- If no settings exist, use default value
  IF initial_credits IS NULL THEN
    initial_credits := 1;
  END IF;

  INSERT INTO public.profiles (
    id,
    email,
    display_name,
    credit_balance
  )
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'display_name', NEW.email),
    initial_credits
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
