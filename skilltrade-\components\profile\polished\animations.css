/* Profile component animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Transition utilities */
.transition-colors {
  transition-property: background-color, border-color, color, fill, stroke;
}

.duration-200 {
  transition-duration: 200ms;
}

/* Responsive utilities */
@media (max-width: 640px) {
  .profile-card {
    padding: 1rem;
  }
  
  .profile-card h3 {
    font-size: 1.1rem;
  }
}

/* Dark mode transitions */
.dark .profile-card {
  transition: background-color 0.3s ease, color 0.3s ease;
}
