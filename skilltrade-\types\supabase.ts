export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          display_name: string | null
          avatar_url: string | null
          bio: string | null
          hobbies: string[] | null
          credit_balance: number
          is_admin: boolean
          teaching_style: string | null
          learning_goals: string[] | null
          created_at: string
          updated_at: string | null
        }
        Insert: {
          id: string
          email: string
          display_name?: string | null
          avatar_url?: string | null
          bio?: string | null
          hobbies?: string[] | null
          credit_balance?: number
          is_admin?: boolean
          teaching_style?: string | null
          learning_goals?: string[] | null
          created_at?: string
          updated_at?: string | null
        }
        Update: {
          id?: string
          email?: string
          display_name?: string | null
          avatar_url?: string | null
          bio?: string | null
          hobbies?: string[] | null
          credit_balance?: number
          is_admin?: boolean
          teaching_style?: string | null
          learning_goals?: string[] | null
          created_at?: string
          updated_at?: string | null
        }
      }
      skills: {
        Row: {
          id: string
          title: string
          description: string
          tags: string[]
          owner_id: string
          is_active: boolean
          created_at: string
          updated_at: string | null
        }
        Insert: {
          id?: string
          title: string
          description: string
          tags?: string[]
          owner_id: string
          is_active?: boolean
          created_at?: string
          updated_at?: string | null
        }
        Update: {
          id?: string
          title?: string
          description?: string
          tags?: string[]
          owner_id?: string
          is_active?: boolean
          created_at?: string
          updated_at?: string | null
        }
      }
      sessions: {
        Row: {
          id: string
          skill_id: string
          teacher_id: string
          learner_id: string
          scheduled_at: string
          duration_hours: number
          status: string
          created_at: string
          teacher_marked_complete: boolean
          learner_marked_complete: boolean
          teacher_marked_at: string | null
          learner_marked_at: string | null
        }
        Insert: {
          id?: string
          skill_id: string
          teacher_id: string
          learner_id: string
          scheduled_at: string
          duration_hours: number
          status?: string
          created_at?: string
          teacher_marked_complete?: boolean
          learner_marked_complete?: boolean
          teacher_marked_at?: string | null
          learner_marked_at?: string | null
        }
        Update: {
          id?: string
          skill_id?: string
          teacher_id?: string
          learner_id?: string
          scheduled_at?: string
          duration_hours?: number
          status?: string
          created_at?: string
          teacher_marked_complete?: boolean
          learner_marked_complete?: boolean
          teacher_marked_at?: string | null
          learner_marked_at?: string | null
        }
      }
      reviews: {
        Row: {
          id: string
          session_id: string
          reviewer_id: string
          rating: number
          comment: string
          created_at: string
        }
        Insert: {
          id?: string
          session_id: string
          reviewer_id: string
          rating: number
          comment: string
          created_at?: string
        }
        Update: {
          id?: string
          session_id?: string
          reviewer_id?: string
          rating?: number
          comment?: string
          created_at?: string
        }
      }
      skill_available_dates: {
        Row: {
          id: string
          skill_id: string
          date_time: string
          duration_hours: number
          is_booked: boolean
          created_at: string
        }
        Insert: {
          id?: string
          skill_id: string
          date_time: string
          duration_hours: number
          is_booked?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          skill_id?: string
          date_time?: string
          duration_hours?: number
          is_booked?: boolean
          created_at?: string
        }
      }
      session_messages: {
        Row: {
          id: string
          session_id: string
          sender_id: string
          message: string
          created_at: string
        }
        Insert: {
          id?: string
          session_id: string
          sender_id: string
          message: string
          created_at?: string
        }
        Update: {
          id?: string
          session_id?: string
          sender_id?: string
          message?: string
          created_at?: string
        }
      }
      skill_taxonomies: {
        Row: {
          id: string
          name: string
          parent_id: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          parent_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          parent_id?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      user_skill_levels: {
        Row: {
          id: string
          user_id: string
          taxonomy_id: string
          proficiency_level: number
          description: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          taxonomy_id: string
          proficiency_level: number
          description?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          taxonomy_id?: string
          proficiency_level?: number
          description?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      portfolio_items: {
        Row: {
          id: string
          user_id: string
          title: string
          description: string | null
          image_url: string | null
          link_url: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          description?: string | null
          image_url?: string | null
          link_url?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          description?: string | null
          image_url?: string | null
          link_url?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      user_availability: {
        Row: {
          id: string
          user_id: string
          day_of_week: number
          start_time: string
          end_time: string
          is_recurring: boolean
          specific_date: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          day_of_week: number
          start_time: string
          end_time: string
          is_recurring?: boolean
          specific_date?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          day_of_week?: number
          start_time?: string
          end_time?: string
          is_recurring?: boolean
          specific_date?: string | null
          created_at?: string
          updated_at?: string
        }
      }
    }
  }
}