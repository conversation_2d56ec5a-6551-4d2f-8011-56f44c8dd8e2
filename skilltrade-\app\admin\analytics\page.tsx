'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { 
  Users, 
  BookOpen, 
  Calendar, 
  CreditCard, 
  TrendingUp,
  Bar<PERSON>hart2,
  PieChart,
  Activity
} from 'lucide-react';
import AdminPageHeader from '@/components/admin/AdminPageHeader';

interface AnalyticsData {
  period: string;
  users: {
    total: number;
    new: number;
    active: number;
    growth: { month: string; count: number }[];
  };
  skills: {
    total: number;
    new: number;
    popular: { id: string; title: string; count: number }[];
  };
  sessions: {
    total: number;
    new: number;
    byStatus: Record<string, number>;
    growth: { month: string; count: number }[];
  };
  credits: {
    total: number;
  };
}

interface StatCardProps {
  title: string;
  value: string | number;
  change?: string | number;
  icon: React.ReactNode;
  color: string;
}

function StatCard({ title, value, change, icon, color }: StatCardProps) {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6">
      <div className="flex items-center">
        <div className={`p-3 rounded-full ${color} text-white mr-4`}>
          {icon}
        </div>
        <div>
          <p className="text-gray-500 dark:text-gray-400 text-sm font-medium">{title}</p>
          <h3 className="text-2xl font-bold text-gray-900 dark:text-white">{value}</h3>
          {change !== undefined && (
            <p className="text-sm text-green-600 dark:text-green-400">
              {typeof change === 'number' && change > 0 ? '+' : ''}{change}
            </p>
          )}
        </div>
      </div>
    </div>
  );
}

export default function AdminAnalyticsPage() {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [period, setPeriod] = useState<string>('30days');
  
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    // Get period from URL if present
    const urlPeriod = searchParams.get('period');
    if (urlPeriod) {
      setPeriod(urlPeriod);
    }
  }, [searchParams]);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      
      const response = await fetch(`/api/admin/analytics?period=${period}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch analytics');
      }
      
      const data = await response.json();
      setAnalytics(data);
    } catch (error: any) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalytics();
  }, [period]);

  const updateUrlParams = (newPeriod: string) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('period', newPeriod);
    router.push(`/admin/analytics?${params.toString()}`);
  };

  const handlePeriodChange = (newPeriod: string) => {
    setPeriod(newPeriod);
    updateUrlParams(newPeriod);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p className="ml-4 text-gray-700 dark:text-gray-300">Loading analytics...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div>
        <AdminPageHeader
          title="Analytics & Reporting"
          description="View platform statistics and trends"
        />
        
        <div className="bg-red-100 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 px-4 py-3 rounded-lg mb-6">
          {error}
        </div>
      </div>
    );
  }

  if (!analytics) {
    return null;
  }

  return (
    <div>
      <AdminPageHeader
        title="Analytics & Reporting"
        description="View platform statistics and trends"
      />
      
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-6">
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => handlePeriodChange('7days')}
            className={`px-4 py-2 rounded-lg ${
              period === '7days'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
            }`}
          >
            Last 7 Days
          </button>
          <button
            onClick={() => handlePeriodChange('30days')}
            className={`px-4 py-2 rounded-lg ${
              period === '30days'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
            }`}
          >
            Last 30 Days
          </button>
          <button
            onClick={() => handlePeriodChange('90days')}
            className={`px-4 py-2 rounded-lg ${
              period === '90days'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
            }`}
          >
            Last 90 Days
          </button>
          <button
            onClick={() => handlePeriodChange('1year')}
            className={`px-4 py-2 rounded-lg ${
              period === '1year'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
            }`}
          >
            Last Year
          </button>
          <button
            onClick={() => handlePeriodChange('all')}
            className={`px-4 py-2 rounded-lg ${
              period === 'all'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
            }`}
          >
            All Time
          </button>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <StatCard
          title="Total Users"
          value={analytics.users.total}
          change={`+${analytics.users.new} new`}
          icon={<Users size={24} />}
          color="bg-blue-600"
        />
        <StatCard
          title="Total Skills"
          value={analytics.skills.total}
          change={`+${analytics.skills.new} new`}
          icon={<BookOpen size={24} />}
          color="bg-green-600"
        />
        <StatCard
          title="Total Sessions"
          value={analytics.sessions.total}
          change={`+${analytics.sessions.new} new`}
          icon={<Calendar size={24} />}
          color="bg-purple-600"
        />
        <StatCard
          title="Total Credits"
          value={analytics.credits.total}
          icon={<CreditCard size={24} />}
          color="bg-yellow-600"
        />
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">User Growth</h2>
            <div className="p-2 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400">
              <TrendingUp size={20} />
            </div>
          </div>
          
          <div className="h-64 flex items-center justify-center">
            {analytics.users.growth.length > 0 ? (
              <div className="w-full h-full">
                {/* Simple bar chart visualization */}
                <div className="flex h-full items-end space-x-2">
                  {analytics.users.growth.slice(-12).map((item, index) => {
                    const maxCount = Math.max(...analytics.users.growth.slice(-12).map(i => i.count));
                    const height = (item.count / maxCount) * 100;
                    
                    return (
                      <div key={index} className="flex-1 flex flex-col items-center">
                        <div 
                          className="w-full bg-blue-500 dark:bg-blue-600 rounded-t-sm" 
                          style={{ height: `${height}%` }}
                        ></div>
                        <div className="text-xs text-gray-500 dark:text-gray-400 mt-2 transform -rotate-45 origin-top-left">
                          {item.month}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            ) : (
              <p className="text-gray-500 dark:text-gray-400">No user growth data available</p>
            )}
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Session Growth</h2>
            <div className="p-2 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400">
              <BarChart2 size={20} />
            </div>
          </div>
          
          <div className="h-64 flex items-center justify-center">
            {analytics.sessions.growth.length > 0 ? (
              <div className="w-full h-full">
                {/* Simple bar chart visualization */}
                <div className="flex h-full items-end space-x-2">
                  {analytics.sessions.growth.slice(-12).map((item, index) => {
                    const maxCount = Math.max(...analytics.sessions.growth.slice(-12).map(i => i.count));
                    const height = maxCount > 0 ? (item.count / maxCount) * 100 : 0;
                    
                    return (
                      <div key={index} className="flex-1 flex flex-col items-center">
                        <div 
                          className="w-full bg-purple-500 dark:bg-purple-600 rounded-t-sm" 
                          style={{ height: `${height}%` }}
                        ></div>
                        <div className="text-xs text-gray-500 dark:text-gray-400 mt-2 transform -rotate-45 origin-top-left">
                          {item.month}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            ) : (
              <p className="text-gray-500 dark:text-gray-400">No session growth data available</p>
            )}
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Sessions by Status</h2>
            <div className="p-2 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400">
              <PieChart size={20} />
            </div>
          </div>
          
          <div className="space-y-4">
            {Object.entries(analytics.sessions.byStatus).length > 0 ? (
              Object.entries(analytics.sessions.byStatus).map(([status, count]) => {
                const percentage = (count / analytics.sessions.new) * 100;
                let statusColor = '';
                
                switch (status) {
                  case 'completed':
                    statusColor = 'bg-green-500';
                    break;
                  case 'pending':
                    statusColor = 'bg-yellow-500';
                    break;
                  case 'cancelled':
                    statusColor = 'bg-red-500';
                    break;
                  case 'disputed':
                    statusColor = 'bg-purple-500';
                    break;
                  default:
                    statusColor = 'bg-blue-500';
                    break;
                }
                
                return (
                  <div key={status}>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300 capitalize">
                        {status}
                      </span>
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        {count} ({percentage.toFixed(1)}%)
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                      <div 
                        className={`${statusColor} h-2.5 rounded-full`} 
                        style={{ width: `${percentage}%` }}
                      ></div>
                    </div>
                  </div>
                );
              })
            ) : (
              <p className="text-gray-500 dark:text-gray-400">No session status data available</p>
            )}
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Popular Skills</h2>
            <div className="p-2 rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400">
              <Activity size={20} />
            </div>
          </div>
          
          <div className="space-y-4">
            {analytics.skills.popular.length > 0 ? (
              analytics.skills.popular.map((skill, index) => {
                const maxCount = analytics.skills.popular[0].count;
                const percentage = (skill.count / maxCount) * 100;
                
                return (
                  <div key={skill.id}>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        {index + 1}. {skill.title}
                      </span>
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        {skill.count} sessions
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                      <div 
                        className="bg-yellow-500 h-2.5 rounded-full" 
                        style={{ width: `${percentage}%` }}
                      ></div>
                    </div>
                  </div>
                );
              })
            ) : (
              <p className="text-gray-500 dark:text-gray-400">No popular skills data available</p>
            )}
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">User Stats</h2>
          <div className="space-y-4">
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Users</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{analytics.users.total}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">New Users</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{analytics.users.new}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Active Users</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{analytics.users.active}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Skill Stats</h2>
          <div className="space-y-4">
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Skills</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{analytics.skills.total}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">New Skills</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{analytics.skills.new}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Skills per User</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {analytics.users.total > 0 
                  ? (analytics.skills.total / analytics.users.total).toFixed(2) 
                  : '0.00'}
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Session Stats</h2>
          <div className="space-y-4">
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Sessions</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{analytics.sessions.total}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">New Sessions</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{analytics.sessions.new}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Sessions per User</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {analytics.users.total > 0 
                  ? (analytics.sessions.total / analytics.users.total).toFixed(2) 
                  : '0.00'}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
