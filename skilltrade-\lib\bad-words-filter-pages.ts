/**
 * Bad Words Filter
 *
 * This module provides functions to validate and censor text containing bad words.
 * It uses a database to store and retrieve the list of bad words.
 *
 * This version is compatible with the pages directory (doesn't use server components).
 */

import { createClientSide } from './supabase';
import { createAdminClient } from './supabase-admin';

// Interface for bad words
export interface BadWord {
  id?: string;
  word: string;
  severity: number;
  active: boolean;
  created_at?: string;
}

// Interface for validation result
export interface ValidationResult {
  isValid: boolean;
  badWords: string[];
  censoredText: string;
}

// Cache for bad words to avoid frequent database calls
let badWordsCache: BadWord[] = [];
let lastCacheUpdate: number = 0;
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes in milliseconds

/**
 * Get all active bad words from the database
 */
export async function getBadWords(): Promise<BadWord[]> {
  const now = Date.now();

  // Return cached bad words if they're still valid
  if (badWordsCache.length > 0 && now - lastCacheUpdate < CACHE_TTL) {
    console.log('Using cached bad words, count:', badWordsCache.length);
    return badWordsCache;
  }

  try {
    console.log('Fetching bad words from database...');

    // Try using the admin client first (most reliable)
    console.log('Trying to get bad words using admin client...');
    const adminSupabase = createAdminClient();
    const { data: adminData, error: adminError } = await adminSupabase
      .from('bad_words')
      .select('*')
      .eq('active', true)
      .order('severity', { ascending: false })
      .order('word');

    if (!adminError && adminData) {
      console.log('Successfully fetched bad words via admin client, count:', adminData.length);
      badWordsCache = adminData;
      lastCacheUpdate = now;
      return adminData;
    }

    if (adminError) {
      console.error('Error fetching bad words via admin client:', adminError);
    }

    // Try client-side Supabase
    const supabase = createClientSide();

    // Try to get bad words using the function (for regular users)
    console.log('Trying to get bad words using RPC function...');
    const { data: functionData, error: functionError } = await supabase
      .rpc('get_bad_words');

    if (!functionError && functionData) {
      console.log('Successfully fetched bad words via RPC, count:', functionData.length);
      badWordsCache = functionData;
      lastCacheUpdate = now;
      return functionData;
    }

    if (functionError) {
      console.error('Error fetching bad words via RPC:', functionError);
    }

    // If that fails, try direct access
    console.log('Trying to get bad words via direct table access...');
    const { data, error } = await supabase
      .from('bad_words')
      .select('*')
      .eq('active', true)
      .order('severity', { ascending: false })
      .order('word');

    if (!error && data) {
      console.log('Successfully fetched bad words via direct access, count:', data.length);
      badWordsCache = data;
      lastCacheUpdate = now;
      return data;
    }

    if (error) {
      console.error('Error fetching bad words via direct access:', error);
    }

    // If all methods failed, return an empty array
    console.log('All methods to fetch bad words failed, returning empty array');
    return [];
  } catch (error) {
    console.error('Error in getBadWords:', error);
    return [];
  }
}

/**
 * Validate text for bad words
 * @param text The text to validate
 * @returns A validation result object
 */
export async function validateText(text: string): Promise<ValidationResult> {
  if (!text) {
    return { isValid: true, badWords: [], censoredText: '' };
  }

  const badWords = await getBadWords();

  if (badWords.length === 0) {
    console.log('No bad words found in database, skipping validation');
    return { isValid: true, badWords: [], censoredText: text };
  }

  // Convert text to lowercase for case-insensitive matching
  const lowerText = text.toLowerCase();
  const foundBadWords: string[] = [];
  let censoredText = text;

  // Check for each bad word
  badWords.forEach(badWord => {
    const word = badWord.word.toLowerCase();

    // Use word boundary regex to match whole words only
    const regex = new RegExp(`\\b${word}\\b`, 'gi');

    if (regex.test(lowerText)) {
      foundBadWords.push(badWord.word);

      // Replace bad words with asterisks
      censoredText = censoredText.replace(regex, '*'.repeat(word.length));
    }
  });

  return {
    isValid: foundBadWords.length === 0,
    badWords: foundBadWords,
    censoredText
  };
}

/**
 * Add a new bad word to the database
 * @param word The bad word to add
 * @param severity The severity level (1-5)
 * @returns The newly created bad word
 */
export async function addBadWord(word: string, severity: number): Promise<BadWord | null> {
  try {
    const adminSupabase = createAdminClient();

    const { data, error } = await adminSupabase
      .from('bad_words')
      .insert({
        word,
        severity,
        active: true
      })
      .select()
      .single();

    if (error) {
      console.error('Error adding bad word:', error);
      return null;
    }

    // Invalidate cache
    lastCacheUpdate = 0;

    return data;
  } catch (error) {
    console.error('Error in addBadWord:', error);
    return null;
  }
}

/**
 * Update an existing bad word
 * @param id The ID of the bad word to update
 * @param updates The updates to apply
 * @returns The updated bad word
 */
export async function updateBadWord(
  id: string,
  updates: { word?: string; severity?: number; active?: boolean }
): Promise<BadWord | null> {
  try {
    const adminSupabase = createAdminClient();

    const { data, error } = await adminSupabase
      .from('bad_words')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating bad word:', error);
      return null;
    }

    // Invalidate cache
    lastCacheUpdate = 0;

    return data;
  } catch (error) {
    console.error('Error in updateBadWord:', error);
    return null;
  }
}

/**
 * Delete a bad word
 * @param id The ID of the bad word to delete
 * @returns True if successful, false otherwise
 */
export async function deleteBadWord(id: string): Promise<boolean> {
  try {
    const adminSupabase = createAdminClient();

    const { error } = await adminSupabase
      .from('bad_words')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting bad word:', error);
      return false;
    }

    // Invalidate cache
    lastCacheUpdate = 0;

    return true;
  } catch (error) {
    console.error('Error in deleteBadWord:', error);
    return false;
  }
}

/**
 * Get all bad words for admin purposes (including inactive ones)
 * @returns Array of all bad words
 */
export async function getAllBadWords(): Promise<BadWord[]> {
  try {
    const adminSupabase = createAdminClient();

    const { data, error } = await adminSupabase
      .from('bad_words')
      .select('*')
      .order('severity', { ascending: false })
      .order('word');

    if (error) {
      console.error('Error fetching all bad words:', error);
      return [];
    }

    return data;
  } catch (error) {
    console.error('Error in getAllBadWords:', error);
    return [];
  }
}
