-- Migration to update direct_messages table permissions

-- Allow all users to select direct_messages
ALTER TABLE direct_messages ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Direct messages are viewable by participants" ON direct_messages;
DROP POLICY IF EXISTS "Direct messages can be inserted by participants" ON direct_messages;
DROP POLICY IF EXISTS "Direct messages can be updated by sender" ON direct_messages;
DROP POLICY IF EXISTS "Direct messages can be deleted by sender" ON direct_messages;

-- Create policies for direct_messages table
CREATE POLICY "Direct messages are viewable by participants"
ON direct_messages
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM conversation_participants
    WHERE conversation_participants.conversation_id = direct_messages.conversation_id
    AND conversation_participants.user_id = auth.uid()
  )
);

CREATE POLICY "Direct messages can be inserted by participants"
ON direct_messages
FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM conversation_participants
    WHERE conversation_participants.conversation_id = direct_messages.conversation_id
    AND conversation_participants.user_id = auth.uid()
  )
  AND
  sender_id = auth.uid()
);

CREATE POLICY "Direct messages can be updated by sender"
ON direct_messages
FOR UPDATE
USING (
  sender_id = auth.uid()
);

CREATE POLICY "Direct messages can be deleted by sender"
ON direct_messages
FOR DELETE
USING (
  sender_id = auth.uid()
);
