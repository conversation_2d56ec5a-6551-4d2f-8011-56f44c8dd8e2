'use client';

import { useState, useEffect } from 'react';
import { createClientSide } from '@/lib/supabase';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import MainHeader from '@/components/MainHeader';
import MainFooter from '@/components/MainFooter';
import SessionChat from '@/components/SessionChat';
import DisputeResolution from '@/components/DisputeResolution';

interface Session {
  id: string;
  skill_id: string;
  teacher_id: string;
  learner_id: string;
  scheduled_at: string;
  duration_hours: number;
  status: string;
  created_at: string;
  teacher_marked_complete?: boolean;
  learner_marked_complete?: boolean;
  teacher_marked_at?: string;
  learner_marked_at?: string;
  skill: {
    title: string;
    description: string;
  };
  teacher: {
    id: string;
    display_name: string | null;
    avatar_url: string | null;
    email: string;
  };
  learner: {
    id: string;
    display_name: string | null;
    avatar_url: string | null;
    email: string;
  };
}

interface Review {
  id: string;
  session_id: string;
  reviewer_id: string;
  rating: number;
  comment: string;
  created_at: string;
  reviewer: {
    display_name: string | null;
    avatar_url: string | null;
    email?: string;
  };
}

export default function SessionDetail({ params }: { params: { id: string } }) {
  const [session, setSession] = useState<Session | null>(null);
  const [review, setReview] = useState<Review | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isParticipant, setIsParticipant] = useState(false);
  const [isTeacher, setIsTeacher] = useState(false);
  const [message, setMessage] = useState<string | null>(null);
  const [currentUser, setCurrentUser] = useState<any>(null);
  const router = useRouter();
  const supabase = createClientSide();
  const { id } = params;

  useEffect(() => {
    const fetchSession = async () => {
      try {
        setLoading(true);

        // Get the current user
        const { data: { user } } = await supabase.auth.getUser();

        if (!user) {
          router.push('/login');
          return;
        }

        setCurrentUser(user);

        // Get the session with related data
        const { data: sessionData, error: sessionError } = await supabase
          .from('sessions')
          .select(`
            *,
            skill:skills(*),
            teacher:profiles!sessions_teacher_id_fkey(*),
            learner:profiles!sessions_learner_id_fkey(*)
          `)
          .eq('id', id)
          .single();

        if (sessionError) {
          throw sessionError;
        }

        setSession(sessionData);

        // Check if the current user is a participant
        const userIsParticipant =
          sessionData.teacher_id === user.id ||
          sessionData.learner_id === user.id;

        setIsParticipant(userIsParticipant);
        setIsTeacher(sessionData.teacher_id === user.id);

        if (!userIsParticipant) {
          router.push('/sessions');
          return;
        }

        // Get the review if it exists
        const { data: reviewData, error: reviewError } = await supabase
          .from('reviews')
          .select(`
            *,
            reviewer:profiles(display_name, avatar_url, email)
          `)
          .eq('session_id', id)
          .maybeSingle();

        if (reviewError) {
          throw reviewError;
        }

        setReview(reviewData);
      } catch (error: any) {
        setError(error.message || 'Failed to load session');
      } finally {
        setLoading(false);
      }
    };

    fetchSession();
  }, [id, router, supabase]);

  const updateSessionStatus = async (newStatus: string) => {
    try {
      if (newStatus === 'completed') {
        // For completion, use the mark_session_complete function
        try {
          const { error } = await supabase.rpc('mark_session_complete', {
            p_session_id: id,
            p_user_id: currentUser.id
          });

          if (error) {
            throw error;
          }
        } catch (rpcError: any) {
          console.error('RPC Error:', rpcError);

          // Fallback to direct update if RPC fails
          const isTeacherMarking = currentUser.id === session!.teacher_id;

          if (isTeacherMarking) {
            await supabase
              .from('sessions')
              .update({
                teacher_marked_complete: true,
                teacher_marked_at: new Date().toISOString()
              })
              .eq('id', id);
          } else {
            await supabase
              .from('sessions')
              .update({
                learner_marked_complete: true,
                learner_marked_at: new Date().toISOString()
              })
              .eq('id', id);
          }

          // Check if both have marked complete
          if ((isTeacherMarking && session!.learner_marked_complete) ||
              (!isTeacherMarking && session!.teacher_marked_complete)) {
            await supabase
              .from('sessions')
              .update({ status: 'completed' })
              .eq('id', id);
          }
        }

        // Refresh the session data
        const { data: updatedSession, error: refreshError } = await supabase
          .from('sessions')
          .select(`
            *,
            skill:skills(*),
            teacher:profiles!sessions_teacher_id_fkey(*),
            learner:profiles!sessions_learner_id_fkey(*)
          `)
          .eq('id', id)
          .single();

        if (refreshError) {
          throw refreshError;
        }

        setSession(updatedSession);

        // Determine the message based on who marked it complete
        const isTeacherMarking = currentUser.id === session!.teacher_id;
        const otherPartyMarked = isTeacherMarking
          ? updatedSession.learner_marked_complete
          : updatedSession.teacher_marked_complete;

        if (otherPartyMarked) {
          setMessage('Session marked as completed! Both parties have confirmed completion.');
        } else {
          setMessage('You have marked this session as complete. Waiting for the other party to confirm.');
        }
      } else {
        // For regular status updates (accept/decline)
        const { error } = await supabase
          .from('sessions')
          .update({ status: newStatus })
          .eq('id', id);

        if (error) {
          throw error;
        }

        // Update the local state
        setSession({
          ...session!,
          status: newStatus,
        });

        setMessage(`Session status updated to ${newStatus}`);
      }
    } catch (error: any) {
      setError(error.message || 'Failed to update session status');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <MainHeader />
        <div className="container mx-auto px-4 py-8">
          <div className="flex justify-center items-center h-64">
            <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
            <p className="ml-4 text-gray-700 dark:text-gray-300">Loading session...</p>
          </div>
        </div>
        <MainFooter />
      </div>
    );
  }

  if (!session) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <MainHeader />
        <div className="container mx-auto px-4 py-8">
          <div className="bg-white dark:bg-gray-800 rounded-xl p-8 text-center shadow-lg">
            <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Session not found</h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              The session you're looking for doesn't exist or you don't have permission to view it.
            </p>
            <Link
              href="/sessions"
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition inline-block"
            >
              Back to Sessions
            </Link>
          </div>
        </div>
        <MainFooter />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <MainHeader />

      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="mb-8">
            <Link
              href="/sessions"
              className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition flex items-center gap-2"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M19 12H5M12 19l-7-7 7-7"/>
              </svg>
              Back to Sessions
            </Link>
          </div>

          {error && (
            <div className="bg-red-100 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 px-4 py-3 rounded-lg mb-6">
              {error}
            </div>
          )}

          {message && (
            <div className="bg-green-100 dark:bg-green-900/30 border border-green-200 dark:border-green-800 text-green-800 dark:text-green-300 px-4 py-3 rounded-lg mb-6">
              {message}
            </div>
          )}

          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
            <div className="p-6 sm:p-8">
              <div className="flex justify-between items-start mb-6">
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">{session.skill.title}</h1>
                <span className={`px-3 py-1 rounded-full text-sm ${getStatusColor(session.status)}`}>
                  {session.status}
                </span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                <div>
                  <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Session Details</h2>
                  <div className="space-y-4">
                    <div>
                      <p className="text-gray-500 dark:text-gray-400 text-sm">Date & Time</p>
                      <p className="text-lg text-gray-700 dark:text-gray-300">{formatDateTime(session.scheduled_at)}</p>
                    </div>
                    <div>
                      <p className="text-gray-500 dark:text-gray-400 text-sm">Duration</p>
                      <p className="text-lg text-gray-700 dark:text-gray-300">{session.duration_hours} {session.duration_hours === 1 ? 'hour' : 'hours'}</p>
                    </div>
                    <div>
                      <p className="text-gray-500 dark:text-gray-400 text-sm">Your Role</p>
                      <p className="text-lg text-gray-700 dark:text-gray-300">{isTeacher ? 'Teacher' : 'Learner'}</p>
                    </div>
                    <div>
                      <p className="text-gray-500 dark:text-gray-400 text-sm">Credits</p>
                      <p className="text-lg text-gray-700 dark:text-gray-300">
                        <span className={isTeacher ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}>
                          {isTeacher ? `+${session.duration_hours}` : `-${session.duration_hours}`} credits
                        </span>
                        {session.status !== 'reviewed' && ' (pending completion)'}
                      </p>
                    </div>
                  </div>
                </div>

                <div>
                  <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
                    {isTeacher ? 'Learner' : 'Teacher'}
                  </h2>
                  <div className="flex items-start gap-4">
                    <div className="flex-shrink-0">
                      {(isTeacher ? session.learner : session.teacher).avatar_url ? (
                        <Image
                          src={(isTeacher ? session.learner : session.teacher).avatar_url!}
                          alt={(isTeacher ? session.learner : session.teacher).display_name || 'User'}
                          width={64}
                          height={64}
                          className="rounded-full object-cover border-2 border-white dark:border-gray-700 shadow-md"
                        />
                      ) : (
                        <div className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center border-2 border-white dark:border-gray-700 shadow-md">
                          <span className="text-xl text-gray-500 dark:text-gray-400">
                            {((isTeacher ? session.learner : session.teacher).display_name || 'U').charAt(0).toUpperCase()}
                          </span>
                        </div>
                      )}
                    </div>
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                        {(isTeacher ? session.learner : session.teacher).display_name ||
                         (isTeacher ? session.learner : session.teacher).email?.split('@')[0] ||
                         'Unknown User'}
                      </h3>
                      <Link
                        href={`/profile/${(isTeacher ? session.learner : session.teacher).id}`}
                        className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm mt-2 inline-block"
                      >
                        View profile
                      </Link>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mb-8">
                <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">About the Skill</h2>
                <p className="text-gray-600 dark:text-gray-300 whitespace-pre-line">
                  {session.skill.description}
                </p>
              </div>

              {/* Dispute Resolution section */}
              {session.status === 'disputed' && currentUser && (
                <div className="mb-8 border-t border-gray-200 dark:border-gray-700 pt-6">
                  <DisputeResolution
                    sessionId={session.id}
                    teacherId={session.teacher_id}
                    learnerId={session.learner_id}
                    currentUser={currentUser}
                    onResolutionComplete={() => {
                      // Refresh the session data
                      window.location.reload();
                    }}
                  />
                </div>
              )}

              {/* Chat section */}
              {(session.status === 'pending' || session.status === 'accepted' || session.status === 'completed' || session.status === 'disputed') && currentUser && (
                <div className="mb-8 border-t border-gray-200 dark:border-gray-700 pt-6">
                  <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Session Chat</h2>
                  <SessionChat
                    sessionId={session.id}
                    teacherId={session.teacher_id}
                    learnerId={session.learner_id}
                    currentUser={currentUser}
                  />
                </div>
              )}

              {review && (
                <div className="border-t border-gray-200 dark:border-gray-700 pt-8 mb-8">
                  <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Review</h2>
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 shadow-sm">
                    <div className="flex items-start gap-4">
                      <div className="flex-shrink-0">
                        {review.reviewer?.avatar_url ? (
                          <Image
                            src={review.reviewer.avatar_url}
                            alt={review.reviewer?.display_name || 'Reviewer'}
                            width={40}
                            height={40}
                            className="rounded-full object-cover border-2 border-white dark:border-gray-700"
                          />
                        ) : (
                          <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center border-2 border-white dark:border-gray-700">
                            <span className="text-sm text-gray-500 dark:text-gray-400">
                              {(review.reviewer?.display_name || 'R').charAt(0).toUpperCase()}
                            </span>
                          </div>
                        )}
                      </div>
                      <div>
                        <div className="flex items-center gap-2 mb-2">
                          <span className="font-medium text-gray-900 dark:text-white">
                            {review.reviewer?.display_name || review.reviewer?.email?.split('@')[0] || 'Anonymous Reviewer'}
                          </span>
                          <div className="flex items-center">
                            {[1, 2, 3, 4, 5].map((star) => (
                              <svg
                                key={star}
                                xmlns="http://www.w3.org/2000/svg"
                                className={`h-4 w-4 ${star <= review.rating ? 'text-yellow-500' : 'text-gray-300 dark:text-gray-600'}`}
                                viewBox="0 0 20 20"
                                fill="currentColor"
                              >
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                              </svg>
                            ))}
                            <span className="ml-1 text-xs text-gray-600 dark:text-gray-400">
                              ({review.rating}/5)
                            </span>
                          </div>
                        </div>
                        <p className="text-gray-600 dark:text-gray-300">
                          {review.comment}
                        </p>
                        <p className="text-gray-500 dark:text-gray-400 text-xs mt-2">
                          {new Date(review.created_at).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              <div className="flex flex-col sm:flex-row justify-between gap-4 border-t border-gray-200 dark:border-gray-700 pt-6 mt-6">
                <div className="flex flex-wrap gap-4">
                  {/* Status update actions */}
                  {session.status === 'pending' && isTeacher && (
                    <>
                      <button
                        onClick={() => updateSessionStatus('accepted')}
                        className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition font-medium"
                      >
                        Accept Session
                      </button>
                      <button
                        onClick={() => updateSessionStatus('cancelled')}
                        className="px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition font-medium"
                      >
                        Decline Session
                      </button>
                    </>
                  )}

                  {session.status === 'accepted' && (
                    <>
                      {/* Show completion button */}
                      <button
                        onClick={() => updateSessionStatus('completed')}
                        className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition font-medium"
                      >
                        Mark as Completed
                      </button>

                      {/* Show notification if other party has marked as complete */}
                      {isTeacher && session.learner_marked_complete && (
                        <div className="mt-2 text-green-600 dark:text-green-400 text-sm flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                          Learner has already marked this session as complete
                        </div>
                      )}

                      {!isTeacher && session.teacher_marked_complete && (
                        <div className="mt-2 text-green-600 dark:text-green-400 text-sm flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                          Teacher has already marked this session as complete
                        </div>
                      )}

                      {/* Auto-completion notice */}
                      {((isTeacher && session.teacher_marked_complete) || (!isTeacher && session.learner_marked_complete)) && (
                        <div className="mt-2 text-gray-500 dark:text-gray-400 text-xs">
                          Note: If the other party doesn't respond within 24 hours, the session will be automatically marked as complete.
                        </div>
                      )}
                    </>
                  )}

                  {session.status === 'completed' && !isTeacher && !review && (
                    <Link
                      href={`/sessions/${session.id}/review`}
                      className="px-6 py-3 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition inline-block font-medium"
                    >
                      Leave Review
                    </Link>
                  )}
                </div>

                <Link
                  href="/sessions"
                  className="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition text-center"
                >
                  Back to Sessions
                </Link>
              </div>
            </div>
          </div>
        </div>
      </main>

      <MainFooter />
    </div>
  );
}

// Helper function to format date and time
function formatDateTime(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString() + ' at ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
}

// Helper function to get status color
function getStatusColor(status: string): string {
  switch (status.toLowerCase()) {
    case 'pending':
      return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300';
    case 'accepted':
      return 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300';
    case 'completed':
      return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300';
    case 'cancelled':
      return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300';
    case 'reviewed':
      return 'bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300';
    default:
      return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300';
  }
}
