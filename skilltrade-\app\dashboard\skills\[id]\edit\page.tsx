'use client';

import { useState, useEffect } from 'react';
import { createClientSide } from '@/lib/supabase';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { XCircle } from 'lucide-react';
import SkillImageUpload from '@/components/SkillImageUpload';

interface AvailableDate {
  id: string;
  skill_id: string;
  date_time: string;
  duration_hours: number;
  is_booked: boolean;
  created_at: string;
  // Local UI state
  date?: string;
  time?: string;
}

interface Skill {
  id: string;
  title: string;
  description: string;
  tags: string[];
  is_active: boolean;
  difficulty_level?: 'beginner' | 'intermediate' | 'advanced';
  image_url?: string | null;
  owner_id: string;
  available_dates?: AvailableDate[];
}

export default function EditSkillPage({ params }: { params: { id: string } }) {
  const id = params.id;
  const [skill, setSkill] = useState<Skill | null>(null);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState('');
  const [isActive, setIsActive] = useState(true);
  const [difficultyLevel, setDifficultyLevel] = useState<'beginner' | 'intermediate' | 'advanced'>('intermediate');
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [availableDates, setAvailableDates] = useState<AvailableDate[]>([]);
  const [newDate, setNewDate] = useState('');
  const [newTime, setNewTime] = useState('');
  const [newDuration, setNewDuration] = useState(1);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [message, setMessage] = useState<string | null>(null);
  const router = useRouter();
  const supabase = createClientSide();

  useEffect(() => {
    const fetchSkill = async () => {
      try {
        setLoading(true);

        // Get the current user
        const { data: { user } } = await supabase.auth.getUser();

        if (!user) {
          router.push('/login');
          return;
        }

        // Get the skill
        const { data, error } = await supabase
          .from('skills')
          .select('*')
          .eq('id', id)
          .single();

        if (error) {
          throw error;
        }

        // Check if the skill belongs to the user
        if (data.owner_id !== user.id) {
          router.push('/dashboard/skills');
          return;
        }

        // Get available dates
        const { data: datesData, error: datesError } = await supabase
          .from('skill_available_dates')
          .select('*')
          .eq('skill_id', id)
          .order('date_time', { ascending: true });

        if (datesError) {
          console.error('Error fetching available dates:', datesError);
        }

        // Process dates for UI
        const processedDates = datesData?.map(date => {
          const dateObj = new Date(date.date_time);
          return {
            ...date,
            date: dateObj.toISOString().split('T')[0],
            time: dateObj.toTimeString().slice(0, 5)
          };
        }) || [];

        setSkill({...data, available_dates: processedDates});
        setTitle(data.title);
        setDescription(data.description || '');
        setTags(data.tags || []);
        setIsActive(data.is_active);
        setDifficultyLevel(data.difficulty_level || 'intermediate');
        setImageUrl(data.image_url);
        setAvailableDates(processedDates);
      } catch (error: any) {
        setError(error.message || 'Failed to load skill');
      } finally {
        setLoading(false);
      }
    };

    fetchSkill();
  }, [id, router, supabase]);

  const handleAddTag = () => {
    if (tagInput.trim() && !tags.includes(tagInput.trim())) {
      setTags([...tags, tagInput.trim()]);
      setTagInput('');
    }
  };

  const handleRemoveTag = (tag: string) => {
    setTags(tags.filter(t => t !== tag));
  };

  const handleAddDate = async () => {
    if (!newDate || !newTime) return;

    try {
      // Combine date and time
      const dateTime = new Date(`${newDate}T${newTime}`);

      // Check if date is valid
      if (isNaN(dateTime.getTime())) {
        throw new Error('Invalid date or time');
      }

      // Insert new available date
      const { data, error } = await supabase
        .from('skill_available_dates')
        .insert({
          skill_id: id,
          date_time: dateTime.toISOString(),
          duration_hours: newDuration,
          is_booked: false,
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      // Add to local state
      const newAvailableDate = {
        ...data,
        date: newDate,
        time: newTime,
      };

      setAvailableDates([...availableDates, newAvailableDate]);

      // Reset form
      setNewDate('');
      setNewTime('');
      setNewDuration(1);

      setMessage('Date added successfully');
      setTimeout(() => setMessage(null), 3000);
    } catch (error: any) {
      setError(error.message || 'Failed to add date');
      setTimeout(() => setError(null), 3000);
    }
  };

  const handleRemoveDate = async (dateId: string) => {
    try {
      // Delete from database
      const { error } = await supabase
        .from('skill_available_dates')
        .delete()
        .eq('id', dateId);

      if (error) {
        throw error;
      }

      // Remove from local state
      setAvailableDates(availableDates.filter(date => date.id !== dateId));

      setMessage('Date removed successfully');
      setTimeout(() => setMessage(null), 3000);
    } catch (error: any) {
      setError(error.message || 'Failed to remove date');
      setTimeout(() => setError(null), 3000);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    setError(null);
    setMessage(null);

    try {
      // Update the skill
      const { error } = await supabase
        .from('skills')
        .update({
          title,
          description,
          tags,
          is_active: isActive,
          difficulty_level: difficultyLevel,
          image_url: imageUrl,
        })
        .eq('id', id);

      if (error) {
        throw error;
      }

      setMessage('Skill updated successfully!');

      // Update the local state
      setSkill({
        ...skill!,
        title,
        description,
        tags,
        is_active: isActive,
        difficulty_level: difficultyLevel,
        image_url: imageUrl,
        available_dates: availableDates
      });
    } catch (error: any) {
      setError(error.message || 'Failed to update skill');
    } finally {
      setSaving(false);
    }
  };

  const handleDeleteSkill = async () => {
    setDeleting(true);
    setError(null);

    try {
      // Use the database function to delete the skill and all its dependencies
      const { data, error } = await supabase
        .rpc('delete_skill_with_dependencies', { skill_id_param: id });

      if (error) {
        throw error;
      }

      // Redirect to skills page
      router.push('/dashboard/skills');
      router.refresh();
    } catch (error: any) {
      setError(error.message || 'Failed to delete skill');
      setDeleting(false);
      setShowDeleteConfirm(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p className="ml-4 text-gray-700 dark:text-gray-300">Loading skill...</p>
      </div>
    );
  }

  if (!skill) {
    return (
      <div className="bg-gray-800 rounded-lg p-8 text-center">
        <h2 className="text-xl font-semibold mb-4">Skill not found</h2>
        <p className="text-gray-300 mb-6">
          The skill you're looking for doesn't exist or you don't have permission to edit it.
        </p>
        <Link
          href="/dashboard/skills"
          className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-6 rounded-md transition inline-block"
        >
          Back to My Skills
        </Link>
      </div>
    );
  }

  return (
    <main className="container mx-auto px-4 py-8">
      <div className="max-w-2xl mx-auto">
        <div className="mb-8">
          <h1 className="text-2xl font-bold">Edit Skill</h1>
          <p className="text-gray-400 mt-2">
            Update your skill details and availability
          </p>
        </div>

        {error && (
          <div className="bg-red-900/30 border border-red-800 text-red-300 px-4 py-3 rounded-lg mb-6">
            {error}
          </div>
        )}

        {message && (
          <div className="bg-green-900/30 border border-green-800 text-green-300 px-4 py-3 rounded-lg mb-6">
            {message}
          </div>
        )}

        {/* Delete Confirmation Modal */}
        {showDeleteConfirm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full">
              <h3 className="text-xl font-bold mb-4">Delete Skill</h3>
              <p className="mb-6">
                Are you sure you want to delete this skill? This action cannot be undone.
                {skill && skill.available_dates && skill.available_dates.some(date => date.is_booked) && (
                  <span className="block mt-2 text-yellow-400">
                    Note: This skill has booked sessions. Deleting it will affect existing bookings.
                  </span>
                )}
              </p>
              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => setShowDeleteConfirm(false)}
                  className="px-4 py-2 bg-gray-700 text-white rounded-md hover:bg-gray-600 transition"
                  disabled={deleting}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleDeleteSkill}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition"
                  disabled={deleting}
                >
                  {deleting ? 'Deleting...' : 'Delete'}
                </button>
              </div>
            </div>
          </div>
        )}

        <div className="bg-gray-800 rounded-lg p-6 shadow-lg">
          <form onSubmit={handleSubmit}>
            <div className="mb-6">
              <label htmlFor="title" className="block text-sm font-medium mb-2">
                Skill Title <span className="text-red-400">*</span>
              </label>
              <input
                id="title"
                type="text"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              />
            </div>

            <div className="mb-6">
              <label htmlFor="description" className="block text-sm font-medium mb-2">
                Description
              </label>
              <textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={5}
                className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              ></textarea>
            </div>

            <div className="mb-6">
              <label htmlFor="difficulty" className="block text-sm font-medium mb-2">
                Difficulty Level
              </label>
              <select
                id="difficulty"
                value={difficultyLevel}
                onChange={(e) => setDifficultyLevel(e.target.value as 'beginner' | 'intermediate' | 'advanced')}
                className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="beginner">Beginner</option>
                <option value="intermediate">Intermediate</option>
                <option value="advanced">Advanced</option>
              </select>
            </div>

            <div className="mb-6">
              <label className="block text-sm font-medium mb-2">
                Skill Image
              </label>
              <p className="text-sm text-gray-400 mb-2">
                Upload an image that represents your skill (optional)
              </p>
              {skill && (
                <SkillImageUpload
                  skillId={skill.id}
                  ownerId={skill.owner_id}
                  url={imageUrl}
                  onUpload={(url) => setImageUrl(url)}
                  onRemove={() => setImageUrl(null)}
                />
              )}
            </div>

            <div className="mb-6">
              <label className="block text-sm font-medium mb-2">
                Tags
              </label>
              <div className="flex flex-wrap gap-2 mb-2">
                {tags.map((tag, index) => (
                  <div key={index} className="bg-gray-700 px-3 py-1 rounded-full text-sm flex items-center">
                    {tag}
                    <button
                      type="button"
                      onClick={() => handleRemoveTag(tag)}
                      className="ml-2 text-gray-400 hover:text-red-400"
                    >
                      <XCircle size={16} />
                    </button>
                  </div>
                ))}
              </div>
              <div className="flex">
                <input
                  type="text"
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddTag())}
                  placeholder="Add a tag"
                  className="flex-1 px-4 py-2 bg-gray-700 border border-gray-600 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <button
                  type="button"
                  onClick={handleAddTag}
                  className="px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700 transition"
                >
                  Add
                </button>
              </div>
            </div>

            <div className="mb-6">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={isActive}
                  onChange={(e) => setIsActive(e.target.checked)}
                  className="h-5 w-5 rounded border-gray-600 bg-gray-700 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm">Active (visible to others)</span>
              </label>
            </div>

            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-4">Available Dates</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                {availableDates.map((date) => {
                  const dateObj = new Date(date.date_time);
                  return (
                    <div
                      key={date.id}
                      className={`p-3 rounded-lg border ${date.is_booked ? 'bg-gray-700 border-gray-600' : 'bg-gray-700/50 border-gray-600'}`}
                    >
                      <div className="flex justify-between items-center">
                        <div>
                          <div className="font-medium">
                            {dateObj.toLocaleDateString(undefined, { weekday: 'short', month: 'short', day: 'numeric' })}
                          </div>
                          <div className="text-sm text-gray-400">
                            {dateObj.toLocaleTimeString(undefined, { hour: '2-digit', minute: '2-digit' })}
                            {' • '}
                            {date.duration_hours} {date.duration_hours === 1 ? 'hour' : 'hours'}
                          </div>
                        </div>
                        <div className="flex items-center">
                          <span className={`text-xs px-2 py-1 rounded-full mr-2 ${date.is_booked ? 'bg-yellow-900/50 text-yellow-300' : 'bg-green-900/50 text-green-300'}`}>
                            {date.is_booked ? 'Booked' : 'Available'}
                          </span>
                          {!date.is_booked && (
                            <button
                              type="button"
                              onClick={() => handleRemoveDate(date.id)}
                              className="text-gray-400 hover:text-red-400"
                              title="Remove date"
                            >
                              <XCircle size={18} />
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>

              <div className="bg-gray-700 p-4 rounded-lg">
                <h4 className="text-sm font-medium mb-3">Add New Date</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-3">
                  <div>
                    <label htmlFor="newDate" className="block text-xs text-gray-400 mb-1">Date</label>
                    <input
                      type="date"
                      id="newDate"
                      value={newDate}
                      onChange={(e) => setNewDate(e.target.value)}
                      className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  <div>
                    <label htmlFor="newTime" className="block text-xs text-gray-400 mb-1">Time</label>
                    <input
                      type="time"
                      id="newTime"
                      value={newTime}
                      onChange={(e) => setNewTime(e.target.value)}
                      className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  <div>
                    <label htmlFor="newDuration" className="block text-xs text-gray-400 mb-1">Duration (hours)</label>
                    <select
                      id="newDuration"
                      value={newDuration}
                      onChange={(e) => setNewDuration(Number(e.target.value))}
                      className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      {[0.5, 1, 1.5, 2, 2.5, 3, 3.5, 4].map(value => (
                        <option key={value} value={value}>{value}</option>
                      ))}
                    </select>
                  </div>
                </div>
                <button
                  type="button"
                  onClick={handleAddDate}
                  className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition"
                >
                  Add Date
                </button>
              </div>
            </div>

            <div className="flex justify-between items-center">
              <button
                type="button"
                onClick={() => setShowDeleteConfirm(true)}
                className="px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition"
              >
                Delete Skill
              </button>

              <div className="flex space-x-3">
                <Link
                  href={`/dashboard/skills/${id}`}
                  className="px-6 py-3 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition"
                >
                  Cancel
                </Link>
                <button
                  type="submit"
                  disabled={saving}
                  className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {saving ? 'Saving...' : 'Save Changes'}
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </main>
  );
}
