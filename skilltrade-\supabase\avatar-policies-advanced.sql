-- Advanced Avatar Storage Policies for Skilltrade
-- This version creates more specific policies that ensure users can only access their own avatars
-- Run this SQL in the Supabase SQL Editor

-- First, remove any existing policies for the avatars bucket
BEGIN;

DELETE FROM storage.policies 
WHERE bucket_id = (SELECT id FROM storage.buckets WHERE name = 'avatars');

-- 1. Policy for viewing avatars (public access)
-- Everyone can view all avatars
INSERT INTO storage.policies (name, bucket_id, operation, definition)
VALUES (
  'Avatars are viewable by everyone',
  (SELECT id FROM storage.buckets WHERE name = 'avatars'),
  'SELECT',
  'true'
);

-- 2. Policy for uploading avatars (own folder only)
-- Users can only upload to a folder matching their user ID
INSERT INTO storage.policies (name, bucket_id, operation, definition)
VALUES (
  'Users can upload avatars to their own folder',
  (SELECT id FROM storage.buckets WHERE name = 'avatars'),
  'INSERT',
  '(auth.uid() = SUBSTRING(path FROM 1 FOR POSITION(''/'', path) - 1)::uuid)'
);

-- 3. Policy for updating avatars (own folder only)
-- Users can only update files in a folder matching their user ID
INSERT INTO storage.policies (name, bucket_id, operation, definition)
VALUES (
  'Users can update avatars in their own folder',
  (SELECT id FROM storage.buckets WHERE name = 'avatars'),
  'UPDATE',
  '(auth.uid() = SUBSTRING(path FROM 1 FOR POSITION(''/'', path) - 1)::uuid)'
);

-- 4. Policy for deleting avatars (own folder only)
-- Users can only delete files in a folder matching their user ID
INSERT INTO storage.policies (name, bucket_id, operation, definition)
VALUES (
  'Users can delete avatars in their own folder',
  (SELECT id FROM storage.buckets WHERE name = 'avatars'),
  'DELETE',
  '(auth.uid() = SUBSTRING(path FROM 1 FOR POSITION(''/'', path) - 1)::uuid)'
);

COMMIT;
