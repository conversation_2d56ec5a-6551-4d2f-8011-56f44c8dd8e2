'use client';

import { useState, useEffect, Suspense } from 'react';
import { createClientSide } from '@/lib/supabase';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import MainHeader from '@/components/MainHeader';
import MainFooter from '@/components/MainFooter';

// Define interfaces for our data
interface Owner {
  display_name: string | null;
  avatar_url: string | null;
  email: string | null;
}

interface Review {
  id: string;
  rating: string;
  comment: string;
  created_at: string;
  reviewer: {
    display_name: string | null;
  };
}

interface Session {
  id: string;
  scheduled_at: string;
  status: string;
}

interface AvailableDate {
  id: string;
  skill_id: string;
  date_time: string;
  duration_hours: number;
  is_booked: boolean;
  created_at: string;
}

interface Skill {
  id: string;
  title: string;
  description: string;
  tags: string[];
  is_active: boolean;
  created_at: string;
  owner_id: string;
  owner: Owner;
  difficulty_level?: 'beginner' | 'intermediate' | 'advanced';
  image_url?: string | null;
  reviews: Review[];
  sessions: Session[];
  session_count: number;
  avg_rating: number;
  last_session_date: string | null;
  available_dates?: AvailableDate[];
}

// Search params component wrapped in Suspense
function SearchParamsHandler({
  setSearchQuery,
  setSelectedTags
}: {
  setSearchQuery: (query: string) => void,
  setSelectedTags: (tags: string[]) => void
}) {
  const searchParams = useSearchParams();

  useEffect(() => {
    const query = searchParams.get('q');
    if (query) {
      setSearchQuery(query);
    }

    const tags = searchParams.get('tags');
    if (tags) {
      // Normalize tags to lowercase for consistent matching
      setSelectedTags(tags.split(',').map(tag => tag.toLowerCase()));
    }
  }, [searchParams, setSearchQuery, setSelectedTags]);

  return null;
}

export default function ExplorePage() {
  const [skills, setSkills] = useState<Skill[]>([]);
  const [filteredSkills, setFilteredSkills] = useState<Skill[]>([]);
  const [uniqueTags, setUniqueTags] = useState<string[]>([]);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const router = useRouter();
  const supabase = createClientSide();

  // SearchParamsHandler is used in the JSX with Suspense

  // Fetch skills from Supabase
  useEffect(() => {
    const fetchSkills = async () => {
      try {
        setLoading(true);

        // Fetch skills with owner information
        const { data: skillsData, error: skillsError } = await supabase
          .from('skills')
          .select(`
            *,
            owner:owner_id (display_name, avatar_url, email)
          `)
          .eq('is_active', true)
          .order('created_at', { ascending: false });

        if (skillsError) {
          throw skillsError;
        }

        if (!skillsData || skillsData.length === 0) {
          setSkills([]);
          setLoading(false);
          return;
        }

        // Prepare enhanced skills array
        const enhancedSkills = await Promise.all(skillsData.map(async (skill) => {
          // First get session IDs for this skill
          const { data: sessionIds, error: sessionIdsError } = await supabase
            .from('sessions')
            .select('id')
            .eq('skill_id', skill.id);

          if (sessionIdsError) {
            console.error('Error fetching session IDs:', sessionIdsError);
          }

          // Then fetch reviews for these sessions
          let reviewsData: any[] = [];
          if (sessionIds && sessionIds.length > 0) {
            const sessionIdArray = sessionIds.map(s => s.id);
            const { data: reviews, error: reviewsError } = await supabase
              .from('reviews')
              .select(`
                id,
                rating,
                comment,
                created_at,
                reviewer:reviewer_id (display_name),
                session_id
              `)
              .in('session_id', sessionIdArray)
              .order('created_at', { ascending: false })
              .limit(3);

            if (reviewsError) {
              console.error('Error fetching reviews:', reviewsError);
            } else {
              reviewsData = reviews || [];
            }
          }

          // Fetch sessions for this skill
          const { data: sessionsData, error: sessionsError } = await supabase
            .from('sessions')
            .select(`
              id,
              scheduled_at,
              status
            `)
            .eq('skill_id', skill.id)
            .order('scheduled_at', { ascending: false })
            .limit(5);

          if (sessionsError) {
            console.error('Error fetching sessions:', sessionsError);
          }

          // Get session count
          const { count: sessionCount, error: countError } = await supabase
            .from('sessions')
            .select('id', { count: 'exact', head: true })
            .eq('skill_id', skill.id);

          if (countError) {
            console.error('Error fetching session count:', countError);
          }

          // Calculate average rating (out of 5 stars)
          let avgRating = 0;
          if (reviewsData && reviewsData.length > 0) {
            const totalStars = reviewsData.reduce((sum, review) => {
              // Handle both numeric ratings and positive/negative ratings
              if (typeof review.rating === 'number') {
                return sum + review.rating;
              } else if (review.rating === 'positive') {
                return sum + 5; // Positive is 5 stars
              } else {
                return sum + 1; // Negative is 1 star
              }
            }, 0);
            avgRating = (totalStars / reviewsData.length) * 20; // Convert to percentage (1-5 stars → 20-100%)
          }

          // Get last session date
          const lastSession = sessionsData && sessionsData.length > 0 ? sessionsData[0] : null;
          const lastSessionDate = lastSession ? lastSession.scheduled_at : null;

          // Get available dates
          const { data: availableDates, error: availableDatesError } = await supabase
            .from('skill_available_dates')
            .select('*')
            .eq('skill_id', skill.id)
            .eq('is_booked', false)
            .gte('date_time', new Date().toISOString())
            .order('date_time', { ascending: true })
            .limit(3);

          if (availableDatesError) {
            console.error('Error fetching available dates:', availableDatesError);
          }

          // Return enhanced skill object
          return {
            ...skill,
            reviews: reviewsData || [],
            sessions: sessionsData || [],
            session_count: sessionCount || 0,
            avg_rating: avgRating,
            last_session_date: lastSessionDate,
            available_dates: availableDates || []
          };
        }));

        setSkills(enhancedSkills);

        // Extract unique tags
        const allTags = skillsData?.flatMap(skill => skill.tags || []) || [];
        setUniqueTags([...new Set(allTags)]);

      } catch (error: any) {
        setError(error.message || 'Failed to load skills');
      } finally {
        setLoading(false);
      }
    };

    fetchSkills();
  }, [supabase]);

  // Filter skills based on search query and selected tags
  useEffect(() => {
    let result = [...skills];

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(skill =>
        skill.title.toLowerCase().includes(query) ||
        skill.description.toLowerCase().includes(query) ||
        (skill.tags && skill.tags.some(tag => tag.toLowerCase().includes(query)))
      );
    }

    // Filter by selected tags
    if (selectedTags.length > 0) {
      result = result.filter(skill =>
        skill.tags && selectedTags.every(tag =>
          skill.tags.some(skillTag => skillTag.toLowerCase() === tag.toLowerCase())
        )
      );
    }

    setFilteredSkills(result);
  }, [skills, searchQuery, selectedTags]);

  // Handle tag selection
  const handleTagToggle = (tag: string) => {
    const normalizedTag = tag.toLowerCase();
    setSelectedTags(prev => {
      // Check if tag is already selected (case-insensitive)
      const tagIndex = prev.findIndex(t => t.toLowerCase() === normalizedTag);
      if (tagIndex >= 0) {
        // Remove tag if already selected
        return prev.filter((_, i) => i !== tagIndex);
      } else {
        // Add tag if not selected
        return [...prev, normalizedTag];
      }
    });
  };

  // Handle search submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();

    // Update URL with search parameters
    const params = new URLSearchParams();
    if (searchQuery) {
      params.set('q', searchQuery);
    }
    if (selectedTags.length > 0) {
      params.set('tags', selectedTags.join(','));
    }

    const newUrl = `/explore${params.toString() ? `?${params.toString()}` : ''}`;
    router.push(newUrl);
  };

  // Clear all filters
  const clearFilters = () => {
    setSearchQuery('');
    setSelectedTags([]);
    router.push('/explore');
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-white">
      <MainHeader />

      {/* Wrap the search params handler in Suspense */}
      <Suspense fallback={null}>
        <SearchParamsHandler
          setSearchQuery={setSearchQuery}
          setSelectedTags={setSelectedTags}
        />
      </Suspense>

      <main className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-2 text-gray-900 dark:text-white">Explore Skills</h1>
        <p className="text-gray-600 dark:text-gray-400 mb-6">Discover skills you can learn from our community members</p>

        {/* Search Form */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-md mb-8">
          <form onSubmit={handleSearch} className="flex flex-col md:flex-row gap-4">
            <div className="flex-grow">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search for skills, topics, or keywords..."
                className="w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div className="flex gap-2">
              <button
                type="submit"
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition font-medium"
              >
                Search
              </button>
              {(searchQuery || selectedTags.length > 0) && (
                <button
                  type="button"
                  onClick={clearFilters}
                  className="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition"
                >
                  Clear
                </button>
              )}
            </div>
          </form>
        </div>

        {error && (
          <div className="bg-red-100 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 px-4 py-3 rounded-lg mb-6">
            {error}
          </div>
        )}

        <div className="flex flex-col md:flex-row gap-8">
          {/* Sidebar */}
          <div className="md:w-1/4">
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-md sticky top-4">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Filter by Tags</h2>
                {selectedTags.length > 0 && (
                  <button
                    onClick={() => setSelectedTags([])}
                    className="text-sm text-blue-600 dark:text-blue-400 hover:underline"
                  >
                    Clear
                  </button>
                )}
              </div>
              <div className="space-y-2">
                {uniqueTags.length > 0 ? (
                  uniqueTags.map((tag, index) => (
                    <div key={index} className="flex items-center">
                      <label className="flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={selectedTags.some(t => t.toLowerCase() === tag.toLowerCase())}
                          onChange={() => handleTagToggle(tag)}
                          className="h-4 w-4 rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500 bg-white dark:bg-gray-700"
                        />
                        <span className="ml-2 text-gray-700 dark:text-gray-300">{tag}</span>
                      </label>
                    </div>
                  ))
                ) : (
                  <p className="text-gray-500 dark:text-gray-400">No tags available</p>
                )}
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="md:w-3/4">
            {loading ? (
              <div className="flex justify-center items-center h-64">
                <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                <p className="ml-4 text-gray-700 dark:text-gray-300">Loading skills...</p>
              </div>
            ) : filteredSkills.length > 0 ? (
              <>
                <div className="mb-4 text-gray-600 dark:text-gray-400">
                  Showing {filteredSkills.length} {filteredSkills.length === 1 ? 'skill' : 'skills'}
                  {searchQuery && <span> for "{searchQuery}"</span>}
                  {selectedTags.length > 0 && (
                    <span> with tags: {selectedTags.map(tag => (
                      <span key={tag} className="inline-block bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 px-2 py-1 rounded-full text-xs mx-1">
                        {tag}
                      </span>
                    ))}</span>
                  )}
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {filteredSkills.map((skill) => (
                    <div key={skill.id} className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition">
                      {/* Skill Image */}
                      {skill.image_url && (
                        <div className="w-full h-48 overflow-hidden">
                          <img
                            src={skill.image_url}
                            alt={skill.title}
                            className="w-full h-full object-cover"
                          />
                        </div>
                      )}

                      <div className="p-6">
                        {/* Header with teacher info */}
                        <div className="flex items-start gap-4">
                          <div className="w-12 h-12 rounded-full bg-gray-200 dark:bg-gray-700 flex-shrink-0 overflow-hidden">
                            {skill.owner?.avatar_url ? (
                              <img
                                src={skill.owner.avatar_url}
                                alt={skill.owner?.display_name || 'User'}
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <div className="w-full h-full flex items-center justify-center">
                                <span className="text-lg text-gray-500 dark:text-gray-400">
                                  {skill.owner?.display_name?.charAt(0) || skill.owner?.email?.charAt(0) || '?'}
                                </span>
                              </div>
                            )}
                          </div>
                          <div className="flex-grow">
                            <h2 className="text-xl font-semibold mb-1 text-gray-900 dark:text-white">{skill.title}</h2>
                            <div className="flex items-center gap-2">
                              <p className="text-gray-500 dark:text-gray-400 text-sm">
                                Taught by {skill.owner?.display_name || skill.owner?.email || 'Unknown'}
                              </p>

                              {/* Difficulty Level Badge */}
                              {skill.difficulty_level && (
                                <span className={`text-xs px-2 py-1 rounded-full ${
                                  skill.difficulty_level === 'beginner'
                                    ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'
                                    : skill.difficulty_level === 'intermediate'
                                    ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300'
                                    : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'
                                }`}>
                                  {skill.difficulty_level.charAt(0).toUpperCase() + skill.difficulty_level.slice(1)}
                                </span>
                              )}
                            </div>
                          </div>

                          {/* Rating badge */}
                          {skill.reviews && skill.reviews.length > 0 && (
                            <div className="flex-shrink-0 bg-gray-100 dark:bg-gray-700 rounded-full px-2 py-1 flex items-center">
                              <span className="text-yellow-500 mr-1">★</span>
                              <span className="text-sm font-medium">
                                {(skill.avg_rating / 20).toFixed(1)}/5
                              </span>
                            </div>
                          )}
                        </div>

                        {/* Description */}
                        <p className="text-gray-600 dark:text-gray-300 my-4 line-clamp-3">
                          {skill.description || 'No description provided'}
                        </p>

                        {/* Stats row */}
                        <div className="flex flex-wrap gap-4 mb-4 text-sm text-gray-500 dark:text-gray-400">
                          <div className="flex items-center">
                            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            <span>
                              {skill.session_count} {skill.session_count === 1 ? 'session' : 'sessions'} completed
                            </span>
                          </div>

                          {skill.last_session_date && (
                            <div className="flex items-center">
                              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                              </svg>
                              <span>
                                Last taught {formatDate(skill.last_session_date)}
                              </span>
                            </div>
                          )}
                        </div>

                        {/* Tags */}
                        <div className="flex flex-wrap gap-2 mb-4">
                          {skill.tags && skill.tags.map((tag, index) => (
                            <span
                              key={index}
                              className={`px-2 py-1 rounded-full text-xs cursor-pointer ${
                                selectedTags.some(t => t.toLowerCase() === tag.toLowerCase())
                                  ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300'
                                  : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                              }`}
                              onClick={() => handleTagToggle(tag)}
                            >
                              {tag}
                            </span>
                          ))}
                        </div>

                        {/* Available dates */}
                        {skill.available_dates && skill.available_dates.length > 0 && (
                          <div className="mb-4">
                            <div className="flex items-center mb-2">
                              <svg className="w-4 h-4 mr-1 text-blue-500 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                              </svg>
                              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                Available Dates
                              </span>
                            </div>
                            <div className="space-y-1">
                              {skill.available_dates.map((date) => (
                                <div key={date.id} className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                                  <span className="w-1.5 h-1.5 rounded-full bg-blue-500 mr-2"></span>
                                  <span>
                                    {new Date(date.date_time).toLocaleDateString()} at {new Date(date.date_time).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                                    <span className="text-gray-500 dark:text-gray-500 ml-1">
                                      ({date.duration_hours}h)
                                    </span>
                                  </span>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Latest review */}
                        {skill.reviews && skill.reviews.length > 0 && (
                          <div className="mb-4 bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                            <div className="flex items-center mb-2">
                              <span className="text-sm font-medium text-gray-700 dark:text-gray-300 mr-2">
                                {skill.reviews[0].reviewer?.display_name || 'Anonymous'} • {formatDate(skill.reviews[0].created_at)}
                              </span>
                              <div className="flex items-center">
                                {[1, 2, 3, 4, 5].map((star) => (
                                  <svg
                                    key={star}
                                    xmlns="http://www.w3.org/2000/svg"
                                    className={`h-3 w-3 ${star <= (typeof skill.reviews[0].rating === 'number' ? skill.reviews[0].rating : (skill.reviews[0].rating === 'positive' ? 5 : 1)) ? 'text-yellow-500' : 'text-gray-300 dark:text-gray-600'}`}
                                    viewBox="0 0 20 20"
                                    fill="currentColor"
                                  >
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                ))}
                              </div>
                            </div>
                            <p className="text-gray-600 dark:text-gray-400 text-sm line-clamp-2">
                              "{skill.reviews[0].comment}"
                            </p>
                          </div>
                        )}

                        {/* Action button */}
                        <div className="flex justify-end">
                          <Link
                            href={`/skills/${skill.id}`}
                            className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition text-sm"
                          >
                            Learn More
                          </Link>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </>
            ) : (
              <div className="bg-white dark:bg-gray-800 rounded-xl p-8 text-center shadow-md">
                <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
                  {searchQuery || selectedTags.length > 0
                    ? 'No skills match your search criteria'
                    : 'No skills available yet'}
                </h2>
                <p className="text-gray-600 dark:text-gray-300 mb-6">
                  {searchQuery || selectedTags.length > 0
                    ? 'Try adjusting your search or filters to find more skills.'
                    : 'Be the first to share your knowledge with the community!'}
                </p>
                {searchQuery || selectedTags.length > 0 ? (
                  <button
                    onClick={clearFilters}
                    className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-6 rounded-lg transition inline-block"
                  >
                    Clear Filters
                  </button>
                ) : (
                  <Link
                    href="/signup"
                    className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-6 rounded-lg transition inline-block"
                  >
                    Sign Up to Add Skills
                  </Link>
                )}
              </div>
            )}
          </div>
        </div>
      </main>

      <MainFooter />
    </div>
  );
}

// Helper function to format dates in a user-friendly way
function formatDate(dateString: string | null): string {
  if (!dateString) return 'N/A';

  const date = new Date(dateString);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - date.getTime());
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 0) {
    return 'Today';
  } else if (diffDays === 1) {
    return 'Yesterday';
  } else if (diffDays < 7) {
    return `${diffDays} days ago`;
  } else if (diffDays < 30) {
    const weeks = Math.floor(diffDays / 7);
    return `${weeks} ${weeks === 1 ? 'week' : 'weeks'} ago`;
  } else if (diffDays < 365) {
    const months = Math.floor(diffDays / 30);
    return `${months} ${months === 1 ? 'month' : 'months'} ago`;
  } else {
    return date.toLocaleDateString();
  }
}
