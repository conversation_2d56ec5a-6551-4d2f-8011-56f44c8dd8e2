'use client';

import { useState, Children } from 'react';
import { cn } from '@/lib/utils';

export type TabItem = {
  id: string;
  label: string;
  icon?: React.ReactNode;
};

interface ProfileTabsProps {
  tabs: TabItem[];
  defaultTab?: string;
  onChange?: (tabId: string) => void;
  className?: string;
  children?: React.ReactNode;
}

export function ProfileTabs({
  tabs,
  defaultTab,
  onChange,
  className,
  children,
}: ProfileTabsProps) {
  // Convert children to array to filter them
  const childrenArray = Children.toArray(children);
  const [activeTab, setActiveTab] = useState<string>(defaultTab || tabs[0]?.id || '');

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    if (onChange) {
      onChange(tabId);
    }
  };

  return (
    <div className={cn('mt-4', className)}>
      {childrenArray.map((child, index) => {
        // Check if the child corresponds to the active tab
        if (index < tabs.length && tabs[index].id === activeTab) {
          return (
            <div key={tabs[index].id} className="animate-in fade-in-50 duration-300">
              {child}
            </div>
          );
        }
        return null;
      })}
    </div>
  );
}

interface ProfileTabProps {
  id: string;
  children: React.ReactNode;
}

export function ProfileTab({ id, children }: ProfileTabProps) {
  return <div id={id}>{children}</div>;
}
