'use client';

import { useState, useEffect } from 'react';
import { X } from 'lucide-react';

export default function EarlyAccessBanner() {
  const [isVisible, setIsVisible] = useState(true);
  const [hasBeenDismissed, setHasBeenDismissed] = useState(false);

  useEffect(() => {
    // Check if the banner has been dismissed before
    const dismissed = localStorage.getItem('earlyAccessBannerDismissed');
    if (dismissed === 'true') {
      setIsVisible(false);
      setHasBeenDismissed(true);
    }
  }, []);

  const dismissBanner = () => {
    setIsVisible(false);
    // Remember the dismissal for 24 hours
    localStorage.setItem('earlyAccessBannerDismissed', 'true');
    
    // Set a timeout to clear the dismissal after 24 hours
    setTimeout(() => {
      localStorage.removeItem('earlyAccessBannerDismissed');
      setHasBeenDismissed(false);
    }, 24 * 60 * 60 * 1000); // 24 hours
  };

  if (!isVisible) return null;

  return (
    <div className="bg-amber-500 dark:bg-amber-700 text-black dark:text-white py-2 px-4">
      <div className="container mx-auto flex items-center justify-between">
        <div className="flex items-center">
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            className="h-5 w-5 mr-2" 
            viewBox="0 0 20 20" 
            fill="currentColor"
          >
            <path 
              fillRule="evenodd" 
              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" 
              clipRule="evenodd" 
            />
          </svg>
          <p className="text-sm md:text-base">
            <span className="font-bold">Early Access:</span> Skilltrade is in active development. You may encounter bugs or glitches. We appreciate your patience and feedback!
          </p>
        </div>
        <button 
          onClick={dismissBanner} 
          className="ml-4 text-black dark:text-white hover:text-gray-800 dark:hover:text-gray-200"
          aria-label="Dismiss message"
        >
          <X size={18} />
        </button>
      </div>
    </div>
  );
}
