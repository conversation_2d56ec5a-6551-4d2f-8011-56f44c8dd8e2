-- Create a new table for skill available dates
CREATE TABLE IF NOT EXISTS skill_available_dates (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  skill_id UUID NOT NULL REFERENCES skills(id) ON DELETE CASCADE,
  date_time TIMESTAMPTZ NOT NULL,
  duration_hours INTEGER NOT NULL DEFAULT 1,
  is_booked BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  
  -- Ensure each skill can only have a specific date/time once
  CONSTRAINT unique_skill_datetime UNIQUE (skill_id, date_time)
);

-- Add RLS policies
ALTER TABLE skill_available_dates ENABLE ROW LEVEL SECURITY;

-- Policy to allow anyone to read available dates
CREATE POLICY "Anyone can read available dates" 
  ON skill_available_dates 
  FOR SELECT 
  USING (true);

-- Policy to allow skill owners to insert available dates
CREATE POLICY "Skill owners can insert available dates" 
  ON skill_available_dates 
  FOR INSERT 
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM skills 
      WHERE skills.id = skill_available_dates.skill_id 
      AND skills.owner_id = auth.uid()
    )
  );

-- Policy to allow skill owners to update available dates
CREATE POLICY "Skill owners can update available dates" 
  ON skill_available_dates 
  FOR UPDATE 
  USING (
    EXISTS (
      SELECT 1 FROM skills 
      WHERE skills.id = skill_available_dates.skill_id 
      AND skills.owner_id = auth.uid()
    )
  );

-- Policy to allow skill owners to delete available dates
CREATE POLICY "Skill owners can delete available dates" 
  ON skill_available_dates 
  FOR DELETE 
  USING (
    EXISTS (
      SELECT 1 FROM skills 
      WHERE skills.id = skill_available_dates.skill_id 
      AND skills.owner_id = auth.uid()
    )
  );

-- Create index for faster lookups
CREATE INDEX idx_skill_available_dates_skill_id ON skill_available_dates(skill_id);
CREATE INDEX idx_skill_available_dates_date_time ON skill_available_dates(date_time);
