'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Pencil, Plus, X } from 'lucide-react';

interface LearningGoalsProps {
  learningGoals: string[] | null;
  isEditable?: boolean;
  onSave?: (goals: string[]) => Promise<void>;
}

// Added animation wrapper
const AnimatedComponent = ({ children }: { children: React.ReactNode }) => (
  <div className="animate-fadeIn">
    {children}
  </div>
);

export default function LearningGoals({
  learningGoals,
  isEditable = false,
  onSave,
}: LearningGoalsProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [goals, setGoals] = useState<string[]>(learningGoals || []);
  const [newGoal, setNewGoal] = useState('');
  const [isSaving, setIsSaving] = useState(false);

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = async () => {
    if (!onSave) return;

    try {
      setIsSaving(true);
      await onSave(goals);
      setIsEditing(false);
    } catch (error) {
      console.error('Error saving learning goals:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setGoals(learningGoals || []);
    setNewGoal('');
    setIsEditing(false);
  };

  const handleAddGoal = () => {
    if (newGoal.trim()) {
      setGoals([...goals, newGoal.trim()]);
      setNewGoal('');
    }
  };

  const handleRemoveGoal = (index: number) => {
    setGoals(goals.filter((_, i) => i !== index));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddGoal();
    }
  };

  return (
    <AnimatedComponent>
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm animate-fadeIn transition-colors duration-200">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Learning Goals</h3>
          {isEditable && !isEditing && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleEdit}
              className="text-gray-500 hover:text-gray-700 transition duration-200 dark:text-gray-400 dark:hover:text-gray-300">
              <Pencil className="h-4 w-4 mr-2" />
              Edit
            </Button>
          )}
        </div>

        {isEditing ? (
          <div className="space-y-4">
            <div className="flex space-x-2">
              <input
                type="text"
                value={newGoal}
                onChange={(e) => setNewGoal(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Add a skill you want to learn..."
                className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
              <Button onClick={handleAddGoal} disabled={!newGoal.trim()}>
                <Plus className="h-4 w-4 mr-2" />
                Add
              </Button>
            </div>

            <div className="space-y-2">
              {goals.length > 0 ? (
                goals.map((goal, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between bg-gray-100 dark:bg-gray-700 p-6 rounded-xl"
                  >
                    <span className="text-gray-800 dark:text-gray-200">{goal}</span>
                    <button
                      onClick={() => handleRemoveGoal(index)}
                      className="text-gray-500 hover:text-red-500 transition duration-200 dark:text-gray-400 dark:hover:text-red-400"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                ))
              ) : (
                <p className="text-gray-500 dark:text-gray-400 italic">
                  No learning goals added yet. Add skills you want to learn.
                </p>
              )}
            </div>

            <div className="flex space-x-2">
              <Button onClick={handleSave} disabled={isSaving}>
                {isSaving ? 'Saving...' : 'Save'}
              </Button>
              <Button variant="outline" onClick={handleCancel} disabled={isSaving} className="transition duration-200">
                Cancel
              </Button>
            </div>
          </div>
        ) : (
          <div>
            {goals.length > 0 ? (
              <ul className="space-y-2">
                {goals.map((goal, index) => (
                  <li key={index} className="flex items-center">
                    <span className="text-blue-500 mr-2">•</span>
                    <span className="text-gray-700 dark:text-gray-300">{goal}</span>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-gray-500 dark:text-gray-400 italic">
                No learning goals provided yet.
                {isEditable && ' Click Edit to add skills you want to learn.'}
              </p>
            )}
          </div>
        )}
      </div>
    </AnimatedComponent>
  );
}
