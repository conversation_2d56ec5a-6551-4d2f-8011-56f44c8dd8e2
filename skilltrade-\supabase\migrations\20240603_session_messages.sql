-- Create session_messages table if it doesn't exist
CREATE TABLE IF NOT EXISTS session_messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  session_id UUID NOT NULL REFERENCES sessions(id) ON DELETE CASCADE,
  sender_id UUID NOT NULL REFERENCES profiles(id),
  content TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS session_messages_session_id_idx ON session_messages(session_id);

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_session_messages_updated_at ON session_messages;
CREATE TRIGGER update_session_messages_updated_at
BEFORE UPDATE ON session_messages
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Create notification function for new messages
CREATE OR REPLACE FUNCTION create_message_notification()
RETURNS TRIGGER AS $$
DECLARE
  session_record RECORD;
  recipient_id UUID;
  session_title TEXT;
BEGIN
  -- Get session details
  SELECT s.*, sk.title as skill_title
  INTO session_record
  FROM sessions s
  JOIN skills sk ON s.skill_id = sk.id
  WHERE s.id = NEW.session_id;

  -- Determine recipient (the other party)
  IF NEW.sender_id = session_record.teacher_id THEN
    recipient_id := session_record.learner_id;
  ELSE
    recipient_id := session_record.teacher_id;
  END IF;

  -- Call the create_notification function (which will be created in another migration)
  -- This function handles all the column existence checks
  PERFORM create_notification(
    recipient_id,
    'message',
    'New message in session',
    'You have received a new message in your session for ' || session_record.skill_title,
    '/dashboard/sessions/' || NEW.session_id
  );

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for message notifications
DROP TRIGGER IF EXISTS on_new_message ON session_messages;
CREATE TRIGGER on_new_message
AFTER INSERT ON session_messages
FOR EACH ROW
EXECUTE FUNCTION create_message_notification();
