-- Add RLS policies for session_messages table
-- This migration adds the necessary Row Level Security policies for the session_messages table

-- First, make sure RLS is enabled
ALTER TABLE session_messages ENABLE ROW LEVEL SECURITY;

-- Drop any existing policies to avoid conflicts
DROP POLICY IF EXISTS "Session messages are viewable by participants" ON session_messages;
DROP POLICY IF EXISTS "Users can insert their own session messages" ON session_messages;

-- Create policy for viewing messages (only session participants can view)
CREATE POLICY "Session messages are viewable by participants" 
ON session_messages
FOR SELECT 
USING (
  EXISTS (
    SELECT 1 FROM sessions 
    WHERE sessions.id = session_messages.session_id 
    AND (auth.uid() = sessions.teacher_id OR auth.uid() = sessions.learner_id)
  )
);

-- Create policy for inserting messages (only session participants can insert)
CREATE POLICY "Users can insert their own session messages" 
ON session_messages
FOR INSERT 
WITH CHECK (
  auth.uid() = sender_id AND
  EXISTS (
    SELECT 1 FROM sessions 
    WHERE sessions.id = session_messages.session_id 
    AND (auth.uid() = sessions.teacher_id OR auth.uid() = sessions.learner_id)
  )
);

-- Create policy for service role to bypass RLS
CREATE POLICY "Service role can manage all session messages" 
ON session_messages
USING (auth.role() = 'service_role');
