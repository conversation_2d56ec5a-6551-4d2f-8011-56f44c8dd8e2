// Test script for profile features
// Run with: node scripts/test-profile-features.js

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.test' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Error: Supabase URL or service role key not found in environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Test user ID - replace with a real user ID from your database
const TEST_USER_ID = 'test-user-id';

// Test data
const testData = {
  teachingStyle: 'I focus on hands-on learning with practical examples. My teaching style is interactive and adaptive to each student\'s needs. I believe in creating a supportive environment where mistakes are seen as learning opportunities.',

  learningGoals: [
    'JavaScript Advanced Concepts',
    'React Native Development',
    'Cloud Architecture',
    'Machine Learning Basics'
  ],

  skillLevels: [
    {
      taxonomyId: '1', // Replace with actual taxonomy ID
      proficiencyLevel: 5,
      description: 'Over 8 years of professional experience with React.js'
    },
    {
      taxonomyId: '2', // Replace with actual taxonomy ID
      proficiencyLevel: 4,
      description: 'Developed multiple Node.js applications with Express'
    },
    {
      taxonomyId: '3', // Replace with actual taxonomy ID
      proficiencyLevel: 3,
      description: 'Basic understanding of Python for data analysis'
    }
  ],

  portfolioItems: [
    {
      title: 'E-commerce Platform',
      description: 'A full-stack e-commerce solution built with Next.js and Supabase',
      imageUrl: 'https://example.com/portfolio1.jpg',
      linkUrl: 'https://github.com/example/ecommerce'
    },
    {
      title: 'Weather App',
      description: 'A mobile weather application built with React Native',
      imageUrl: 'https://example.com/portfolio2.jpg',
      linkUrl: 'https://github.com/example/weather-app'
    }
  ],

  availabilitySlots: [
    {
      dayOfWeek: 1, // Monday
      startTime: '09:00',
      endTime: '12:00',
      isRecurring: true
    },
    {
      dayOfWeek: 3, // Wednesday
      startTime: '14:00',
      endTime: '18:00',
      isRecurring: true
    },
    {
      dayOfWeek: 5, // Friday
      startTime: '10:00',
      endTime: '15:00',
      isRecurring: true
    }
  ]
};

// Test functions
async function testTeachingStyle() {
  console.log('\n--- Testing Teaching Style ---');

  try {
    // Update teaching style
    const { error } = await supabase
      .from('profiles')
      .update({
        teaching_style: testData.teachingStyle,
        updated_at: new Date().toISOString()
      })
      .eq('id', TEST_USER_ID);

    if (error) throw error;

    // Verify teaching style was updated
    const { data, error: fetchError } = await supabase
      .from('profiles')
      .select('teaching_style')
      .eq('id', TEST_USER_ID)
      .single();

    if (fetchError) throw fetchError;

    if (data.teaching_style === testData.teachingStyle) {
      console.log('✅ Teaching style updated successfully');
    } else {
      console.log('❌ Teaching style update failed');
      console.log('Expected:', testData.teachingStyle);
      console.log('Actual:', data.teaching_style);
    }
  } catch (error) {
    console.error('Error testing teaching style:', error.message);
  }
}

async function testLearningGoals() {
  console.log('\n--- Testing Learning Goals ---');

  try {
    // Update learning goals
    const { error } = await supabase
      .from('profiles')
      .update({
        learning_goals: testData.learningGoals,
        updated_at: new Date().toISOString()
      })
      .eq('id', TEST_USER_ID);

    if (error) throw error;

    // Verify learning goals were updated
    const { data, error: fetchError } = await supabase
      .from('profiles')
      .select('learning_goals')
      .eq('id', TEST_USER_ID)
      .single();

    if (fetchError) throw fetchError;

    if (JSON.stringify(data.learning_goals) === JSON.stringify(testData.learningGoals)) {
      console.log('✅ Learning goals updated successfully');
    } else {
      console.log('❌ Learning goals update failed');
      console.log('Expected:', testData.learningGoals);
      console.log('Actual:', data.learning_goals);
    }
  } catch (error) {
    console.error('Error testing learning goals:', error.message);
  }
}

async function testSkillLevels() {
  console.log('\n--- Testing Skill Levels ---');

  try {
    // First, delete existing skill levels
    const { error: deleteError } = await supabase
      .from('user_skill_levels')
      .delete()
      .eq('user_id', TEST_USER_ID);

    if (deleteError) throw deleteError;

    // Insert new skill levels
    const skillLevelsToInsert = testData.skillLevels.map(level => ({
      user_id: TEST_USER_ID,
      taxonomy_id: level.taxonomyId,
      proficiency_level: level.proficiencyLevel,
      description: level.description
    }));

    const { error: insertError } = await supabase
      .from('user_skill_levels')
      .insert(skillLevelsToInsert);

    if (insertError) throw insertError;

    // Verify skill levels were inserted
    const { data, error: fetchError } = await supabase
      .from('user_skill_levels')
      .select('*')
      .eq('user_id', TEST_USER_ID);

    if (fetchError) throw fetchError;

    if (data.length === testData.skillLevels.length) {
      console.log(`✅ ${data.length} skill levels inserted successfully`);
    } else {
      console.log('❌ Skill levels insertion failed');
      console.log('Expected count:', testData.skillLevels.length);
      console.log('Actual count:', data.length);
    }
  } catch (error) {
    console.error('Error testing skill levels:', error.message);
  }
}

async function testPortfolioItems() {
  console.log('\n--- Testing Portfolio Items ---');

  try {
    // First, delete existing portfolio items
    const { error: deleteError } = await supabase
      .from('portfolio_items')
      .delete()
      .eq('user_id', TEST_USER_ID);

    if (deleteError) throw deleteError;

    // Insert new portfolio items
    const portfolioItemsToInsert = testData.portfolioItems.map(item => ({
      user_id: TEST_USER_ID,
      title: item.title,
      description: item.description,
      image_url: item.imageUrl,
      link_url: item.linkUrl
    }));

    const { error: insertError } = await supabase
      .from('portfolio_items')
      .insert(portfolioItemsToInsert);

    if (insertError) throw insertError;

    // Verify portfolio items were inserted
    const { data, error: fetchError } = await supabase
      .from('portfolio_items')
      .select('*')
      .eq('user_id', TEST_USER_ID);

    if (fetchError) throw fetchError;

    if (data.length === testData.portfolioItems.length) {
      console.log(`✅ ${data.length} portfolio items inserted successfully`);
    } else {
      console.log('❌ Portfolio items insertion failed');
      console.log('Expected count:', testData.portfolioItems.length);
      console.log('Actual count:', data.length);
    }
  } catch (error) {
    console.error('Error testing portfolio items:', error.message);
  }
}

async function testAvailabilitySlots() {
  console.log('\n--- Testing Availability Slots ---');

  try {
    // First, delete existing availability slots
    const { error: deleteError } = await supabase
      .from('user_availability')
      .delete()
      .eq('user_id', TEST_USER_ID);

    if (deleteError) throw deleteError;

    // Insert new availability slots
    const availabilitySlotsToInsert = testData.availabilitySlots.map(slot => ({
      user_id: TEST_USER_ID,
      day_of_week: slot.dayOfWeek,
      start_time: slot.startTime,
      end_time: slot.endTime,
      is_recurring: slot.isRecurring
    }));

    const { error: insertError } = await supabase
      .from('user_availability')
      .insert(availabilitySlotsToInsert);

    if (insertError) throw insertError;

    // Verify availability slots were inserted
    const { data, error: fetchError } = await supabase
      .from('user_availability')
      .select('*')
      .eq('user_id', TEST_USER_ID);

    if (fetchError) throw fetchError;

    if (data.length === testData.availabilitySlots.length) {
      console.log(`✅ ${data.length} availability slots inserted successfully`);
    } else {
      console.log('❌ Availability slots insertion failed');
      console.log('Expected count:', testData.availabilitySlots.length);
      console.log('Actual count:', data.length);
    }
  } catch (error) {
    console.error('Error testing availability slots:', error.message);
  }
}

// Run all tests
async function runAllTests() {
  console.log('Starting profile features tests...');
  console.log('Test user ID:', TEST_USER_ID);

  await testTeachingStyle();
  await testLearningGoals();
  await testSkillLevels();
  await testPortfolioItems();
  await testAvailabilitySlots();

  console.log('\nAll tests completed!');
}

runAllTests();
