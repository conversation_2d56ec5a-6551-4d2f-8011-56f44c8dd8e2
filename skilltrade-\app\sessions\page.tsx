'use client';

import { useState, useEffect } from 'react';
import { createClientSide } from '@/lib/supabase';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import MainHeader from '@/components/MainHeader';
import MainFooter from '@/components/MainFooter';

interface Session {
  id: string;
  skill_id: string;
  teacher_id: string;
  learner_id: string;
  scheduled_at: string;
  duration_hours: number;
  status: string;
  created_at: string;
  skill: {
    title: string;
  };
  teacher: {
    display_name: string | null;
    avatar_url: string | null;
    email?: string;
  };
  learner: {
    display_name: string | null;
    avatar_url: string | null;
    email?: string;
  };
}

export default function SessionsPage() {
  const [sessions, setSessions] = useState<Session[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<'all' | 'teaching' | 'learning'>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const router = useRouter();
  const supabase = createClientSide();

  useEffect(() => {
    const fetchSessions = async () => {
      try {
        setLoading(true);

        // Get the current user
        const { data: { user } } = await supabase.auth.getUser();

        if (!user) {
          router.push('/login');
          return;
        }

        // Get all sessions where the user is either teacher or learner
        const { data, error } = await supabase
          .from('sessions')
          .select(`
            *,
            skill:skills(title),
            teacher:profiles!sessions_teacher_id_fkey(display_name, avatar_url, email),
            learner:profiles!sessions_learner_id_fkey(display_name, avatar_url, email)
          `)
          .or(`teacher_id.eq.${user.id},learner_id.eq.${user.id}`)
          .order('scheduled_at', { ascending: false });

        if (error) {
          throw error;
        }

        setSessions(data || []);
      } catch (error: any) {
        setError(error.message || 'Failed to load sessions');
      } finally {
        setLoading(false);
      }
    };

    fetchSessions();
  }, [router, supabase]);

  // Get the current user from the state
  const [currentUser, setCurrentUser] = useState<any>(null);

  // Fetch the current user when the component mounts
  useEffect(() => {
    const fetchCurrentUser = async () => {
      const { data } = await supabase.auth.getUser();
      setCurrentUser(data.user);
    };

    fetchCurrentUser();
  }, [supabase]);

  // Filter sessions based on selected filters
  const filteredSessions = sessions.filter(session => {
    // Filter by role (teaching/learning)
    const isTeaching = session.teacher_id === currentUser?.id;

    if (filter === 'teaching' && !isTeaching) return false;
    if (filter === 'learning' && isTeaching) return false;

    // Filter by status
    if (statusFilter !== 'all' && session.status !== statusFilter) return false;

    return true;
  });

  const updateSessionStatus = async (sessionId: string, newStatus: string) => {
    try {
      const { error } = await supabase
        .from('sessions')
        .update({ status: newStatus })
        .eq('id', sessionId);

      if (error) {
        throw error;
      }

      // Update the local state
      setSessions(sessions.map(session =>
        session.id === sessionId
          ? { ...session, status: newStatus }
          : session
      ));
    } catch (error: any) {
      setError(error.message || 'Failed to update session status');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <MainHeader />
        <div className="container mx-auto px-4 py-8">
          <div className="flex justify-center items-center h-64">
            <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
            <p className="ml-4 text-gray-700 dark:text-gray-300">Loading sessions...</p>
          </div>
        </div>
        <MainFooter />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <MainHeader />

      <main className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">My Sessions</h1>
              <p className="text-gray-600 dark:text-gray-400 mt-2">
                Manage your teaching and learning sessions
              </p>
            </div>
          </div>

          {error && (
            <div className="bg-red-100 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 px-4 py-3 rounded-lg mb-6">
              {error}
            </div>
          )}

          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 mb-8 shadow-md">
            <div className="flex flex-col md:flex-row gap-4">
              <div>
                <label htmlFor="role-filter" className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                  Filter by Role
                </label>
                <select
                  id="role-filter"
                  value={filter}
                  onChange={(e) => setFilter(e.target.value as 'all' | 'teaching' | 'learning')}
                  className="px-4 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Sessions</option>
                  <option value="teaching">Teaching</option>
                  <option value="learning">Learning</option>
                </select>
              </div>

              <div>
                <label htmlFor="status-filter" className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                  Filter by Status
                </label>
                <select
                  id="status-filter"
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-4 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Statuses</option>
                  <option value="pending">Pending</option>
                  <option value="accepted">Accepted</option>
                  <option value="completed">Completed</option>
                  <option value="cancelled">Cancelled</option>
                  <option value="reviewed">Reviewed</option>
                </select>
              </div>
            </div>
          </div>

          {filteredSessions.length === 0 ? (
            <div className="bg-white dark:bg-gray-800 rounded-xl p-8 text-center shadow-md">
              <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">No sessions found</h2>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                {filter !== 'all' || statusFilter !== 'all'
                  ? 'No sessions match your filter criteria. Try adjusting your filters.'
                  : 'You don\'t have any sessions yet. Book a session or wait for someone to book with you.'}
              </p>
              {(filter !== 'all' || statusFilter !== 'all') && (
                <button
                  onClick={() => {
                    setFilter('all');
                    setStatusFilter('all');
                  }}
                  className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition inline-block"
                >
                  Clear Filters
                </button>
              )}
            </div>
          ) : (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="bg-gray-100 dark:bg-gray-700">
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 dark:text-gray-300 uppercase tracking-wider">
                        Skill
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 dark:text-gray-300 uppercase tracking-wider">
                        Role
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 dark:text-gray-300 uppercase tracking-wider">
                        With
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 dark:text-gray-300 uppercase tracking-wider">
                        Date & Time
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 dark:text-gray-300 uppercase tracking-wider">
                        Duration
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 dark:text-gray-300 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 dark:text-gray-300 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                    {filteredSessions.map((session) => {
                      const isTeacher = session.teacher_id === currentUser?.id;
                      const otherPerson = isTeacher ? session.learner : session.teacher;

                      return (
                        <tr key={session.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              {session.skill?.title || 'Unknown Skill'}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-700 dark:text-gray-300">
                              {isTeacher ? 'Teacher' : 'Learner'}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-700 dark:text-gray-300">
                              {otherPerson?.display_name || otherPerson?.email?.split('@')[0] || 'Unknown User'}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-700 dark:text-gray-300">
                              {formatDateTime(session.scheduled_at)}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-700 dark:text-gray-300">
                              {session.duration_hours} {session.duration_hours === 1 ? 'hour' : 'hours'}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(session.status)}`}>
                              {session.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex space-x-3">
                              <Link
                                href={`/sessions/${session.id}`}
                                className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium"
                              >
                                View
                              </Link>

                              {/* Status update actions */}
                              {session.status === 'pending' && isTeacher && (
                                <>
                                  <button
                                    onClick={() => updateSessionStatus(session.id, 'accepted')}
                                    className="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 text-sm font-medium"
                                  >
                                    Accept
                                  </button>
                                  <button
                                    onClick={() => updateSessionStatus(session.id, 'cancelled')}
                                    className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 text-sm font-medium"
                                  >
                                    Decline
                                  </button>
                                </>
                              )}

                              {session.status === 'accepted' && (
                                <button
                                  onClick={() => updateSessionStatus(session.id, 'completed')}
                                  className="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 text-sm font-medium"
                                >
                                  Mark Completed
                                </button>
                              )}

                              {session.status === 'completed' && !isTeacher && (
                                <Link
                                  href={`/sessions/${session.id}/review`}
                                  className="text-yellow-600 dark:text-yellow-400 hover:text-yellow-800 dark:hover:text-yellow-300 text-sm font-medium"
                                >
                                  Leave Review
                                </Link>
                              )}
                            </div>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      </main>

      <MainFooter />
    </div>
  );
}

// Helper function to format date and time
function formatDateTime(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString() + ' at ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
}

// Helper function to get status color
function getStatusColor(status: string): string {
  switch (status.toLowerCase()) {
    case 'pending':
      return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300';
    case 'accepted':
      return 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300';
    case 'completed':
      return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300';
    case 'cancelled':
      return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300';
    case 'reviewed':
      return 'bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300';
    default:
      return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300';
  }
}
