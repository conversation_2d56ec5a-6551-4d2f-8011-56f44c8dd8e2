-- Fix the apply_credits_on_review function to use 'message' instead of 'content'
CREATE OR REPLACE FUNCTION apply_credits_on_review() RETURNS TRIGGER AS $$
DECLARE
  has_message_column BOOLEAN;
  has_content_column BOOLEAN;
BEGIN
  -- Check which columns exist in the notifications table
  SELECT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'notifications'
    AND column_name = 'message'
  ) INTO has_message_column;

  SELECT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'notifications'
    AND column_name = 'content'
  ) INTO has_content_column;

  -- If rating is 3 or higher, consider it positive
  IF NEW.rating >= 3 THEN
    UPDATE sessions SET status = 'reviewed' WHERE id = NEW.session_id;

    -- Credit teacher
    INSERT INTO ledger(user_id, session_id, hours_delta, reason)
    SELECT teacher_id, NEW.session_id, s.duration_hours, 'teach'
    FROM sessions s WHERE s.id = NEW.session_id;

    -- Debit learner
    INSERT INTO ledger(user_id, session_id, hours_delta, reason)
    SELECT learner_id, NEW.session_id, -s.duration_hours, 'learn'
    FROM sessions s WHERE s.id = NEW.session_id;

    -- Update balances
    UPDATE profiles p
    SET credit_balance = p.credit_balance + l.hours_delta
    FROM ledger l
    WHERE l.user_id = p.id AND l.session_id = NEW.session_id;
  ELSE
    -- If rating is below 3, mark as disputed
    UPDATE sessions SET status = 'disputed' WHERE id = NEW.session_id;

    -- Create notifications for both parties using the correct column name
    IF has_message_column THEN
      -- Use 'message' column if it exists
      INSERT INTO notifications (
        user_id,
        type,
        title,
        message,
        related_id
      )
      SELECT
        teacher_id,
        'dispute',
        'Session disputed',
        'A session has been marked as disputed. Please communicate with the other participant to resolve the issue.',
        NEW.session_id
      FROM sessions WHERE id = NEW.session_id;

      INSERT INTO notifications (
        user_id,
        type,
        title,
        message,
        related_id
      )
      SELECT
        learner_id,
        'dispute',
        'Session disputed',
        'A session has been marked as disputed. Please communicate with the other participant to resolve the issue.',
        NEW.session_id
      FROM sessions WHERE id = NEW.session_id;
    ELSIF has_content_column THEN
      -- Use 'content' column if it exists
      INSERT INTO notifications (
        user_id,
        type,
        title,
        content,
        related_id
      )
      SELECT
        teacher_id,
        'dispute',
        'Session disputed',
        'A session has been marked as disputed. Please communicate with the other participant to resolve the issue.',
        NEW.session_id
      FROM sessions WHERE id = NEW.session_id;

      INSERT INTO notifications (
        user_id,
        type,
        title,
        content,
        related_id
      )
      SELECT
        learner_id,
        'dispute',
        'Session disputed',
        'A session has been marked as disputed. Please communicate with the other participant to resolve the issue.',
        NEW.session_id
      FROM sessions WHERE id = NEW.session_id;
    END IF;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Fix the resolve_dispute function to use 'message' instead of 'content'
CREATE OR REPLACE FUNCTION resolve_dispute(
  p_session_id UUID,
  p_resolution TEXT,
  p_user_id UUID,
  p_agreement_message TEXT
) RETURNS VOID AS $$
DECLARE
  v_session RECORD;
  v_other_user_id UUID;
  has_message_column BOOLEAN;
  has_content_column BOOLEAN;
BEGIN
  -- Check which columns exist in the notifications table
  SELECT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'notifications'
    AND column_name = 'message'
  ) INTO has_message_column;

  SELECT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'notifications'
    AND column_name = 'content'
  ) INTO has_content_column;

  -- Get the session
  SELECT * INTO v_session FROM sessions WHERE id = p_session_id;

  -- Check if the session is disputed
  IF v_session.status != 'disputed' THEN
    RAISE EXCEPTION 'Session is not disputed';
  END IF;

  -- Check if the user is a participant
  IF v_session.teacher_id = p_user_id THEN
    v_other_user_id := v_session.learner_id;
  ELSIF v_session.learner_id = p_user_id THEN
    v_other_user_id := v_session.teacher_id;
  ELSE
    RAISE EXCEPTION 'User is not a participant in this session';
  END IF;

  -- Add a message about the resolution
  INSERT INTO messages (
    session_id,
    sender_id,
    content
  ) VALUES (
    p_session_id,
    p_user_id,
    'DISPUTE RESOLUTION: ' || p_agreement_message
  );

  -- Update the session status
  UPDATE sessions
  SET
    status = p_resolution,
    notes = COALESCE(notes, '') || E'\n\nDispute resolved by participants: ' || p_agreement_message
  WHERE id = p_session_id;

  -- Create notification for the other participant using the correct column
  IF has_message_column THEN
    -- Use 'message' column if it exists
    INSERT INTO notifications (
      user_id,
      type,
      title,
      message,
      related_id
    ) VALUES (
      v_other_user_id,
      'dispute_resolved',
      'Dispute resolved',
      'A dispute has been resolved for one of your sessions. Resolution: ' || p_resolution,
      p_session_id
    );
  ELSIF has_content_column THEN
    -- Use 'content' column if it exists
    INSERT INTO notifications (
      user_id,
      type,
      title,
      content,
      related_id
    ) VALUES (
      v_other_user_id,
      'dispute_resolved',
      'Dispute resolved',
      'A dispute has been resolved for one of your sessions. Resolution: ' || p_resolution,
      p_session_id
    );
  END IF;

  -- If resolution is 'completed', apply credits
  IF p_resolution = 'completed' THEN
    -- Credit teacher
    INSERT INTO ledger(user_id, session_id, hours_delta, reason)
    VALUES (v_session.teacher_id, p_session_id, v_session.duration_hours, 'teach (dispute resolved)');

    -- Debit learner
    INSERT INTO ledger(user_id, session_id, hours_delta, reason)
    VALUES (v_session.learner_id, p_session_id, -v_session.duration_hours, 'learn (dispute resolved)');

    -- Update balances
    UPDATE profiles
    SET credit_balance = credit_balance + v_session.duration_hours
    WHERE id = v_session.teacher_id;

    UPDATE profiles
    SET credit_balance = credit_balance - v_session.duration_hours
    WHERE id = v_session.learner_id;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Fix the mark_session_complete function to use 'message' instead of 'content'
CREATE OR REPLACE FUNCTION mark_session_complete(
  p_session_id UUID,
  p_user_id UUID
) RETURNS VOID AS $$
DECLARE
  v_session RECORD;
  v_now TIMESTAMPTZ := now();
  has_message_column BOOLEAN;
  has_content_column BOOLEAN;
BEGIN
  -- Check which columns exist in the notifications table
  SELECT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'notifications'
    AND column_name = 'message'
  ) INTO has_message_column;

  SELECT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'notifications'
    AND column_name = 'content'
  ) INTO has_content_column;

  -- Get the session
  SELECT * INTO v_session FROM sessions WHERE id = p_session_id;

  -- Check if the user is a participant
  IF v_session.teacher_id = p_user_id THEN
    -- Teacher marking complete
    UPDATE sessions
    SET
      teacher_marked_complete = TRUE,
      teacher_marked_at = v_now
    WHERE id = p_session_id;

    -- Create notification for learner using the correct column
    IF has_message_column THEN
      -- Use 'message' column if it exists
      INSERT INTO notifications (
        user_id,
        type,
        title,
        message,
        related_id
      ) VALUES (
        v_session.learner_id,
        'session_update',
        'Session marked as complete',
        'The teacher has marked your session as complete. Please review and confirm completion.',
        p_session_id
      );
    ELSIF has_content_column THEN
      -- Use 'content' column if it exists
      INSERT INTO notifications (
        user_id,
        type,
        title,
        content,
        related_id
      ) VALUES (
        v_session.learner_id,
        'session_update',
        'Session marked as complete',
        'The teacher has marked your session as complete. Please review and confirm completion.',
        p_session_id
      );
    END IF;

  ELSIF v_session.learner_id = p_user_id THEN
    -- Learner marking complete
    UPDATE sessions
    SET
      learner_marked_complete = TRUE,
      learner_marked_at = v_now
    WHERE id = p_session_id;

    -- Create notification for teacher using the correct column
    IF has_message_column THEN
      -- Use 'message' column if it exists
      INSERT INTO notifications (
        user_id,
        type,
        title,
        message,
        related_id
      ) VALUES (
        v_session.teacher_id,
        'session_update',
        'Session marked as complete',
        'The learner has marked your session as complete. Please review and confirm completion.',
        p_session_id
      );
    ELSIF has_content_column THEN
      -- Use 'content' column if it exists
      INSERT INTO notifications (
        user_id,
        type,
        title,
        content,
        related_id
      ) VALUES (
        v_session.teacher_id,
        'session_update',
        'Session marked as complete',
        'The learner has marked your session as complete. Please review and confirm completion.',
        p_session_id
      );
    END IF;
  ELSE
    -- User is not a participant
    RAISE EXCEPTION 'User is not a participant in this session';
  END IF;

  -- Check if both have marked complete
  IF (
    SELECT teacher_marked_complete AND learner_marked_complete
    FROM sessions
    WHERE id = p_session_id
  ) THEN
    -- Both have marked complete, update status
    UPDATE sessions SET status = 'completed' WHERE id = p_session_id;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Fix the auto_complete_sessions function to use 'message' instead of 'content'
CREATE OR REPLACE FUNCTION auto_complete_sessions() RETURNS VOID AS $$
DECLARE
  v_session RECORD;
  has_message_column BOOLEAN;
  has_content_column BOOLEAN;
BEGIN
  -- Check which columns exist in the notifications table
  SELECT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'notifications'
    AND column_name = 'message'
  ) INTO has_message_column;

  SELECT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'notifications'
    AND column_name = 'content'
  ) INTO has_content_column;

  -- Find sessions where one party marked complete more than 24 hours ago
  FOR v_session IN
    SELECT * FROM sessions
    WHERE
      (
        (teacher_marked_complete AND NOT learner_marked_complete AND teacher_marked_at < now() - INTERVAL '24 hours')
        OR
        (learner_marked_complete AND NOT teacher_marked_complete AND learner_marked_at < now() - INTERVAL '24 hours')
      )
      AND status = 'accepted'
  LOOP
    -- Auto-complete the session
    UPDATE sessions
    SET
      status = 'completed',
      teacher_marked_complete = TRUE,
      learner_marked_complete = TRUE
    WHERE id = v_session.id;

    -- Create notifications for both parties using the correct column
    IF has_message_column THEN
      -- Use 'message' column if it exists
      INSERT INTO notifications (
        user_id,
        type,
        title,
        message,
        related_id
      ) VALUES
      (
        v_session.teacher_id,
        'session_update',
        'Session auto-completed',
        'A session has been automatically marked as complete after 24 hours.',
        v_session.id
      ),
      (
        v_session.learner_id,
        'session_update',
        'Session auto-completed',
        'A session has been automatically marked as complete after 24 hours.',
        v_session.id
      );
    ELSIF has_content_column THEN
      -- Use 'content' column if it exists
      INSERT INTO notifications (
        user_id,
        type,
        title,
        content,
        related_id
      ) VALUES
      (
        v_session.teacher_id,
        'session_update',
        'Session auto-completed',
        'A session has been automatically marked as complete after 24 hours.',
        v_session.id
      ),
      (
        v_session.learner_id,
        'session_update',
        'Session auto-completed',
        'A session has been automatically marked as complete after 24 hours.',
        v_session.id
      );
    END IF;
  END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;