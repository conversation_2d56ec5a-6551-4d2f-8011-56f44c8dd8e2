import { NextRequest, NextResponse } from 'next/server';
import { createServerSide } from '@/lib/supabase-server';
import { createAdminClient } from '@/lib/supabase-admin';

// POST /api/conversations/[id]/read - Mark a conversation as read
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createServerSide();

    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const conversationId = params.id;
    console.log('Marking conversation as read:', conversationId);
    console.log('User ID:', user.id);

    // Use the admin client to bypass RLS
    const adminSupabase = createAdminClient();

    // Check if the user is a participant in the conversation
    const { data: participant, error: participantError } = await adminSupabase
      .from('conversation_participants')
      .select('*')
      .eq('conversation_id', conversationId)
      .eq('user_id', user.id)
      .single();

    if (participantError) {
      console.error('Participant check error in read endpoint:', participantError);
      return NextResponse.json(
        { error: 'Conversation not found or you are not a participant' },
        { status: 404 }
      );
    }

    // Update the last_read_at timestamp
    const now = new Date().toISOString();
    console.log('Updating last_read_at to:', now);

    const { data: updatedParticipant, error: updateError } = await adminSupabase
      .from('conversation_participants')
      .update({
        last_read_at: now
      })
      .eq('conversation_id', conversationId)
      .eq('user_id', user.id)
      .select();

    if (updateError) {
      console.error('Error updating last_read_at:', updateError);
      throw updateError;
    }

    console.log('Successfully marked conversation as read:', updatedParticipant);

    return NextResponse.json({
      success: true,
      participant: updatedParticipant
    });
  } catch (error: any) {
    console.error('Error marking conversation as read:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to mark conversation as read' },
      { status: 500 }
    );
  }
}
