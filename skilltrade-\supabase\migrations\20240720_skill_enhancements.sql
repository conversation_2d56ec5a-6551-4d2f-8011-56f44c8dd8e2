-- Migration to add image_url and difficulty_level to skills table
-- This migration adds support for skill images and difficulty levels

-- Add image_url column to skills table
ALTER TABLE skills ADD COLUMN IF NOT EXISTS image_url TEXT;

-- Add difficulty_level column to skills table
-- Using an enum type for better data integrity
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'skill_difficulty_level') THEN
        CREATE TYPE skill_difficulty_level AS ENUM ('beginner', 'intermediate', 'advanced');
    END IF;
END$$;

ALTER TABLE skills ADD COLUMN IF NOT EXISTS difficulty_level skill_difficulty_level DEFAULT 'intermediate';

-- Create a bucket for skill images if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM storage.buckets WHERE name = 'skill-images') THEN
        INSERT INTO storage.buckets (id, name, public)
        VALUES ('skill-images', 'skill-images', true);
    END IF;
END $$;

-- Add storage policies for skill images bucket

-- Policy for viewing skill images (public access)
INSERT INTO storage.policies (name, bucket_id, operation, definition)
SELECT 'Skill images are viewable by everyone',
       id,
       'SELECT',
       'true'
FROM storage.buckets
WHERE name = 'skill-images'
AND NOT EXISTS (
    SELECT 1 FROM storage.policies 
    WHERE bucket_id = (SELECT id FROM storage.buckets WHERE name = 'skill-images')
    AND operation = 'SELECT'
    AND name = 'Skill images are viewable by everyone'
);

-- Policy for uploading skill images (authenticated users only)
INSERT INTO storage.policies (name, bucket_id, operation, definition)
SELECT 'Users can upload skill images',
       id,
       'INSERT',
       'auth.role() = ''authenticated'''
FROM storage.buckets
WHERE name = 'skill-images'
AND NOT EXISTS (
    SELECT 1 FROM storage.policies 
    WHERE bucket_id = (SELECT id FROM storage.buckets WHERE name = 'skill-images')
    AND operation = 'INSERT'
    AND name = 'Users can upload skill images'
);

-- Policy for updating skill images (own images only)
INSERT INTO storage.policies (name, bucket_id, operation, definition)
SELECT 'Users can update their own skill images',
       id,
       'UPDATE',
       'auth.role() = ''authenticated'''
FROM storage.buckets
WHERE name = 'skill-images'
AND NOT EXISTS (
    SELECT 1 FROM storage.policies 
    WHERE bucket_id = (SELECT id FROM storage.buckets WHERE name = 'skill-images')
    AND operation = 'UPDATE'
    AND name = 'Users can update their own skill images'
);

-- Policy for deleting skill images (own images only)
INSERT INTO storage.policies (name, bucket_id, operation, definition)
SELECT 'Users can delete their own skill images',
       id,
       'DELETE',
       'auth.role() = ''authenticated'''
FROM storage.buckets
WHERE name = 'skill-images'
AND NOT EXISTS (
    SELECT 1 FROM storage.policies 
    WHERE bucket_id = (SELECT id FROM storage.buckets WHERE name = 'skill-images')
    AND operation = 'DELETE'
    AND name = 'Users can delete their own skill images'
);
