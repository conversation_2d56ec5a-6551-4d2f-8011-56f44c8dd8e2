'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback, useReducer } from 'react';
import { createClientSide } from '@/lib/supabase';
import { useAuth } from './AuthContext';
import { useRouter } from 'next/navigation';
import { useToast } from '@/hooks/use-toast';

export interface OtherUser {
  id: string;
  display_name: string | null;
  avatar_url: string | null;
}

export interface Conversation {
  id: string;
  last_message_preview: string | null;
  last_message_at: string | null;
  created_at: string;
  updated_at: string;
  status: string;
  other_user: OtherUser;
  unread_count: number;
}

export interface Message {
  id: string;
  conversation_id: string;
  sender_id: string;
  content: string;
  created_at: string;
  is_read: boolean;
  sender: {
    id: string;
    display_name: string | null;
    avatar_url: string | null;
  };
  is_optimistic?: boolean;
}

// Define state and actions for the reducer
type MessagingState = {
  conversations: Conversation[];
  activeConversation: Conversation | null;
  messages: Message[];
  loadingConversations: boolean;
  loadingMessages: boolean;
  error: string | null;
};

type MessagingAction =
  | { type: 'SET_CONVERSATIONS', payload: Conversation[] }
  | { type: 'ADD_CONVERSATION', payload: Conversation }
  | { type: 'UPDATE_CONVERSATION', payload: { id: string, changes: Partial<Conversation> } }
  | { type: 'REMOVE_CONVERSATION', payload: string }
  | { type: 'SET_ACTIVE_CONVERSATION', payload: Conversation | null }
  | { type: 'SET_MESSAGES', payload: Message[] }
  | { type: 'ADD_MESSAGE', payload: Message }
  | { type: 'UPDATE_MESSAGE', payload: { id: string, changes: Partial<Message> } }
  | { type: 'SET_LOADING_CONVERSATIONS', payload: boolean }
  | { type: 'SET_LOADING_MESSAGES', payload: boolean }
  | { type: 'SET_ERROR', payload: string | null };

// Reducer function
function messagingReducer(state: MessagingState, action: MessagingAction): MessagingState {
  switch (action.type) {
    case 'SET_CONVERSATIONS':
      return { ...state, conversations: action.payload };
    case 'ADD_CONVERSATION':
      // Only add if it doesn't already exist
      if (state.conversations.some(c => c.id === action.payload.id)) {
        return state;
      }
      return { ...state, conversations: [action.payload, ...state.conversations] };
    case 'UPDATE_CONVERSATION':
      return {
        ...state,
        conversations: state.conversations.map(conv =>
          conv.id === action.payload.id ? { ...conv, ...action.payload.changes } : conv
        ),
        // Also update activeConversation if it's the same one
        activeConversation: state.activeConversation?.id === action.payload.id
          ? { ...state.activeConversation, ...action.payload.changes }
          : state.activeConversation
      };
    case 'REMOVE_CONVERSATION':
      return {
        ...state,
        conversations: state.conversations.filter(conv => conv.id !== action.payload),
        // Clear activeConversation if it's the one being removed
        activeConversation: state.activeConversation?.id === action.payload ? null : state.activeConversation
      };
    case 'SET_ACTIVE_CONVERSATION':
      return { ...state, activeConversation: action.payload };
    case 'SET_MESSAGES':
      return { ...state, messages: action.payload };
    case 'ADD_MESSAGE':
      // Only add if it doesn't already exist
      if (state.messages.some(m => m.id === action.payload.id)) {
        return state;
      }
      return { ...state, messages: [...state.messages, action.payload] };
    case 'UPDATE_MESSAGE':
      return {
        ...state,
        messages: state.messages.map(msg =>
          msg.id === action.payload.id ? { ...msg, ...action.payload.changes } : msg
        )
      };
    case 'SET_LOADING_CONVERSATIONS':
      return { ...state, loadingConversations: action.payload };
    case 'SET_LOADING_MESSAGES':
      return { ...state, loadingMessages: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    default:
      return state;
  }
}

interface MessagingContextType {
  conversations: Conversation[];
  activeConversation: Conversation | null;
  messages: Message[];
  loadingConversations: boolean;
  loadingMessages: boolean;
  error: string | null;
  fetchConversations: () => Promise<void>;
  fetchMessages: (conversationId: string) => Promise<void>;
  sendMessage: (conversationId: string, content: string) => Promise<boolean>;
  markConversationAsRead: (conversationId: string) => Promise<boolean>;
  startConversation: (userId: string) => Promise<string | null>;
  archiveConversation: (conversationId: string) => Promise<boolean>;
  deleteConversation: (conversationId: string) => Promise<boolean>;
  setActiveConversation: (conversation: Conversation | null) => void;
}

const MessagingContext = createContext<MessagingContextType | undefined>(undefined);

export const useMessaging = () => {
  const context = useContext(MessagingContext);
  if (context === undefined) {
    throw new Error('useMessaging must be used within a MessagingProvider');
  }
  return context;
};

export const MessagingProvider = ({ children }: { children: ReactNode }) => {
  const { user } = useAuth();
  const router = useRouter();
  const { toast } = useToast();
  const supabase = createClientSide();

  // Use the reducer for state management
  const [state, dispatch] = useReducer(messagingReducer, {
    conversations: [],
    activeConversation: null,
    messages: [],
    loadingConversations: false,
    loadingMessages: false,
    error: null
  });

  // Fetch conversations with optimized caching and error handling
  // Using useCallback to memoize the function and prevent dependency issues
  const fetchConversations = useCallback(async () => {
    if (!user) {
      dispatch({ type: 'SET_CONVERSATIONS', payload: [] });
      dispatch({ type: 'SET_LOADING_CONVERSATIONS', payload: false });
      return;
    }

    try {
      // Only show loading indicator on initial load, not on updates
      if (state.conversations.length === 0) {
        dispatch({ type: 'SET_LOADING_CONVERSATIONS', payload: true });
      }
      dispatch({ type: 'SET_ERROR', payload: null });

      // Use AbortController to handle timeouts and cancellations
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      // Add cache busting to prevent browser caching
      const timestamp = new Date().getTime();
      const response = await fetch(`/api/conversations?t=${timestamp}`, {
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache',
          'Expires': '0',
        },
        signal: controller.signal
      }).finally(() => clearTimeout(timeoutId));

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch conversations');
      }

      const data = await response.json();

      // Ensure all conversations have required fields
      const processedConversations = (data.conversations || []).map((conv: any) => {
        // Ensure status is set
        if (!conv.status) {
          conv.status = 'active';
        }

        // Ensure unread_count is a number
        if (typeof conv.unread_count !== 'number') {
          conv.unread_count = 0;
        }

        return conv;
      });

      // Sort conversations by last message time (most recent first)
      const sortedConversations = [...processedConversations].sort((a, b) => {
        const timeA = a.last_message_at ? new Date(a.last_message_at).getTime() : new Date(a.created_at).getTime();
        const timeB = b.last_message_at ? new Date(b.last_message_at).getTime() : new Date(b.created_at).getTime();
        return timeB - timeA;
      });

      dispatch({ type: 'SET_CONVERSATIONS', payload: sortedConversations });
    } catch (error: any) {
      // Only set error if it's not an abort error (which is expected during cleanup)
      if (error.name !== 'AbortError') {
        dispatch({ type: 'SET_ERROR', payload: error.message || 'Failed to load conversations' });
        console.error('Error fetching conversations:', error);

        // Show error toast only for unexpected errors
        toast({
          title: 'Error',
          description: error.message || 'Failed to load conversations',
          variant: 'destructive',
        });
      }
    } finally {
      dispatch({ type: 'SET_LOADING_CONVERSATIONS', payload: false });
    }
  }, [user?.id, toast]);

  // Mark a conversation as read
  const markConversationAsRead = useCallback(async (conversationId: string): Promise<boolean> => {
    if (!user) return false;

    try {
      const response = await fetch(`/api/conversations/${conversationId}/read`, {
        method: 'POST',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to mark conversation as read');
      }

      // Update the unread count in the conversations list
      dispatch({
        type: 'UPDATE_CONVERSATION',
        payload: {
          id: conversationId,
          changes: { unread_count: 0 }
        }
      });

      return true;
    } catch (error: any) {
      console.error('Error marking conversation as read:', error);
      return false;
    }
  }, [user]);

  // Fetch messages for a conversation
  const fetchMessages = useCallback(async (conversationId: string) => {
    if (!user) {
      dispatch({ type: 'SET_MESSAGES', payload: [] });
      dispatch({ type: 'SET_LOADING_MESSAGES', payload: false });
      return;
    }

    try {
      // Only show loading indicator on initial load
      if (state.messages.length === 0) {
        dispatch({ type: 'SET_LOADING_MESSAGES', payload: true });
      }
      dispatch({ type: 'SET_ERROR', payload: null });

      // Use AbortController to handle timeouts
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      // Add cache busting to prevent browser caching
      const timestamp = new Date().getTime();
      const response = await fetch(`/api/conversations/${conversationId}?t=${timestamp}`, {
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache',
          'Expires': '0',
        },
        signal: controller.signal
      }).finally(() => clearTimeout(timeoutId));

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch messages');
      }

      const data = await response.json();
      const newMessages = data.messages || [];

      dispatch({ type: 'SET_MESSAGES', payload: newMessages });

      // Update active conversation
      const conversation = data.conversation;
      if (conversation) {
        dispatch({ type: 'SET_ACTIVE_CONVERSATION', payload: conversation });

        // Update the conversation in the conversations list
        dispatch({
          type: 'UPDATE_CONVERSATION',
          payload: {
            id: conversation.id,
            changes: { unread_count: 0 }
          }
        });
      }

      // Mark conversation as read
      await markConversationAsRead(conversationId);
    } catch (error: any) {
      // Only set error if it's not an abort error
      if (error.name !== 'AbortError') {
        dispatch({ type: 'SET_ERROR', payload: error.message || 'Failed to load messages' });
        console.error('Error fetching messages:', error);
      }
    } finally {
      dispatch({ type: 'SET_LOADING_MESSAGES', payload: false });
    }
  }, [user?.id, markConversationAsRead]);

  // Send a message
  const sendMessage = async (conversationId: string, content: string): Promise<boolean> => {
    if (!user) return false;

    try {
      setError(null);

      const response = await fetch('/api/messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          conversation_id: conversationId,
          content,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to send message');
      }

      const data = await response.json();

      // If the message was censored, show a warning
      if (data.censored) {
        toast({
          title: 'Message censored',
          description: `Your message contained inappropriate language (${data.badWords.join(', ')}) and has been censored.`,
          variant: 'destructive',
        });
      }

      return true;
    } catch (error: any) {
      setError(error.message || 'Failed to send message');
      console.error('Error sending message:', error);

      toast({
        title: 'Error',
        description: error.message || 'Failed to send message',
        variant: 'destructive',
      });

      return false;
    }
  };

  // Start a new conversation
  const startConversation = async (userId: string): Promise<string | null> => {
    if (!user) return null;

    try {
      setError(null);

      const response = await fetch('/api/conversations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id: userId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to start conversation');
      }

      const data = await response.json();

      // Add the new conversation to the list
      const newConversation = data.conversation;
      setConversations(prev => {
        // Check if the conversation already exists
        if (prev.some(conv => conv.id === newConversation.id)) {
          return prev;
        }
        return [newConversation, ...prev];
      });

      return newConversation.id;
    } catch (error: any) {
      setError(error.message || 'Failed to start conversation');
      console.error('Error starting conversation:', error);

      toast({
        title: 'Error',
        description: error.message || 'Failed to start conversation',
        variant: 'destructive',
      });

      return null;
    }
  };

  // Accept a conversation request
  const acceptConversationRequest = async (conversationId: string): Promise<boolean> => {
    if (!user) return false;

    try {
      setError(null);

      const response = await fetch(`/api/conversations/${conversationId}/accept`, {
        method: 'POST',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to accept conversation request');
      }

      // Update the conversation status in the list
      setConversations(prev =>
        prev.map(conv =>
          conv.id === conversationId ? { ...conv, status: 'accepted' } : conv
        )
      );

      // Update active conversation if it's the current one
      if (activeConversation?.id === conversationId) {
        setActiveConversation(prev => prev ? { ...prev, status: 'accepted' } : null);
      }

      toast({
        title: 'Conversation accepted',
        description: 'You can now exchange messages with this user.',
      });

      return true;
    } catch (error: any) {
      setError(error.message || 'Failed to accept conversation request');
      console.error('Error accepting conversation request:', error);

      toast({
        title: 'Error',
        description: error.message || 'Failed to accept conversation request',
        variant: 'destructive',
      });

      return false;
    }
  };

  // Reject a conversation request
  const rejectConversationRequest = async (conversationId: string): Promise<boolean> => {
    if (!user) return false;

    try {
      setError(null);

      const response = await fetch(`/api/conversations/${conversationId}/reject`, {
        method: 'POST',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to reject conversation request');
      }

      // Update the conversation status in the list
      setConversations(prev =>
        prev.map(conv =>
          conv.id === conversationId ? { ...conv, status: 'rejected' } : conv
        )
      );

      // Update active conversation if it's the current one
      if (activeConversation?.id === conversationId) {
        setActiveConversation(prev => prev ? { ...prev, status: 'rejected' } : null);
      }

      toast({
        title: 'Conversation rejected',
        description: 'You have rejected this conversation request.',
      });

      return true;
    } catch (error: any) {
      setError(error.message || 'Failed to reject conversation request');
      console.error('Error rejecting conversation request:', error);

      toast({
        title: 'Error',
        description: error.message || 'Failed to reject conversation request',
        variant: 'destructive',
      });

      return false;
    }
  };

  // Set up polling for conversations and messages (forum-style)
  useEffect(() => {
    if (!user) return;

    console.log('Setting up polling for conversations with user ID:', user.id);

    // Initial fetch
    fetchConversations();

    // Set up polling interval for conversations
    const conversationsPollingInterval = setInterval(() => {
      console.log('Polling for conversation updates');
      fetchConversations();
    }, 5000); // Poll every 5 seconds

    return () => {
      console.log('Cleaning up polling for user:', user.id);
      clearInterval(conversationsPollingInterval);
    };
  }, [user?.id, fetchConversations]);

  // Set up polling for messages when there's an active conversation
  useEffect(() => {
    if (!user || !activeConversation) return;

    console.log('Setting up polling for messages in conversation:', activeConversation.id);

    // Set up polling interval for messages
    const messagesPollingInterval = setInterval(() => {
      console.log('Polling for message updates in conversation:', activeConversation.id);
      fetchMessages(activeConversation.id);
    }, 3000); // Poll every 3 seconds

    return () => {
      console.log('Cleaning up message polling for conversation:', activeConversation.id);
      clearInterval(messagesPollingInterval);
    };
  }, [user?.id, activeConversation?.id, fetchMessages]);

  // End a conversation
  const endConversation = async (conversationId: string): Promise<boolean> => {
    if (!user) return false;

    try {
      setError(null);

      const response = await fetch(`/api/conversations/${conversationId}/end`, {
        method: 'POST',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to end conversation');
      }

      // Update the conversation status in the list
      setConversations(prev =>
        prev.map(conv =>
          conv.id === conversationId ? { ...conv, status: 'ended' } : conv
        )
      );

      // Update active conversation if it's the current one
      if (activeConversation?.id === conversationId) {
        setActiveConversation(prev => prev ? { ...prev, status: 'ended' } : null);
      }

      toast({
        title: 'Conversation ended',
        description: 'This conversation has been ended.',
      });

      return true;
    } catch (error: any) {
      setError(error.message || 'Failed to end conversation');
      console.error('Error ending conversation:', error);

      toast({
        title: 'Error',
        description: error.message || 'Failed to end conversation',
        variant: 'destructive',
      });

      return false;
    }
  };

  // Delete a conversation
  const deleteConversation = async (conversationId: string): Promise<boolean> => {
    if (!user) return false;

    try {
      setError(null);

      console.log('Deleting conversation from context:', conversationId);

      const response = await fetch(`/api/conversations/${conversationId}/delete`, {
        method: 'POST',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete conversation');
      }

      const result = await response.json();
      console.log('Delete conversation response:', result);

      // Remove the conversation from the list
      setConversations(prev => {
        console.log('Removing conversation from state:', conversationId);
        return prev.filter(conv => conv.id !== conversationId);
      });

      // Clear active conversation if it's the current one
      if (activeConversation?.id === conversationId) {
        console.log('Clearing active conversation');
        setActiveConversation(null);
        // No navigation needed - the UI will update based on the state change
      }

      toast({
        title: 'Conversation deleted',
        description: 'The conversation has been deleted.',
      });

      return true;
    } catch (error: any) {
      setError(error.message || 'Failed to delete conversation');
      console.error('Error deleting conversation:', error);

      toast({
        title: 'Error',
        description: error.message || 'Failed to delete conversation',
        variant: 'destructive',
      });

      return false;
    }
  };

  return (
    <MessagingContext.Provider
      value={{
        conversations,
        activeConversation,
        messages,
        loadingConversations,
        loadingMessages,
        error,
        fetchConversations,
        fetchMessages,
        sendMessage,
        markConversationAsRead,
        startConversation,
        acceptConversationRequest,
        rejectConversationRequest,
        endConversation,
        deleteConversation,
        setActiveConversation
      }}
    >
      {children}
    </MessagingContext.Provider>
  );
};
