'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { Star, Clock, Calendar, User } from 'lucide-react';

interface SkillOwner {
  id: string;
  display_name?: string | null;
  avatar_url?: string | null;
}

// Define a type that can handle both single owner and array of owners
type OwnerData = SkillOwner | SkillOwner[] | any;

interface Review {
  id: string;
  rating: number;
  comment: string;
  created_at: string;
}

interface AvailableDate {
  id: string;
  date_time: string;
  duration_hours: number;
  is_booked: boolean;
}

interface Skill {
  id: string;
  title: string;
  description: string;
  tags: string[];
  owner_id: string;
  created_at: string;
  is_active: boolean;
  difficulty_level?: 'beginner' | 'intermediate' | 'advanced';
  image_url?: string | null;
  owner: OwnerData;
  reviews?: Review[];
  avg_rating?: number;
  last_session_date?: string | null;
  available_dates?: AvailableDate[];
}

export default function FeaturedSkills({ skills }: { skills: Skill[] }) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [direction, setDirection] = useState(0);
  const [isHovering, setIsHovering] = useState(false);

  // Auto-rotate skills every 5 seconds unless hovering
  useEffect(() => {
    if (isHovering) return;

    const interval = setInterval(() => {
      setDirection(1);
      setCurrentIndex((prevIndex) => (prevIndex + 1) % skills.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [skills.length, isHovering]);

  const handlePrev = () => {
    setDirection(-1);
    setCurrentIndex((prevIndex) => (prevIndex - 1 + skills.length) % skills.length);
  };

  const handleNext = () => {
    setDirection(1);
    setCurrentIndex((prevIndex) => (prevIndex + 1) % skills.length);
  };

  // Animation variants
  const variants = {
    enter: (direction: number) => ({
      x: direction > 0 ? 1000 : -1000,
      opacity: 0,
      scale: 0.9,
    }),
    center: {
      x: 0,
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.5,
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    },
    exit: (direction: number) => ({
      x: direction < 0 ? 1000 : -1000,
      opacity: 0,
      scale: 0.9,
      transition: {
        duration: 0.5
      }
    })
  };

  // If no skills are provided, show a placeholder
  if (!skills || skills.length === 0) {
    return (
      <div className="bg-gray-800 rounded-xl p-8 text-center">
        <p className="text-gray-400">No featured skills available at the moment.</p>
      </div>
    );
  }

  const currentSkill = skills[currentIndex];

  return (
    <div
      className="relative overflow-hidden"
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
    >
      {/* Navigation buttons */}
      <button
        onClick={handlePrev}
        className="absolute left-4 top-1/2 -translate-y-1/2 z-10 bg-gray-900/50 hover:bg-gray-900/80 text-white p-2 rounded-full transition-all"
        aria-label="Previous skill"
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
        </svg>
      </button>

      <button
        onClick={handleNext}
        className="absolute right-4 top-1/2 -translate-y-1/2 z-10 bg-gray-900/50 hover:bg-gray-900/80 text-white p-2 rounded-full transition-all"
        aria-label="Next skill"
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
        </svg>
      </button>

      {/* Skill card */}
      <motion.div
        key={currentSkill.id}
        custom={direction}
        variants={variants}
        initial="enter"
        animate="center"
        exit="exit"
        className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl overflow-hidden shadow-xl"
      >
        {/* Skill Image */}
        {currentSkill.image_url && (
          <div className="w-full h-64 overflow-hidden">
            <img
              src={currentSkill.image_url}
              alt={currentSkill.title}
              className="w-full h-full object-cover"
            />
          </div>
        )}

        <div className="p-6 md:p-8">
          <div className="flex flex-col md:flex-row gap-6">
            {/* Left side - Skill info */}
            <div className="md:w-2/3">
              <div className="flex items-start justify-between mb-2">
                <div>
                  <h3 className="text-2xl font-bold text-white">{currentSkill.title}</h3>

                  {/* Difficulty Level Badge */}
                  {currentSkill.difficulty_level && (
                    <div className="mt-2 mb-2">
                      <span className={`text-xs px-3 py-1 rounded-full ${
                        currentSkill.difficulty_level === 'beginner'
                          ? 'bg-green-900/50 text-green-300'
                          : currentSkill.difficulty_level === 'intermediate'
                          ? 'bg-yellow-900/50 text-yellow-300'
                          : 'bg-red-900/50 text-red-300'
                      }`}>
                        {currentSkill.difficulty_level.charAt(0).toUpperCase() + currentSkill.difficulty_level.slice(1)} Level
                      </span>
                    </div>
                  )}
                </div>

                <div className="flex items-center bg-blue-600/20 text-blue-400 px-3 py-1 rounded-full text-sm">
                  <Star className="w-4 h-4 mr-1" />
                  <span>{currentSkill.avg_rating?.toFixed(1) || 'New'}</span>
                </div>
              </div>

              <p className="text-gray-300 mb-6 line-clamp-3">{currentSkill.description}</p>

              <div className="flex flex-wrap gap-2 mb-6">
                {currentSkill.tags?.map((tag, index) => (
                  <span
                    key={index}
                    className="bg-gray-700 text-gray-300 px-3 py-1 rounded-full text-sm"
                  >
                    {tag}
                  </span>
                ))}
              </div>

              <div className="space-y-3 mb-6">
                {currentSkill.last_session_date && (
                  <div className="flex items-center text-gray-400">
                    <Calendar className="w-5 h-5 mr-2 text-gray-500" />
                    <span>Last session: {new Date(currentSkill.last_session_date).toLocaleDateString()}</span>
                  </div>
                )}

                {currentSkill.available_dates && currentSkill.available_dates.length > 0 && (
                  <div className="flex items-center text-gray-400">
                    <Clock className="w-5 h-5 mr-2 text-gray-500" />
                    <span>Next available: {new Date(currentSkill.available_dates[0].date_time).toLocaleString(undefined, {
                      month: 'short',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}</span>
                  </div>
                )}
              </div>

              <Link
                href={`/skills/${currentSkill.id}`}
                className="inline-block px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              >
                View Details
              </Link>
            </div>

            {/* Right side - Teacher info */}
            <div className="md:w-1/3 flex flex-col items-center">
              <div className="w-24 h-24 rounded-full overflow-hidden mb-4 bg-gray-700 flex items-center justify-center">
                {(() => {
                  // Handle different owner data structures
                  let ownerData: SkillOwner = { id: currentSkill.owner_id, display_name: null, avatar_url: null };

                  // Use type assertion with unknown first to avoid TypeScript errors
                  const ownerRaw = currentSkill.owner as unknown;

                  if (Array.isArray(ownerRaw) && ownerRaw.length > 0) {
                    const firstOwner = ownerRaw[0];
                    if (firstOwner && typeof firstOwner === 'object') {
                      if ('id' in firstOwner) ownerData.id = firstOwner.id;
                      if ('display_name' in firstOwner) ownerData.display_name = firstOwner.display_name;
                      if ('avatar_url' in firstOwner) ownerData.avatar_url = firstOwner.avatar_url;
                    }
                  } else if (ownerRaw && typeof ownerRaw === 'object' && !Array.isArray(ownerRaw)) {
                    const singleOwner = ownerRaw as Record<string, any>;
                    if ('id' in singleOwner) ownerData.id = singleOwner.id;
                    if ('display_name' in singleOwner) ownerData.display_name = singleOwner.display_name;
                    if ('avatar_url' in singleOwner) ownerData.avatar_url = singleOwner.avatar_url;
                  }

                  return ownerData.avatar_url ? (
                    <Image
                      src={ownerData.avatar_url}
                      alt={ownerData.display_name || 'Teacher'}
                      width={96}
                      height={96}
                      className="object-cover w-full h-full"
                    />
                  ) : (
                    <User className="w-12 h-12 text-gray-500" />
                  );
                })()}
              </div>

              <h4 className="text-lg font-medium text-white mb-2">
                {(() => {
                  // Handle different owner data structures
                  let displayName = 'Unknown Teacher';

                  // Use type assertion with unknown first to avoid TypeScript errors
                  const ownerRaw = currentSkill.owner as unknown;

                  if (Array.isArray(ownerRaw) && ownerRaw.length > 0) {
                    const firstOwner = ownerRaw[0];
                    if (firstOwner && typeof firstOwner === 'object' && 'display_name' in firstOwner) {
                      displayName = firstOwner.display_name || 'Unknown Teacher';
                    }
                  } else if (ownerRaw && typeof ownerRaw === 'object' && !Array.isArray(ownerRaw)) {
                    const singleOwner = ownerRaw as Record<string, any>;
                    if ('display_name' in singleOwner) {
                      displayName = singleOwner.display_name || 'Unknown Teacher';
                    }
                  }

                  return displayName;
                })()}
              </h4>

              <div className="text-center">
                <div className="text-sm text-gray-400 mb-4">Teacher</div>

                <Link
                  href={`/profile/${currentSkill.owner_id}`}
                  className="text-blue-400 hover:text-blue-300 text-sm"
                >
                  View Profile
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Progress indicators */}
        <div className="flex justify-center gap-2 p-4 bg-gray-900/50">
          {skills.map((_, index) => (
            <button
              key={index}
              onClick={() => {
                setDirection(index > currentIndex ? 1 : -1);
                setCurrentIndex(index);
              }}
              className={`w-2 h-2 rounded-full transition-all ${
                index === currentIndex
                  ? 'bg-blue-500 w-6'
                  : 'bg-gray-600 hover:bg-gray-500'
              }`}
              aria-label={`Go to skill ${index + 1}`}
            />
          ))}
        </div>
      </motion.div>
    </div>
  );
}
