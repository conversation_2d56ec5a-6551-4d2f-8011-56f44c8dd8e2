'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { Edit, AlertTriangle } from 'lucide-react';
import AdminPageHeader from '@/components/admin/AdminPageHeader';
import AdminDataTable from '@/components/admin/AdminDataTable';

interface Skill {
  id: string;
  title: string;
}

interface Profile {
  id: string;
  display_name: string | null;
  email: string | null;
  avatar_url: string | null;
}

interface Session {
  id: string;
  skill: Skill;
  teacher: Profile;
  learner: Profile;
  scheduled_at: string;
  duration_hours: number;
  status: string;
  created_at: string;
}

export default function AdminSessionsPage() {
  const [sessions, setSessions] = useState<Session[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [total, setTotal] = useState(0);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    // Get status filter from URL if present
    const status = searchParams.get('status');
    if (status) {
      setStatusFilter(status);
    }
  }, [searchParams]);

  const fetchSessions = async () => {
    try {
      setLoading(true);
      
      const queryParams = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        status: statusFilter,
      });
      
      const response = await fetch(`/api/admin/sessions?${queryParams.toString()}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch sessions');
      }
      
      const data = await response.json();
      setSessions(data.sessions);
      setTotal(data.total);
    } catch (error: any) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSessions();
  }, [page, limit, statusFilter]);

  const updateUrlParams = (status: string) => {
    const params = new URLSearchParams(searchParams.toString());
    
    if (status === 'all') {
      params.delete('status');
    } else {
      params.set('status', status);
    }
    
    router.push(`/admin/sessions${params.toString() ? `?${params.toString()}` : ''}`);
  };

  const columns = [
    {
      key: 'skill',
      label: 'Skill',
      render: (skill: Skill) => (
        <div className="font-medium text-gray-900 dark:text-white">
          {skill?.title || 'Unknown Skill'}
        </div>
      ),
    },
    {
      key: 'teacher',
      label: 'Teacher',
      render: (teacher: Profile) => (
        <div className="flex items-center">
          <div className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 flex-shrink-0 overflow-hidden mr-3">
            {teacher?.avatar_url ? (
              <img
                src={teacher.avatar_url}
                alt={teacher?.display_name || 'User'}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {teacher?.display_name?.charAt(0) || teacher?.email?.charAt(0) || '?'}
                </span>
              </div>
            )}
          </div>
          <div className="text-sm text-gray-900 dark:text-white">
            {teacher?.display_name || teacher?.email || 'Unknown User'}
          </div>
        </div>
      ),
    },
    {
      key: 'learner',
      label: 'Learner',
      render: (learner: Profile) => (
        <div className="flex items-center">
          <div className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 flex-shrink-0 overflow-hidden mr-3">
            {learner?.avatar_url ? (
              <img
                src={learner.avatar_url}
                alt={learner?.display_name || 'User'}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {learner?.display_name?.charAt(0) || learner?.email?.charAt(0) || '?'}
                </span>
              </div>
            )}
          </div>
          <div className="text-sm text-gray-900 dark:text-white">
            {learner?.display_name || learner?.email || 'Unknown User'}
          </div>
        </div>
      ),
    },
    {
      key: 'scheduled_at',
      label: 'Date',
      sortable: true,
      render: (value: string) => (
        <div className="text-gray-900 dark:text-white">
          {new Date(value).toLocaleDateString()}
        </div>
      ),
    },
    {
      key: 'duration_hours',
      label: 'Duration',
      render: (hours: number) => (
        <div className="text-gray-900 dark:text-white">
          {hours} hour{hours !== 1 ? 's' : ''}
        </div>
      ),
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (status: string) => (
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            status === 'completed'
              ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'
              : status === 'pending'
              ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300'
              : status === 'cancelled'
              ? 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'
              : status === 'disputed'
              ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300'
              : 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300'
          }`}
        >
          {status}
        </span>
      ),
    },
  ];

  const renderActions = (session: Session) => (
    <div className="flex items-center space-x-3">
      <Link
        href={`/admin/sessions/${session.id}`}
        className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
      >
        <Edit className="h-5 w-5" />
      </Link>
      
      {session.status === 'disputed' && (
        <Link
          href={`/admin/sessions/${session.id}?resolve=true`}
          className="text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300"
          title="Resolve dispute"
        >
          <AlertTriangle className="h-5 w-5" />
        </Link>
      )}
    </div>
  );

  return (
    <div>
      <AdminPageHeader
        title="Session Management"
        description="View and manage teaching/learning sessions"
      />
      
      {error && (
        <div className="bg-red-100 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 px-4 py-3 rounded-lg mb-6">
          {error}
        </div>
      )}
      
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-md mb-6">
        <div>
          <label htmlFor="status-filter" className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
            Filter by Status
          </label>
          <select
            id="status-filter"
            value={statusFilter}
            onChange={(e) => {
              setStatusFilter(e.target.value);
              updateUrlParams(e.target.value);
            }}
            className="px-4 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">All Sessions</option>
            <option value="pending">Pending</option>
            <option value="confirmed">Confirmed</option>
            <option value="completed">Completed</option>
            <option value="cancelled">Cancelled</option>
            <option value="disputed">Disputed</option>
          </select>
        </div>
      </div>
      
      <AdminDataTable
        columns={columns}
        data={sessions}
        actions={renderActions}
        loading={loading}
        emptyState={
          <div className="text-center">
            <p className="text-gray-500 dark:text-gray-400 mb-4">No sessions found</p>
          </div>
        }
      />
      
      {/* Pagination */}
      {total > 0 && (
        <div className="mt-6 flex items-center justify-between">
          <div className="text-sm text-gray-700 dark:text-gray-300">
            Showing <span className="font-medium">{(page - 1) * limit + 1}</span> to{' '}
            <span className="font-medium">{Math.min(page * limit, total)}</span> of{' '}
            <span className="font-medium">{total}</span> sessions
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => setPage(page - 1)}
              disabled={page === 1}
              className={`px-3 py-1 rounded-md ${
                page === 1
                  ? 'bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-600 cursor-not-allowed'
                  : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600'
              }`}
            >
              Previous
            </button>
            <button
              onClick={() => setPage(page + 1)}
              disabled={page * limit >= total}
              className={`px-3 py-1 rounded-md ${
                page * limit >= total
                  ? 'bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-600 cursor-not-allowed'
                  : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600'
              }`}
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
