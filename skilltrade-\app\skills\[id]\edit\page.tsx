'use client';

import { useState, useEffect } from 'react';
import { createClientSide } from '@/lib/supabase';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import MainHeader from '@/components/MainHeader';
import MainFooter from '@/components/MainFooter';
import { XCircle } from 'lucide-react';

interface AvailableDate {
  id: string;
  skill_id: string;
  date_time: string;
  duration_hours: number;
  is_booked: boolean;
  created_at: string;
  // Local UI state
  date?: string;
  time?: string;
}

interface Skill {
  id: string;
  title: string;
  description: string;
  tags: string[];
  is_active: boolean;
  available_dates?: AvailableDate[];
}

export default function EditSkill({ params }: { params: { id: string } }) {
  const [skill, setSkill] = useState<Skill | null>(null);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [tags, setTags] = useState('');
  const [isActive, setIsActive] = useState(true);
  const [availableDates, setAvailableDates] = useState<AvailableDate[]>([]);
  const [newDate, setNewDate] = useState('');
  const [newTime, setNewTime] = useState('');
  const [newDuration, setNewDuration] = useState(1);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [message, setMessage] = useState<string | null>(null);
  const router = useRouter();
  const supabase = createClientSide();
  const { id } = params;

  const handleAddAvailableDate = () => {
    // Validate inputs
    if (!newDate || !newTime) {
      setError('Please select both date and time');
      return;
    }

    // Check if date is in the future
    const dateTime = new Date(`${newDate}T${newTime}`);
    if (dateTime <= new Date()) {
      setError('Please select a future date and time');
      return;
    }

    // Check if we already have 3 dates
    if (availableDates.length >= 3) {
      setError('You can only add up to 3 available dates');
      return;
    }

    // Check if this date/time is already added
    const isDuplicate = availableDates.some(
      date => date.date === newDate && date.time === newTime
    );

    if (isDuplicate) {
      setError('This date and time is already added');
      return;
    }

    // Add the new date (with a temporary ID for UI purposes)
    setAvailableDates([
      ...availableDates,
      {
        id: `temp-${crypto.randomUUID()}`,
        skill_id: id,
        date_time: new Date(`${newDate}T${newTime}`).toISOString(),
        duration_hours: newDuration,
        is_booked: false,
        created_at: new Date().toISOString(),
        date: newDate,
        time: newTime
      }
    ]);

    // Clear inputs and error
    setNewDate('');
    setNewTime('');
    setNewDuration(1);
    setError(null);
  };

  const handleRemoveAvailableDate = async (dateId: string) => {
    // Check if this is a temporary ID (not yet saved to database)
    const isTemporary = dateId.startsWith('temp-');

    if (!isTemporary) {
      // Check if the date is already booked
      const dateToRemove = availableDates.find(date => date.id === dateId);
      if (dateToRemove?.is_booked) {
        setError('Cannot remove a date that has already been booked');
        return;
      }

      // Delete from database
      const { error: deleteError } = await supabase
        .from('skill_available_dates')
        .delete()
        .eq('id', dateId);

      if (deleteError) {
        setError('Failed to remove date: ' + deleteError.message);
        return;
      }
    }

    // Remove from state
    setAvailableDates(availableDates.filter(date => date.id !== dateId));
  };

  useEffect(() => {
    const fetchSkill = async () => {
      try {
        setLoading(true);

        // Get the current user
        const { data: { user } } = await supabase.auth.getUser();

        if (!user) {
          router.push('/login');
          return;
        }

        // Get the skill
        const { data, error } = await supabase
          .from('skills')
          .select('*')
          .eq('id', id)
          .single();

        if (error) {
          throw error;
        }

        // Check if the skill belongs to the user
        if (data.owner_id !== user.id) {
          router.push('/skills');
          return;
        }

        // Get available dates
        const { data: datesData, error: datesError } = await supabase
          .from('skill_available_dates')
          .select('*')
          .eq('skill_id', id)
          .order('date_time', { ascending: true });

        if (datesError) {
          console.error('Error fetching available dates:', datesError);
        }

        // Process dates for UI
        const processedDates = datesData?.map(date => {
          const dateObj = new Date(date.date_time);
          return {
            ...date,
            date: dateObj.toISOString().split('T')[0],
            time: dateObj.toTimeString().slice(0, 5)
          };
        }) || [];

        setSkill({...data, available_dates: processedDates});
        setTitle(data.title);
        setDescription(data.description);
        setTags(data.tags.join(', '));
        setIsActive(data.is_active);
        setAvailableDates(processedDates);
      } catch (error: any) {
        setError(error.message || 'Failed to load skill');
      } finally {
        setLoading(false);
      }
    };

    fetchSkill();
  }, [id, router, supabase]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    setError(null);
    setMessage(null);

    try {
      // Parse tags from comma-separated string to array
      const tagsArray = tags
        .split(',')
        .map(tag => tag.trim())
        .filter(tag => tag !== '');

      // Update the skill
      const { error } = await supabase
        .from('skills')
        .update({
          title,
          description,
          tags: tagsArray,
          is_active: isActive,
        })
        .eq('id', id);

      if (error) {
        throw error;
      }

      // Handle available dates
      // 1. Find new dates to add (those with temporary IDs)
      const newDatesToAdd = availableDates.filter(date => date.id.startsWith('temp-'));

      if (newDatesToAdd.length > 0) {
        // Format dates for insertion
        const formattedDates = newDatesToAdd.map(date => ({
          skill_id: id,
          date_time: date.date_time,
          duration_hours: date.duration_hours,
          is_booked: false
        }));

        // Insert new dates
        const { error: insertError } = await supabase
          .from('skill_available_dates')
          .insert(formattedDates);

        if (insertError) {
          console.error('Error adding available dates:', insertError);
          // Continue even if there's an error
        }
      }

      // Refresh available dates
      const { data: updatedDates, error: fetchError } = await supabase
        .from('skill_available_dates')
        .select('*')
        .eq('skill_id', id)
        .order('date_time', { ascending: true });

      if (!fetchError && updatedDates) {
        // Process dates for UI
        const processedDates = updatedDates.map(date => {
          const dateObj = new Date(date.date_time);
          return {
            ...date,
            date: dateObj.toISOString().split('T')[0],
            time: dateObj.toTimeString().slice(0, 5)
          };
        });

        setAvailableDates(processedDates);
      }

      setMessage('Skill updated successfully!');

      // Update the local state
      setSkill({
        ...skill!,
        title,
        description,
        tags: tagsArray,
        is_active: isActive,
        available_dates: availableDates
      });
    } catch (error: any) {
      setError(error.message || 'Failed to update skill');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <MainHeader />
        <div className="container mx-auto px-4 py-8">
          <div className="flex justify-center items-center h-64">
            <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
            <p className="ml-4 text-gray-700 dark:text-gray-300">Loading skill...</p>
          </div>
        </div>
        <MainFooter />
      </div>
    );
  }

  if (!skill) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <MainHeader />
        <div className="container mx-auto px-4 py-8">
          <div className="bg-white dark:bg-gray-800 rounded-xl p-8 text-center shadow-lg">
            <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Skill not found</h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              The skill you're looking for doesn't exist or you don't have permission to edit it.
            </p>
            <Link
              href="/skills"
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition inline-block"
            >
              Back to My Skills
            </Link>
          </div>
        </div>
        <MainFooter />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <MainHeader />

      <main className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Edit Skill</h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Update your skill details
            </p>
          </div>

          {error && (
            <div className="bg-red-100 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 px-4 py-3 rounded-lg mb-6">
              {error}
            </div>
          )}

          {message && (
            <div className="bg-green-100 dark:bg-green-900/30 border border-green-200 dark:border-green-800 text-green-800 dark:text-green-300 px-4 py-3 rounded-lg mb-6">
              {message}
            </div>
          )}

          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
            <form onSubmit={handleSubmit}>
              <div className="mb-6">
                <label htmlFor="title" className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                  Skill Title
                </label>
                <input
                  id="title"
                  type="text"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  className="w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>

              <div className="mb-6">
                <label htmlFor="description" className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                  Description
                </label>
                <textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  rows={6}
                  className="w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                ></textarea>
              </div>

              <div className="mb-6">
                <label htmlFor="tags" className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                  Tags
                </label>
                <input
                  id="tags"
                  type="text"
                  value={tags}
                  onChange={(e) => setTags(e.target.value)}
                  className="w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="e.g., programming, music, language (comma separated)"
                />
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Add relevant tags to help others find your skill
                </p>
              </div>

              <div className="mb-6">
                <div className="flex items-center">
                  <input
                    id="is-active"
                    type="checkbox"
                    checked={isActive}
                    onChange={(e) => setIsActive(e.target.checked)}
                    className="h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
                  />
                  <label htmlFor="is-active" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                    Active (visible to others)
                  </label>
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 ml-7">
                  Deactivate if you're temporarily unavailable to teach this skill
                </p>
              </div>

              <div className="mb-6">
                <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                  Available Dates (up to 3)
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400 mb-4">
                  Add dates when you're available to teach this skill. Learners will be able to choose from these dates.
                </p>

                {/* Available dates list */}
                {availableDates.length > 0 && (
                  <div className="mb-4 space-y-2">
                    {availableDates.map((date) => (
                      <div
                        key={date.id}
                        className="flex items-center justify-between bg-gray-50 dark:bg-gray-700 p-3 rounded-lg"
                      >
                        <div>
                          <span className="text-gray-900 dark:text-white font-medium">
                            {new Date(date.date_time).toLocaleDateString()} at {new Date(date.date_time).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                          </span>
                          <span className="text-gray-500 dark:text-gray-400 ml-2">
                            ({date.duration_hours} {date.duration_hours === 1 ? 'hour' : 'hours'})
                          </span>
                          {date.is_booked && (
                            <span className="ml-2 px-2 py-0.5 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 text-xs rounded-full">
                              Booked
                            </span>
                          )}
                        </div>
                        <button
                          type="button"
                          onClick={() => handleRemoveAvailableDate(date.id)}
                          className={`text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 ${date.is_booked ? 'opacity-50 cursor-not-allowed' : ''}`}
                          disabled={date.is_booked}
                          title={date.is_booked ? "Can't remove booked dates" : "Remove date"}
                        >
                          <XCircle size={18} />
                        </button>
                      </div>
                    ))}
                  </div>
                )}

                {/* Add new date form */}
                {availableDates.length < 3 && (
                  <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div>
                        <label htmlFor="new-date" className="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">
                          Date
                        </label>
                        <input
                          id="new-date"
                          type="date"
                          value={newDate}
                          onChange={(e) => setNewDate(e.target.value)}
                          min={new Date().toISOString().split('T')[0]}
                          className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                      </div>
                      <div>
                        <label htmlFor="new-time" className="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">
                          Time
                        </label>
                        <input
                          id="new-time"
                          type="time"
                          value={newTime}
                          onChange={(e) => setNewTime(e.target.value)}
                          className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                      </div>
                      <div>
                        <label htmlFor="new-duration" className="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">
                          Duration
                        </label>
                        <select
                          id="new-duration"
                          value={newDuration}
                          onChange={(e) => setNewDuration(Number(e.target.value))}
                          className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        >
                          <option value={1}>1 hour</option>
                          <option value={2}>2 hours</option>
                          <option value={3}>3 hours</option>
                        </select>
                      </div>
                    </div>
                    <div className="flex justify-end">
                      <button
                        type="button"
                        onClick={handleAddAvailableDate}
                        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition text-sm"
                      >
                        Add Date
                      </button>
                    </div>
                  </div>
                )}
                {availableDates.length >= 3 && (
                  <p className="text-xs text-amber-500 dark:text-amber-400 mt-2">
                    You've reached the maximum of 3 available dates.
                  </p>
                )}
              </div>

              <div className="flex flex-col sm:flex-row justify-end gap-4 mt-8">
                <Link
                  href="/skills"
                  className="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition text-center"
                >
                  Cancel
                </Link>
                <button
                  type="submit"
                  className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition font-medium"
                  disabled={saving}
                >
                  {saving ? 'Saving...' : 'Save Changes'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </main>

      <MainFooter />
    </div>
  );
}
