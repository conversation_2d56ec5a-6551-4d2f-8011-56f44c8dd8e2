'use client';

import { useState, useEffect } from 'react';
import { createClientSide } from '@/lib/supabase';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

interface Session {
  id: string;
  skill_id: string;
  teacher_id: string;
  learner_id: string;
  scheduled_at: string;
  duration_hours: number;
  status: string;
  skill: {
    title: string;
  };
  teacher: {
    display_name: string | null;
    email: string;
  };
}

export default function ReviewPage({ params }: { params: { id: string } }) {
  const [session, setSession] = useState<Session | null>(null);
  const [rating, setRating] = useState<number>(5);
  const [comment, setComment] = useState('');
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const supabase = createClientSide();
  const { id } = params;

  useEffect(() => {
    const fetchSession = async () => {
      try {
        setLoading(true);

        // Get the current user
        const { data: { user } } = await supabase.auth.getUser();

        if (!user) {
          router.push('/login');
          return;
        }

        // Get the session with related data
        const { data: sessionData, error: sessionError } = await supabase
          .from('sessions')
          .select(`
            id,
            skill_id,
            teacher_id,
            learner_id,
            scheduled_at,
            duration_hours,
            status,
            skill:skills(title),
            teacher:profiles!sessions_teacher_id_fkey(display_name, email)
          `)
          .eq('id', id)
          .single();

        if (sessionError) {
          throw sessionError;
        }

        // Check if the current user is the learner
        if (sessionData.learner_id !== user.id) {
          router.push('/dashboard/sessions');
          return;
        }

        // Check if the session is completed
        if (sessionData.status !== 'completed') {
          router.push(`/dashboard/sessions/${id}`);
          return;
        }

        // Check if a review already exists
        const { data: existingReview, error: reviewError } = await supabase
          .from('reviews')
          .select('id')
          .eq('session_id', id)
          .maybeSingle();

        if (reviewError) {
          throw reviewError;
        }

        if (existingReview) {
          router.push(`/dashboard/sessions/${id}`);
          return;
        }

        // Transform the data to match the Session interface
        const transformedSession: Session = {
          ...sessionData,
          skill: Array.isArray(sessionData.skill) && sessionData.skill.length > 0
            ? { title: sessionData.skill[0].title }
            : { title: 'Unknown Skill' },
          teacher: Array.isArray(sessionData.teacher) && sessionData.teacher.length > 0
            ? {
                display_name: sessionData.teacher[0].display_name,
                email: sessionData.teacher[0].email
              }
            : { display_name: null, email: '<EMAIL>' }
        };

        setSession(transformedSession);
      } catch (error: any) {
        setError(error.message || 'Failed to load session');
      } finally {
        setLoading(false);
      }
    };

    fetchSession();
  }, [id, router, supabase]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!comment.trim()) {
      setError('Please provide a comment for your review');
      return;
    }

    try {
      setSubmitting(true);
      setError(null);

      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user || !session) {
        throw new Error('User or session not found');
      }

      // Create the review
      const { error: reviewError } = await supabase
        .from('reviews')
        .insert({
          session_id: id,
          reviewer_id: user.id,
          rating,
          comment: comment.trim(),
        });

      if (reviewError) {
        console.error('Review error:', reviewError);
        throw reviewError;
      }

      // Update the session status based on rating
      // If rating is less than 3, mark as disputed, otherwise mark as reviewed
      const newStatus = rating < 3 ? 'disputed' : 'reviewed';

      const { error: sessionError } = await supabase
        .from('sessions')
        .update({ status: newStatus })
        .eq('id', id);

      if (sessionError) {
        throw sessionError;
      }

      // If it's a low rating (disputed), manually create notifications instead of relying on the trigger
      if (rating < 3) {
        try {
          console.log('Creating manual notifications for disputed session');

          // Get the other party's ID
          const otherPartyId = session.teacher_id === user.id ? session.learner_id : session.teacher_id;

          // Use the RPC function to create notification for the other party
          await supabase.rpc('create_notification', {
            user_id_param: otherPartyId,
            type_param: 'dispute',
            title_param: 'Session disputed',
            message_param: 'A session has been marked as disputed. Please communicate with the other participant to resolve the issue.',
            link_param: `/dashboard/sessions/${id}`
          });

          console.log('Notification created successfully');
        } catch (notifError) {
          // Just log the error but don't fail the whole operation
          console.error('Error creating notification:', notifError);
        }
      }

      // Redirect to the session page
      router.push(`/dashboard/sessions/${id}`);
    } catch (error: any) {
      setError(error.message || 'Failed to submit review');
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p className="ml-4 text-gray-700 dark:text-gray-300">Loading...</p>
      </div>
    );
  }

  if (!session) {
    return (
      <main className="container mx-auto px-4 py-8">
        <div className="bg-white dark:bg-gray-800 rounded-xl p-8 text-center shadow-lg">
          <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Session not found</h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            The session you're looking for doesn't exist or you don't have permission to review it.
          </p>
          <Link
            href="/dashboard/sessions"
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition inline-block"
          >
            Back to Sessions
          </Link>
        </div>
      </main>
    );
  }

  return (
    <main className="container mx-auto px-4 py-8">
      <div className="max-w-2xl mx-auto">
        <div className="mb-8">
          <Link
            href={`/dashboard/sessions/${id}`}
            className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition flex items-center gap-2"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M19 12H5M12 19l-7-7 7-7"/>
            </svg>
            Back to Session
          </Link>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
          <div className="p-6 sm:p-8">
            <h1 className="text-2xl font-bold mb-6 text-gray-900 dark:text-white">
              Review Your Learning Experience
            </h1>

            <div className="mb-6">
              <p className="text-gray-600 dark:text-gray-300">
                You're reviewing your session for <span className="font-medium text-gray-900 dark:text-white">{session.skill.title}</span> with <span className="font-medium text-gray-900 dark:text-white">{session.teacher.display_name || (session.teacher.email ? session.teacher.email.split('@')[0] : 'Unknown User')}</span>.
              </p>
            </div>

            {error && (
              <div className="bg-red-100 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 px-4 py-3 rounded-lg mb-6">
                {error}
              </div>
            )}

            <form onSubmit={handleSubmit}>
              <div className="mb-6">
                <label className="block text-gray-700 dark:text-gray-300 font-medium mb-2">
                  Rate your experience (1-5 stars)
                </label>
                <div className="flex items-center gap-2 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
                  <div className="flex">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <button
                        key={star}
                        type="button"
                        onClick={() => setRating(star)}
                        className="p-1 focus:outline-none focus:ring-0"
                        aria-label={`Rate ${star} stars`}
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className={`h-8 w-8 ${star <= rating ? 'text-yellow-500' : 'text-gray-300 dark:text-gray-600'} transition-colors duration-150`}
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                      </button>
                    ))}
                  </div>
                  <span className="ml-2 text-gray-700 dark:text-gray-300 font-medium">
                    {rating} {rating === 1 ? 'Star' : 'Stars'}
                  </span>
                </div>
                <div className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                  <p>Rating guide:</p>
                  <ul className="list-disc pl-5 mt-1 space-y-1">
                    <li>1-2 stars: Poor experience, significant issues</li>
                    <li>3 stars: Average experience, some issues</li>
                    <li>4-5 stars: Good to excellent experience</li>
                  </ul>
                  <p className="mt-1 italic">Note: Ratings below 3 stars will mark the session as disputed.</p>
                </div>
              </div>

              <div className="mb-6">
                <label htmlFor="comment" className="block text-gray-700 dark:text-gray-300 font-medium mb-2">
                  Your Review
                </label>
                <textarea
                  id="comment"
                  value={comment}
                  onChange={(e) => setComment(e.target.value)}
                  rows={5}
                  className="w-full px-4 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Share your experience with this teacher and session..."
                  required
                ></textarea>
              </div>

              <div className="flex flex-col sm:flex-row justify-end gap-4">
                <Link
                  href={`/dashboard/sessions/${id}`}
                  className="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition text-center"
                >
                  Cancel
                </Link>
                <button
                  type="submit"
                  disabled={submitting}
                  className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition font-medium"
                >
                  {submitting ? 'Submitting...' : 'Submit Review'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </main>
  );
}
