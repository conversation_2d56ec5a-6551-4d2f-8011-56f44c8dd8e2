'use client';

import Link from 'next/link';
import { formatDistanceToNow } from 'date-fns';
import { useRouter } from 'next/navigation';
import { useNotifications } from '@/contexts/NotificationsContext';

interface Notification {
  id: string;
  user_id: string;
  type: string;
  title: string;
  message?: string;  // Make message optional
  content?: string;  // Add content as an alternative
  link?: string | null;  // Make link optional
  is_read: boolean;
  created_at: string;
}

export default function NotificationsPage() {
  const router = useRouter();
  const {
    notifications,
    unreadCount,
    unreadMessageCount,
    totalUnreadCount,
    loading,
    error,
    markAsRead,
    markAllAsRead,
    fetchUnreadCounts
  } = useNotifications();

  const formatNotificationTime = (timestamp: string) => {
    return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'message':
        return (
          <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-blue-600 dark:text-blue-400">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z" />
              <path d="M15 7v2a4 4 0 01-4 4H9.828l-1.766 1.767c.28.149.599.233.938.233h2l3 3v-3h2a2 2 0 002-2V9a2 2 0 00-2-2h-1z" />
            </svg>
          </div>
        );
      case 'session':
        return (
          <div className="w-10 h-10 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center text-green-600 dark:text-green-400">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
            </svg>
          </div>
        );
      case 'review':
        return (
          <div className="w-10 h-10 rounded-full bg-yellow-100 dark:bg-yellow-900/30 flex items-center justify-center text-yellow-600 dark:text-yellow-400">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
          </div>
        );
      case 'dispute':
        return (
          <div className="w-10 h-10 rounded-full bg-red-100 dark:bg-red-900/30 flex items-center justify-center text-red-600 dark:text-red-400">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
        );
      default:
        return (
          <div className="w-10 h-10 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-gray-600 dark:text-gray-400">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-950">
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="flex flex-col mb-6">
            <div className="flex justify-between items-center">
              <h1 className="text-2xl font-bold text-white">Notifications</h1>
              {notifications.some(n => !n.is_read) && (
                <button
                  onClick={markAllAsRead}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition text-sm"
                >
                  Mark all as read
                </button>
              )}
            </div>

            <div className="flex mt-2 text-gray-400">
              <div className="mr-6">
                <span className="font-medium">{unreadCount}</span> unread notification{unreadCount !== 1 ? 's' : ''}
              </div>
              {unreadMessageCount > 0 && (
                <div>
                  <span className="font-medium">{unreadMessageCount}</span> unread message{unreadMessageCount !== 1 ? 's' : ''} in{' '}
                  <Link href="/dashboard/messages" className="text-blue-400 hover:text-blue-300">
                    conversations
                  </Link>
                </div>
              )}
            </div>
          </div>

          {error && (
            <div className="bg-red-100 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 px-4 py-3 rounded-lg mb-6">
              {error}
            </div>
          )}

          {loading ? (
            <div className="bg-gray-800 rounded-xl p-8 flex justify-center items-center">
              <div className="w-10 h-10 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
              <p className="ml-4 text-gray-300">Loading notifications...</p>
            </div>
          ) : notifications.length === 0 ? (
            <div className="bg-gray-800 rounded-xl p-8 text-center">
              <div className="w-16 h-16 mx-auto mb-4 text-gray-500">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                </svg>
              </div>
              <h2 className="text-xl font-semibold mb-2 text-white">No notifications yet</h2>
              <p className="text-gray-400">
                You don't have any notifications at the moment. Notifications will appear here when you receive messages, session updates, or reviews.
              </p>
            </div>
          ) : (
            <div className="bg-gray-800 rounded-xl overflow-hidden">
              <ul className="divide-y divide-gray-700">
                {notifications.map((notification) => (
                  <li
                    key={notification.id}
                    className={`${!notification.is_read ? 'bg-gray-700' : ''}`}
                  >
                    <div
                      className={`p-4 flex gap-4 hover:bg-gray-700 transition cursor-pointer ${notification.link ? 'cursor-pointer' : ''}`}
                      onClick={async (e) => {
                        e.preventDefault();
                        e.stopPropagation();

                        try {
                          if (!notification.is_read) {
                            // Wait for the markAsRead function to complete
                            console.log('Attempting to mark notification as read:', notification.id);
                            const success = await markAsRead(notification.id);
                            console.log('Mark as read success:', success);
                          }

                          // Add a small delay to ensure the API call completes
                          setTimeout(() => {
                            if (notification.link) {
                              // Use Next.js router for navigation
                              console.log('Navigating to:', notification.link);
                              router.push(notification.link);
                            }
                          }, 300);
                        } catch (error) {
                          console.error('Error in notification click handler:', error);
                        }
                      }}
                    >
                      {getNotificationIcon(notification.type)}
                      <div className="flex-1 min-w-0">
                        <div className="flex justify-between items-start">
                          <p className={`font-medium ${!notification.is_read ? 'text-white' : 'text-gray-300'}`}>
                            {notification.title}
                          </p>
                          <span className="text-xs text-gray-400 ml-2 whitespace-nowrap">
                            {formatNotificationTime(notification.created_at)}
                          </span>
                        </div>
                        <p className="text-gray-400 mt-1">
                          {notification.message || notification.content || "No message content"}
                        </p>
                        {notification.link && (
                          <Link
                            href={notification.link}
                            className="text-blue-400 hover:text-blue-300 text-sm mt-2 inline-block"
                            onClick={async (e) => {
                              e.stopPropagation();
                              try {
                                if (!notification.is_read) {
                                  console.log('Marking notification as read from link click:', notification.id);
                                  const success = await markAsRead(notification.id);
                                  console.log('Mark as read success from link:', success);

                                  // Small delay to ensure the API call completes
                                  await new Promise(resolve => setTimeout(resolve, 300));
                                }
                              } catch (error) {
                                console.error('Error marking notification as read from link:', error);
                              }
                            }}
                          >
                            View details
                          </Link>
                        )}
                      </div>
                      {!notification.is_read && (
                        <div className="w-2 h-2 bg-blue-500 rounded-full self-start mt-2 flex-shrink-0"></div>
                      )}
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </main>
    </div>
  );
}
