# Avatar Storage in Skilltrade

This document explains how to set up and use the avatar storage functionality in the Skilltrade application.

## Setting Up Avatar Storage in Supabase

### 1. Create the Avatars Bucket

1. Log in to the [Supabase Dashboard](https://app.supabase.com/)
2. Select your project
3. Navigate to Storage in the left sidebar
4. Click "New Bucket"
5. Name the bucket "avatars"
6. Set the bucket to public (for easy access to profile pictures)
7. Click "Create bucket"

### 2. Set Up Storage Policies

You have three options to set up the storage policies:

#### Option 1: Using the JavaScript Script

Run the following command:

```bash
npm run setup-avatar-policies
```

This script will automatically create the necessary policies for the avatars bucket.

#### Option 2: Using the SQL Editor

1. Navigate to the SQL Editor in the Supabase dashboard
2. Create a new query
3. Copy and paste the contents of either:
   - `supabase/avatar-policies.sql` for basic policies
   - `supabase/avatar-policies-advanced.sql` for more secure policies that enforce user-specific folders
4. Execute the query

#### Option 3: Manual Setup

1. Navigate to Storage > Buckets > avatars > Policies in the Supabase dashboard
2. Create the following policies:

**Basic Policies:**

- **Policy for Viewing Avatars**
  - Name: "Avatars are viewable by everyone"
  - Allowed operation: SELECT
  - Policy definition: `true`

- **Policy for Uploading Avatars**
  - Name: "Users can upload their own avatars"
  - Allowed operation: INSERT
  - Policy definition: `(auth.uid() = auth.uid())`

- **Policy for Updating Avatars**
  - Name: "Users can update their own avatars"
  - Allowed operation: UPDATE
  - Policy definition: `(auth.uid() = auth.uid())`

- **Policy for Deleting Avatars**
  - Name: "Users can delete their own avatars"
  - Allowed operation: DELETE
  - Policy definition: `(auth.uid() = auth.uid())`

**Advanced Policies (Recommended):**

- **Policy for Viewing Avatars**
  - Name: "Avatars are viewable by everyone"
  - Allowed operation: SELECT
  - Policy definition: `true`

- **Policy for Uploading Avatars**
  - Name: "Users can upload avatars to their own folder"
  - Allowed operation: INSERT
  - Policy definition: `(auth.uid() = SUBSTRING(path FROM 1 FOR POSITION('/', path) - 1)::uuid)`

- **Policy for Updating Avatars**
  - Name: "Users can update avatars in their own folder"
  - Allowed operation: UPDATE
  - Policy definition: `(auth.uid() = SUBSTRING(path FROM 1 FOR POSITION('/', path) - 1)::uuid)`

- **Policy for Deleting Avatars**
  - Name: "Users can delete avatars in their own folder"
  - Allowed operation: DELETE
  - Policy definition: `(auth.uid() = SUBSTRING(path FROM 1 FOR POSITION('/', path) - 1)::uuid)`

## Using the Avatar Upload Component

The `AvatarUpload` component provides a user-friendly interface for uploading, displaying, and removing avatars.

### Import the Component

```tsx
import AvatarUpload from '@/components/AvatarUpload';
```

### Basic Usage

```tsx
<AvatarUpload 
  userId={user.id} 
  url={user.avatar_url} 
  onUpload={(url) => {
    // Handle the new avatar URL, e.g., update the user state
    console.log('New avatar URL:', url);
  }}
/>
```

### Props

- `userId` (required): The ID of the user (UUID)
- `url` (optional): The current avatar URL
- `size` (optional): The size of the avatar in pixels (default: 150)
- `onUpload` (optional): Callback function that receives the new avatar URL

### Example in a Profile Page

```tsx
'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@supabase/supabase-js';
import AvatarUpload from '@/components/AvatarUpload';

// Create a Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabase = createClient(supabaseUrl, supabaseAnonKey);

export default function ProfilePage() {
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function getUser() {
      const { data: { session } } = await supabase.auth.getSession();
      
      if (session?.user) {
        const { data: profile } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', session.user.id)
          .single();
          
        setUser({ ...session.user, ...profile });
      }
      
      setLoading(false);
    }
    
    getUser();
  }, []);

  async function updateProfile(avatarUrl: string) {
    if (!user) return;
    
    const { error } = await supabase
      .from('profiles')
      .update({ avatar_url: avatarUrl })
      .eq('id', user.id);
      
    if (error) {
      console.error('Error updating profile:', error);
    } else {
      setUser({ ...user, avatar_url: avatarUrl });
    }
  }

  if (loading) {
    return <div>Loading...</div>;
  }

  if (!user) {
    return <div>Please sign in</div>;
  }

  return (
    <div className="max-w-md mx-auto p-6 bg-gray-800 rounded-lg shadow-lg">
      <h1 className="text-2xl font-bold mb-6">Profile</h1>
      
      <div className="flex flex-col items-center mb-6">
        <AvatarUpload 
          userId={user.id} 
          url={user.avatar_url} 
          onUpload={updateProfile}
        />
      </div>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-400">Email</label>
          <div className="mt-1 text-white">{user.email}</div>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-400">Display Name</label>
          <div className="mt-1 text-white">{user.display_name || 'Not set'}</div>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-400">Credit Balance</label>
          <div className="mt-1 text-white">{user.credit_balance} credits</div>
        </div>
      </div>
    </div>
  );
}
```

## Avatar Upload Utility Functions

The `utils/avatar-upload.ts` file provides utility functions for handling avatar uploads:

### `uploadAvatar(userId: string, file: File)`

Uploads an avatar image to Supabase Storage and updates the user's profile.

```tsx
import { uploadAvatar } from '@/utils/avatar-upload';

// Example usage
const handleFileChange = async (event) => {
  const file = event.target.files[0];
  try {
    const result = await uploadAvatar(userId, file);
    console.log('Avatar uploaded:', result.url);
  } catch (error) {
    console.error('Error uploading avatar:', error);
  }
};
```

### `deleteAvatar(userId: string, filePath: string)`

Deletes an avatar image from Supabase Storage and updates the user's profile.

```tsx
import { deleteAvatar } from '@/utils/avatar-upload';

// Example usage
const handleRemoveAvatar = async () => {
  try {
    // Extract the path from the URL
    const path = avatarUrl.split('/').slice(-2).join('/');
    await deleteAvatar(userId, path);
    console.log('Avatar removed');
  } catch (error) {
    console.error('Error removing avatar:', error);
  }
};
```

### `getAvatarUrl(userId: string)`

Gets the avatar URL for a user from their profile.

```tsx
import { getAvatarUrl } from '@/utils/avatar-upload';

// Example usage
const fetchAvatarUrl = async () => {
  try {
    const url = await getAvatarUrl(userId);
    console.log('Avatar URL:', url);
  } catch (error) {
    console.error('Error getting avatar URL:', error);
  }
};
```

## Best Practices

1. **User-Specific Folders**: Always store avatars in user-specific folders (e.g., `{userId}/{fileName}`) to maintain proper organization and security.

2. **File Size Limits**: Limit avatar file sizes (the utility enforces a 2MB limit).

3. **File Type Validation**: Only allow image files (the utility checks for the `image/` MIME type prefix).

4. **Unique File Names**: Use unique file names to prevent conflicts (the utility uses `{userId}-{timestamp}.{extension}`).

5. **Error Handling**: Always handle errors properly in your UI to provide feedback to users.

6. **Caching**: The utility sets a cache control of 1 hour (3600 seconds) for avatars to improve performance.

7. **Cleanup**: Implement a cleanup strategy for old avatars to manage storage usage.
