-- Migration for conversation request system
-- This adds a status field to conversations and updates related functions

-- 1. Add status field to conversations table
ALTER TABLE conversations 
ADD COLUMN status TEXT NOT NULL DEFAULT 'pending' 
CHECK (status IN ('pending', 'accepted', 'rejected'));

-- 2. Update the get_or_create_conversation function to handle the request system
CREATE OR REPLACE FUNCTION get_or_create_conversation(user1_id UUID, user2_id UUID)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  existing_conversation_id UUID;
  new_conversation_id UUID;
BEGIN
  -- Check if a conversation already exists between these users
  SELECT cp1.conversation_id INTO existing_conversation_id
  FROM conversation_participants cp1
  JOIN conversation_participants cp2 ON cp1.conversation_id = cp2.conversation_id
  JOIN conversations c ON c.id = cp1.conversation_id
  WHERE cp1.user_id = user1_id AND cp2.user_id = user2_id;
  
  -- If a conversation exists, return it
  IF existing_conversation_id IS NOT NULL THEN
    RETURN existing_conversation_id;
  END IF;
  
  -- Otherwise, create a new conversation with pending status
  INSERT INTO conversations (id, created_at, updated_at, status)
  VALUES (uuid_generate_v4(), NOW(), NOW(), 'pending')
  RETURNING id INTO new_conversation_id;
  
  -- Add both users as participants
  INSERT INTO conversation_participants (conversation_id, user_id)
  VALUES 
    (new_conversation_id, user1_id),
    (new_conversation_id, user2_id);
  
  -- Create a notification for the recipient
  PERFORM create_notification(
    user2_id,
    'conversation_request',
    'New conversation request',
    'Someone wants to start a conversation with you',
    '/dashboard/messages?id=' || new_conversation_id
  );
  
  RETURN new_conversation_id;
END;
$$;

-- 3. Create function to accept a conversation request
CREATE OR REPLACE FUNCTION accept_conversation_request(conversation_id UUID, user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  is_participant BOOLEAN;
  other_user_id UUID;
BEGIN
  -- Check if the user is a participant in the conversation
  SELECT EXISTS (
    SELECT 1 FROM conversation_participants
    WHERE conversation_id = $1 AND user_id = $2
  ) INTO is_participant;
  
  IF NOT is_participant THEN
    RAISE EXCEPTION 'User is not a participant in this conversation';
  END IF;
  
  -- Update the conversation status to accepted
  UPDATE conversations
  SET status = 'accepted'
  WHERE id = conversation_id;
  
  -- Get the other participant's ID
  SELECT cp.user_id INTO other_user_id
  FROM conversation_participants cp
  WHERE cp.conversation_id = $1 AND cp.user_id != $2
  LIMIT 1;
  
  -- Create a notification for the other user
  PERFORM create_notification(
    other_user_id,
    'conversation_accepted',
    'Conversation request accepted',
    'Your conversation request has been accepted',
    '/dashboard/messages?id=' || conversation_id
  );
  
  RETURN TRUE;
END;
$$;

-- 4. Create function to reject a conversation request
CREATE OR REPLACE FUNCTION reject_conversation_request(conversation_id UUID, user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  is_participant BOOLEAN;
  other_user_id UUID;
BEGIN
  -- Check if the user is a participant in the conversation
  SELECT EXISTS (
    SELECT 1 FROM conversation_participants
    WHERE conversation_id = $1 AND user_id = $2
  ) INTO is_participant;
  
  IF NOT is_participant THEN
    RAISE EXCEPTION 'User is not a participant in this conversation';
  END IF;
  
  -- Update the conversation status to rejected
  UPDATE conversations
  SET status = 'rejected'
  WHERE id = conversation_id;
  
  -- Get the other participant's ID
  SELECT cp.user_id INTO other_user_id
  FROM conversation_participants cp
  WHERE cp.conversation_id = $1 AND cp.user_id != $2
  LIMIT 1;
  
  -- Create a notification for the other user
  PERFORM create_notification(
    other_user_id,
    'conversation_rejected',
    'Conversation request rejected',
    'Your conversation request has been rejected',
    '/dashboard/messages'
  );
  
  RETURN TRUE;
END;
$$;
