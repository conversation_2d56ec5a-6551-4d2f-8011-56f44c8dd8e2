// User Scenario Tests for Profile Features
// This script simulates different user scenarios to test the profile features

const { chromium } = require('playwright');
require('dotenv').config({ path: '.env.test' });

// Configuration
const BASE_URL = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
const TEST_EMAIL = process.env.TEST_USER_EMAIL || '<EMAIL>';
const TEST_PASSWORD = process.env.TEST_USER_PASSWORD || 'password';

// User scenarios to test
const scenarios = [
  {
    name: 'New User Profile Setup',
    description: 'A new user sets up their profile for the first time',
    steps: [
      { action: 'navigate', path: '/login' },
      { action: 'login', email: TEST_EMAIL, password: TEST_PASSWORD },
      { action: 'navigate', path: '/dashboard/profile' },
      { action: 'fillField', selector: 'input[placeholder*="display name"]', value: 'Test User' },
      { action: 'fillField', selector: 'textarea[placeholder*="about yourself"]', value: 'I am a test user who loves learning and teaching new skills.' },
      { action: 'fillField', selector: 'textarea[placeholder*="teaching approach"]', value: 'I believe in hands-on learning and practical examples.' },
      { action: 'addLearningGoal', value: 'JavaScript' },
      { action: 'addLearningGoal', value: 'React' },
      { action: 'addLearningGoal', value: 'Node.js' },
      { action: 'addSkill', category: 'Web Development', level: 4, description: 'Experienced web developer with 5+ years of experience' },
      { action: 'addAvailabilitySlot', day: 'Monday', startTime: '09:00', endTime: '12:00' },
      { action: 'addAvailabilitySlot', day: 'Wednesday', startTime: '14:00', endTime: '18:00' },
      { action: 'click', selector: 'button[type="submit"]' },
      { action: 'waitForText', text: 'Profile updated successfully' },
      { action: 'navigate', path: '/profile' },
      { action: 'verifyText', text: 'Test User' },
      { action: 'verifyText', text: 'I am a test user who loves learning and teaching new skills.' },
      { action: 'verifyText', text: 'I believe in hands-on learning and practical examples.' },
      { action: 'verifyText', text: 'JavaScript' },
      { action: 'verifyText', text: 'React' },
      { action: 'verifyText', text: 'Node.js' },
      { action: 'verifyText', text: 'Web Development' },
      { action: 'verifyText', text: 'Monday' },
      { action: 'verifyText', text: 'Wednesday' },
    ],
  },
  {
    name: 'Experienced User Updates Profile',
    description: 'An experienced user updates their existing profile',
    steps: [
      { action: 'navigate', path: '/login' },
      { action: 'login', email: TEST_EMAIL, password: TEST_PASSWORD },
      { action: 'navigate', path: '/dashboard/profile' },
      { action: 'click', selector: 'button:has-text("Edit")' },
      { action: 'clearField', selector: 'textarea[placeholder*="teaching approach"]' },
      { action: 'fillField', selector: 'textarea[placeholder*="teaching approach"]', value: 'I focus on project-based learning and real-world applications.' },
      { action: 'removeLearningGoal', value: 'JavaScript' },
      { action: 'addLearningGoal', value: 'TypeScript' },
      { action: 'removeSkill', category: 'Web Development' },
      { action: 'addSkill', category: 'Frontend Development', level: 5, description: 'Expert in React and modern frontend frameworks' },
      { action: 'removeAvailabilitySlot', day: 'Monday' },
      { action: 'addAvailabilitySlot', day: 'Friday', startTime: '10:00', endTime: '15:00' },
      { action: 'click', selector: 'button:has-text("Save")' },
      { action: 'waitForText', text: 'Profile updated successfully' },
      { action: 'navigate', path: '/profile' },
      { action: 'verifyText', text: 'I focus on project-based learning and real-world applications.' },
      { action: 'verifyText', text: 'TypeScript' },
      { action: 'verifyNotText', text: 'JavaScript' },
      { action: 'verifyText', text: 'Frontend Development' },
      { action: 'verifyNotText', text: 'Web Development' },
      { action: 'verifyText', text: 'Friday' },
      { action: 'verifyNotText', text: 'Monday' },
    ],
  },
  {
    name: 'User Adds Portfolio Items',
    description: 'A user adds portfolio items to showcase their work',
    steps: [
      { action: 'navigate', path: '/login' },
      { action: 'login', email: TEST_EMAIL, password: TEST_PASSWORD },
      { action: 'navigate', path: '/dashboard/profile' },
      { action: 'click', selector: 'button:has-text("Edit Portfolio")' },
      { action: 'click', selector: 'button:has-text("Add Portfolio Item")' },
      { action: 'fillField', selector: 'input[placeholder*="title"]', value: 'E-commerce Website' },
      { action: 'fillField', selector: 'textarea[placeholder*="describe"]', value: 'A full-stack e-commerce website built with Next.js and Supabase' },
      { action: 'fillField', selector: 'input[placeholder*="https://"]', value: 'https://example.com/project1' },
      { action: 'uploadImage', selector: 'input[type="file"]', path: './test-assets/portfolio1.jpg' },
      { action: 'click', selector: 'button:has-text("Add Item")' },
      { action: 'click', selector: 'button:has-text("Add Portfolio Item")' },
      { action: 'fillField', selector: 'input[placeholder*="title"]', value: 'Mobile App' },
      { action: 'fillField', selector: 'textarea[placeholder*="describe"]', value: 'A React Native mobile app for task management' },
      { action: 'fillField', selector: 'input[placeholder*="https://"]', value: 'https://example.com/project2' },
      { action: 'uploadImage', selector: 'input[type="file"]', path: './test-assets/portfolio2.jpg' },
      { action: 'click', selector: 'button:has-text("Add Item")' },
      { action: 'click', selector: 'button:has-text("Save")' },
      { action: 'waitForText', text: 'Portfolio updated successfully' },
      { action: 'navigate', path: '/profile' },
      { action: 'verifyText', text: 'E-commerce Website' },
      { action: 'verifyText', text: 'Mobile App' },
      { action: 'verifyText', text: 'A full-stack e-commerce website built with Next.js and Supabase' },
      { action: 'verifyText', text: 'A React Native mobile app for task management' },
      { action: 'verifyElement', selector: 'img[alt="E-commerce Website"]' },
      { action: 'verifyElement', selector: 'img[alt="Mobile App"]' },
    ],
  },
  {
    name: 'User Manages Testimonials',
    description: 'A user selects which testimonials to feature on their profile',
    steps: [
      { action: 'navigate', path: '/login' },
      { action: 'login', email: TEST_EMAIL, password: TEST_PASSWORD },
      { action: 'navigate', path: '/dashboard/profile' },
      { action: 'click', selector: 'button:has-text("Edit Testimonials")' },
      { action: 'selectTestimonial', index: 0 },
      { action: 'selectTestimonial', index: 2 },
      { action: 'click', selector: 'button:has-text("Save")' },
      { action: 'waitForText', text: 'Testimonials updated successfully' },
      { action: 'navigate', path: '/profile' },
      { action: 'verifyTestimonialCount', count: 2 },
    ],
  },
  {
    name: 'User Views Profile in Dark Mode',
    description: 'A user views their profile with dark mode enabled',
    steps: [
      { action: 'navigate', path: '/login' },
      { action: 'login', email: TEST_EMAIL, password: TEST_PASSWORD },
      { action: 'toggleDarkMode' },
      { action: 'navigate', path: '/profile' },
      { action: 'verifyDarkMode' },
      { action: 'screenshot', path: './test-results/dark-mode-profile.png' },
    ],
  },
  {
    name: 'User Views Profile on Mobile',
    description: 'A user views their profile on a mobile device',
    steps: [
      { action: 'setViewport', width: 375, height: 667 },
      { action: 'navigate', path: '/login' },
      { action: 'login', email: TEST_EMAIL, password: TEST_PASSWORD },
      { action: 'navigate', path: '/profile' },
      { action: 'verifyResponsiveLayout' },
      { action: 'screenshot', path: './test-results/mobile-profile.png' },
    ],
  },
];

// Helper functions for test actions
async function performAction(page, action) {
  console.log(`Performing action: ${action.action}`);

  switch (action.action) {
    case 'navigate':
      await page.goto(`${BASE_URL}${action.path}`);
      break;

    case 'login':
      await page.fill('input[type="email"]', action.email);
      await page.fill('input[type="password"]', action.password);
      await page.click('button[type="submit"]');
      await page.waitForNavigation();
      break;

    case 'click':
      await page.click(action.selector);
      break;

    case 'fillField':
      await page.fill(action.selector, action.value);
      break;

    case 'clearField':
      await page.fill(action.selector, '');
      break;

    case 'waitForText':
      await page.waitForSelector(`text="${action.text}"`);
      break;

    case 'verifyText':
      const textContent = await page.textContent('body');
      if (!textContent.includes(action.text)) {
        throw new Error(`Text "${action.text}" not found on page`);
      }
      break;

    case 'verifyNotText':
      const pageContent = await page.textContent('body');
      if (pageContent.includes(action.text)) {
        throw new Error(`Text "${action.text}" found on page but should not be present`);
      }
      break;

    case 'verifyElement':
      await page.waitForSelector(action.selector);
      break;

    case 'addLearningGoal':
      await page.fill('input[placeholder*="skill you want to learn"]', action.value);
      await page.click('button:has-text("Add")');
      break;

    case 'removeLearningGoal':
      const goalElement = await page.locator(`text="${action.value}"`).first();
      const parentElement = await goalElement.locator('xpath=..').first();
      await parentElement.locator('button').click();
      break;

    case 'addSkill':
      await page.selectOption('select[aria-label="Skill category"]', { label: action.category });
      await page.fill('input[type="range"]', action.level.toString());
      await page.fill('textarea[placeholder*="experience with this skill"]', action.description);
      await page.click('button:has-text("Add Skill")');
      break;

    case 'removeSkill':
      const skillElement = await page.locator(`text="${action.category}"`).first();
      const skillParent = await skillElement.locator('xpath=../..').first();
      await skillParent.locator('button').click();
      break;

    case 'addAvailabilitySlot':
      await page.click('button:has-text("Add Availability Slot")');
      await page.selectOption('select[aria-label="Day of week"]', { label: action.day });
      await page.fill('input[type="time"]:nth-of-type(1)', action.startTime);
      await page.fill('input[type="time"]:nth-of-type(2)', action.endTime);
      await page.click('button:has-text("Add Slot")');
      break;

    case 'removeAvailabilitySlot':
      const dayElement = await page.locator(`text="${action.day}"`).first();
      const slotParent = await dayElement.locator('xpath=../..').first();
      await slotParent.locator('button').click();
      break;

    case 'uploadImage':
      await page.setInputFiles(action.selector, action.path);
      break;

    case 'selectTestimonial':
      const testimonials = await page.locator('.testimonial-card').all();
      await testimonials[action.index].locator('button:has-text("Select")').click();
      break;

    case 'verifyTestimonialCount':
      const count = await page.locator('.featured-testimonial').count();
      if (count !== action.count) {
        throw new Error(`Expected ${action.count} featured testimonials, but found ${count}`);
      }
      break;

    case 'toggleDarkMode':
      await page.evaluate(() => {
        document.documentElement.classList.toggle('dark');
      });
      break;

    case 'verifyDarkMode':
      const isDark = await page.evaluate(() => {
        return document.documentElement.classList.contains('dark');
      });
      if (!isDark) {
        throw new Error('Dark mode is not enabled');
      }
      break;

    case 'setViewport':
      await page.setViewportSize({ width: action.width, height: action.height });
      break;

    case 'verifyResponsiveLayout':
      // Check if the layout is responsive by verifying certain elements are stacked
      const isMobileLayout = await page.evaluate(() => {
        const grid = document.querySelector('.grid');
        if (!grid) return false;

        const computedStyle = window.getComputedStyle(grid);
        return computedStyle.gridTemplateColumns.split(' ').length === 1;
      });

      if (!isMobileLayout) {
        throw new Error('Page is not using a responsive mobile layout');
      }
      break;

    case 'screenshot':
      await page.screenshot({ path: action.path, fullPage: true });
      break;

    default:
      throw new Error(`Unknown action: ${action.action}`);
  }
}

// Main test function
async function runTests() {
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();

  const results = [];

  for (const scenario of scenarios) {
    console.log(`\nRunning scenario: ${scenario.name}`);
    console.log(scenario.description);

    try {
      for (const step of scenario.steps) {
        await performAction(page, step);
      }

      results.push({
        scenario: scenario.name,
        status: 'PASS',
      });

      console.log(`✅ Scenario passed: ${scenario.name}`);
    } catch (error) {
      results.push({
        scenario: scenario.name,
        status: 'FAIL',
        error: error.message,
      });

      console.error(`❌ Scenario failed: ${scenario.name}`);
      console.error(`   Error: ${error.message}`);

      // Take a screenshot of the failure
      await page.screenshot({ path: `./test-results/${scenario.name.replace(/\s+/g, '-')}-failure.png` });
    }
  }

  // Generate a report
  const report = {
    timestamp: new Date().toISOString(),
    totalScenarios: scenarios.length,
    passed: results.filter(r => r.status === 'PASS').length,
    failed: results.filter(r => r.status === 'FAIL').length,
    results,
  };

  console.log('\nTest Results:');
  console.log(`Total Scenarios: ${report.totalScenarios}`);
  console.log(`Passed: ${report.passed}`);
  console.log(`Failed: ${report.failed}`);

  // Write report to file
  const fs = require('fs');
  fs.writeFileSync('./test-results/report.json', JSON.stringify(report, null, 2));

  await browser.close();
}

// Run the tests
runTests().catch(console.error);
