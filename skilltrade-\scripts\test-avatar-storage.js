const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Error: Missing Supabase environment variables.');
  console.error('Make sure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are set in .env.local');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Sample test image path - replace with an actual image in your project
const testImagePath = path.join(__dirname, '..', 'public', 'test-avatar.png');

// If the test image doesn't exist, create a simple one
function createTestImage() {
  if (!fs.existsSync(testImagePath)) {
    console.log('Test image not found. Creating a sample image...');
    
    // Ensure the public directory exists
    const publicDir = path.join(__dirname, '..', 'public');
    if (!fs.existsSync(publicDir)) {
      fs.mkdirSync(publicDir, { recursive: true });
    }
    
    // Copy a placeholder image or create a simple one
    // For simplicity, we'll use a base64 encoded 1x1 pixel PNG
    const base64Image = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg==';
    const imageBuffer = Buffer.from(base64Image, 'base64');
    fs.writeFileSync(testImagePath, imageBuffer);
    
    console.log('Sample test image created at:', testImagePath);
  }
}

async function testAvatarStorage() {
  try {
    console.log('Testing Supabase avatar storage...');
    
    // 1. Sign in (you need to provide credentials)
    console.log('\n1. Signing in to Supabase...');
    console.log('Please enter your email and password when prompted.');
    
    const email = await promptInput('Email: ');
    const password = await promptInput('Password: ');
    
    const { data: { user }, error: signInError } = await supabase.auth.signInWithPassword({
      email,
      password
    });
    
    if (signInError) {
      throw new Error(`Sign in failed: ${signInError.message}`);
    }
    
    console.log('Signed in successfully as:', user.email);
    
    // 2. Check if the avatars bucket exists
    console.log('\n2. Checking if avatars bucket exists...');
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
    
    if (bucketsError) {
      throw new Error(`Error listing buckets: ${bucketsError.message}`);
    }
    
    const avatarsBucket = buckets.find(bucket => bucket.name === 'avatars');
    
    if (!avatarsBucket) {
      throw new Error('Avatars bucket not found. Please create it first.');
    }
    
    console.log('Avatars bucket found.');
    
    // 3. Create test image if it doesn't exist
    createTestImage();
    
    // 4. Upload test avatar
    console.log('\n3. Uploading test avatar...');
    const userId = user.id;
    const fileName = `test-avatar-${Date.now()}.png`;
    const filePath = `${userId}/${fileName}`;
    
    const fileBuffer = fs.readFileSync(testImagePath);
    
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('avatars')
      .upload(filePath, fileBuffer, {
        contentType: 'image/png',
        cacheControl: '3600',
        upsert: true
      });
    
    if (uploadError) {
      throw new Error(`Error uploading avatar: ${uploadError.message}`);
    }
    
    console.log('Avatar uploaded successfully:', filePath);
    
    // 5. Get public URL
    console.log('\n4. Getting public URL...');
    const { data: { publicUrl } } = supabase.storage
      .from('avatars')
      .getPublicUrl(filePath);
    
    console.log('Public URL:', publicUrl);
    
    // 6. List files in user's folder
    console.log('\n5. Listing files in user folder...');
    const { data: files, error: listError } = await supabase.storage
      .from('avatars')
      .list(userId);
    
    if (listError) {
      throw new Error(`Error listing files: ${listError.message}`);
    }
    
    console.log('Files in user folder:');
    files.forEach(file => console.log(` - ${file.name}`));
    
    // 7. Delete the test avatar
    console.log('\n6. Deleting test avatar...');
    const { error: deleteError } = await supabase.storage
      .from('avatars')
      .remove([filePath]);
    
    if (deleteError) {
      throw new Error(`Error deleting avatar: ${deleteError.message}`);
    }
    
    console.log('Avatar deleted successfully.');
    
    console.log('\nAll tests passed! Avatar storage is working correctly.');
  } catch (error) {
    console.error('\nError testing avatar storage:', error.message);
    
    // Provide troubleshooting guidance based on the error
    if (error.message.includes('Permission denied')) {
      console.log('\nTroubleshooting:');
      console.log('1. Check that you have set up the correct storage policies in the Supabase dashboard.');
      console.log('2. Make sure you are signed in with a valid user account.');
      console.log('3. Verify that the policies allow authenticated users to perform the operation that failed.');
    } else if (error.message.includes('not found')) {
      console.log('\nTroubleshooting:');
      console.log('1. Make sure the avatars bucket exists in your Supabase project.');
      console.log('2. Create it manually in the Supabase dashboard if needed.');
    }
    
    process.exit(1);
  }
}

// Helper function to prompt for input
function promptInput(prompt) {
  return new Promise((resolve) => {
    process.stdout.write(prompt);
    process.stdin.once('data', (data) => {
      resolve(data.toString().trim());
    });
  });
}

// Run the test
testAvatarStorage();
