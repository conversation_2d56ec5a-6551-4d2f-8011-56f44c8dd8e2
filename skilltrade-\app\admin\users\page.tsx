'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Edit, Trash2, UserPlus, Shield, ShieldOff } from 'lucide-react';
import AdminPageHeader from '@/components/admin/AdminPageHeader';
import AdminDataTable from '@/components/admin/AdminDataTable';

interface User {
  id: string;
  email: string;
  display_name: string | null;
  credit_balance: number;
  is_admin: boolean;
  created_at: string;
}

export default function AdminUsersPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [total, setTotal] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const router = useRouter();

  const fetchUsers = async () => {
    try {
      setLoading(true);
      
      const queryParams = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
      });
      
      if (searchQuery) {
        queryParams.set('query', searchQuery);
      }
      
      const response = await fetch(`/api/admin/users?${queryParams.toString()}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch users');
      }
      
      const data = await response.json();
      setUsers(data.users);
      setTotal(data.total);
    } catch (error: any) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, [page, limit, searchQuery]);

  const handleDeleteUser = async (userId: string) => {
    if (!confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
      return;
    }
    
    try {
      const response = await fetch(`/api/admin/users/${userId}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete user');
      }
      
      // Refresh the user list
      fetchUsers();
    } catch (error: any) {
      setError(error.message);
    }
  };

  const handleRoleChange = async (userId: string, action: 'promote' | 'demote') => {
    try {
      const response = await fetch(`/api/admin/users/${userId}/role`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to ${action} user`);
      }
      
      // Refresh the user list
      fetchUsers();
    } catch (error: any) {
      setError(error.message);
    }
  };

  const columns = [
    {
      key: 'display_name',
      label: 'Name',
      sortable: true,
      render: (value: string | null, user: User) => (
        <div>
          <div className="font-medium text-gray-900 dark:text-white">
            {value || 'Unnamed User'}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">{user.email}</div>
        </div>
      ),
    },
    {
      key: 'credit_balance',
      label: 'Credits',
      sortable: true,
      render: (value: number) => (
        <div className="text-gray-900 dark:text-white">{value}</div>
      ),
    },
    {
      key: 'is_admin',
      label: 'Role',
      sortable: true,
      render: (value: boolean) => (
        <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          value
            ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300'
            : 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300'
        }`}>
          {value ? 'Admin' : 'User'}
        </div>
      ),
    },
    {
      key: 'created_at',
      label: 'Joined',
      sortable: true,
      render: (value: string) => (
        <div className="text-gray-500 dark:text-gray-400">
          {new Date(value).toLocaleDateString()}
        </div>
      ),
    },
  ];

  const renderActions = (user: User) => (
    <div className="flex items-center space-x-3">
      <Link
        href={`/admin/users/${user.id}`}
        className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
      >
        <Edit className="h-5 w-5" />
      </Link>
      
      {user.is_admin ? (
        <button
          onClick={() => handleRoleChange(user.id, 'demote')}
          className="text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300"
          title="Demote from admin"
        >
          <ShieldOff className="h-5 w-5" />
        </button>
      ) : (
        <button
          onClick={() => handleRoleChange(user.id, 'promote')}
          className="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300"
          title="Promote to admin"
        >
          <Shield className="h-5 w-5" />
        </button>
      )}
      
      <button
        onClick={() => handleDeleteUser(user.id)}
        className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300"
        title="Delete user"
      >
        <Trash2 className="h-5 w-5" />
      </button>
    </div>
  );

  return (
    <div>
      <AdminPageHeader
        title="User Management"
        description="View and manage user accounts"
        actions={
          <Link
            href="/admin/users/new"
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <UserPlus className="h-5 w-5 mr-2" />
            Add User
          </Link>
        }
      />
      
      {error && (
        <div className="bg-red-100 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 px-4 py-3 rounded-lg mb-6">
          {error}
        </div>
      )}
      
      <AdminDataTable
        columns={columns}
        data={users}
        searchable={true}
        searchKeys={['email', 'display_name']}
        actions={renderActions}
        loading={loading}
        emptyState={
          <div className="text-center">
            <p className="text-gray-500 dark:text-gray-400 mb-4">No users found</p>
            <Link
              href="/admin/users/new"
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <UserPlus className="h-5 w-5 mr-2" />
              Add User
            </Link>
          </div>
        }
      />
      
      {/* Pagination */}
      {total > 0 && (
        <div className="mt-6 flex items-center justify-between">
          <div className="text-sm text-gray-700 dark:text-gray-300">
            Showing <span className="font-medium">{(page - 1) * limit + 1}</span> to{' '}
            <span className="font-medium">{Math.min(page * limit, total)}</span> of{' '}
            <span className="font-medium">{total}</span> users
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => setPage(page - 1)}
              disabled={page === 1}
              className={`px-3 py-1 rounded-md ${
                page === 1
                  ? 'bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-600 cursor-not-allowed'
                  : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600'
              }`}
            >
              Previous
            </button>
            <button
              onClick={() => setPage(page + 1)}
              disabled={page * limit >= total}
              className={`px-3 py-1 rounded-md ${
                page * limit >= total
                  ? 'bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-600 cursor-not-allowed'
                  : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600'
              }`}
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
