import { createServerSide } from '@/lib/supabase-server';
import FeaturedSkills from './FeaturedSkills';

export const dynamic = 'force-dynamic';

interface Review {
  id: string;
  rating: number;
  comment: string;
  created_at: string;
  session_id?: string; // Optional session_id for direct queries
}

interface Session {
  id: string;
  skill_id: string;
  scheduled_at: string;
}

interface AvailableDate {
  id: string;
  skill_id: string;
  date_time: string;
  duration_hours: number;
  is_booked: boolean;
}

interface SkillOwner {
  id: string;
  display_name: string | null;
  avatar_url: string | null;
}

// Define a type that can handle both single owner and array of owners
type OwnerData = SkillOwner | SkillOwner[] | any;

interface Skill {
  id: string;
  title: string;
  description: string;
  tags: string[];
  owner_id: string;
  is_active: boolean;
  created_at: string;
  difficulty_level?: 'beginner' | 'intermediate' | 'advanced';
  image_url?: string | null;
  owner: OwnerData;
}

interface EnhancedSkill extends Skill {
  reviews: Review[];
  avg_rating: number;
  last_session_date: string | null;
  available_dates: AvailableDate[];
}

export default async function FeaturedSkillsSection() {
  try {
    const supabase = await createServerSide();

    console.log('Fetching skills for featured section...');

    // Fetch a random selection of active skills with their owners
    const { data: skills, error } = await supabase
      .from('skills')
      .select(`
        id,
        title,
        description,
        tags,
        owner_id,
        is_active,
        created_at,
        difficulty_level,
        image_url,
        owner:profiles!skills_owner_id_fkey(id, display_name, avatar_url)
      `)
      .eq('is_active', true)
      .order('created_at', { ascending: false })
      .limit(10);

    if (error) {
      console.error('Error fetching skills:', error);
      throw error;
    }

    console.log(`Fetched ${skills?.length || 0} skills`);

    // If no skills are found, return early
    if (!skills || skills.length === 0) {
      return (
        <section className="py-16 bg-gradient-to-b from-gray-900 to-gray-950">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-4 text-white">
                Featured Skills
              </h2>
              <p className="text-xl text-gray-400 max-w-2xl mx-auto">
                Discover some of the amazing skills our community members are teaching
              </p>
            </div>
            <div className="bg-gray-800 rounded-xl p-8 text-center max-w-4xl mx-auto">
              <p className="text-gray-400">No featured skills available at the moment.</p>
            </div>
          </div>
        </section>
      );
    }

    // Get reviews for all skills in a single query using a different approach
    // First, get all sessions for these skills
    const { data: skillSessions, error: sessionsError } = await supabase
      .from('sessions')
      .select('id, skill_id')
      .in('skill_id', skills.map(skill => skill.id));

    if (sessionsError) {
      console.error('Error fetching sessions for reviews:', sessionsError);
    }

    console.log(`Fetched ${skillSessions?.length || 0} sessions for reviews`);

    // Initialize empty reviews by skill ID
    const reviewsBySkillId: Record<string, Review[]> = {};
    skills.forEach(skill => {
      reviewsBySkillId[skill.id] = [];
    });

    // If we have sessions, get reviews for those sessions
    if (skillSessions && skillSessions.length > 0) {
      const sessionIds = skillSessions.map(session => session.id);

      // Get reviews for these sessions
      const { data: allReviews, error: reviewsError } = await supabase
        .from('reviews')
        .select('id, rating, comment, created_at, session_id')
        .in('session_id', sessionIds);

      if (reviewsError) {
        console.error('Error fetching reviews:', reviewsError);
      }

      console.log(`Fetched ${allReviews?.length || 0} reviews`);

      // Create a map of session ID to skill ID for quick lookup
      const sessionToSkillMap = skillSessions.reduce<Record<string, string>>((map, session) => {
        map[session.id] = session.skill_id;
        return map;
      }, {});

      // Group reviews by skill ID using the session-to-skill mapping
      (allReviews || []).forEach(review => {
        const skillId = sessionToSkillMap[review.session_id];
        if (skillId && reviewsBySkillId[skillId]) {
          reviewsBySkillId[skillId].push({
            id: review.id,
            rating: review.rating,
            comment: review.comment,
            created_at: review.created_at
          });
        }
      });
    }

    // Get all sessions for these skills in a single query
    const { data: allSessions } = await supabase
      .from('sessions')
      .select('id, skill_id, scheduled_at')
      .in('skill_id', skills.map(skill => skill.id))
      .order('scheduled_at', { ascending: false });

    console.log(`Fetched ${allSessions?.length || 0} sessions`);

    // Group sessions by skill_id
    const sessionsBySkillId = (allSessions || []).reduce<Record<string, Session[]>>((acc, session) => {
      if (!acc[session.skill_id]) {
        acc[session.skill_id] = [];
      }
      acc[session.skill_id].push(session);
      return acc;
    }, {});

    // Get all available dates for these skills in a single query
    const { data: allAvailableDates } = await supabase
      .from('skill_available_dates')
      .select('*')
      .in('skill_id', skills.map(skill => skill.id))
      .eq('is_booked', false)
      .gte('date_time', new Date().toISOString())
      .order('date_time', { ascending: true });

    console.log(`Fetched ${allAvailableDates?.length || 0} available dates`);

    // Group available dates by skill_id
    const availableDatesBySkillId = (allAvailableDates || []).reduce<Record<string, AvailableDate[]>>((acc, date) => {
      if (!acc[date.skill_id]) {
        acc[date.skill_id] = [];
      }
      acc[date.skill_id].push(date);
      return acc;
    }, {});

    // Enhance skills with the grouped data
    const enhancedSkills = skills.map(skill => {
      // Get reviews for this skill
      const skillReviews = reviewsBySkillId[skill.id] || [];

      // Calculate average rating
      // Convert old 'positive'/'negative' ratings to numeric values if needed
      const ratings = skillReviews.map((review: Review) => {
        if (typeof review.rating === 'number') {
          return review.rating; // Already a number (1-5 scale)
        } else {
          // Handle legacy ratings if they exist
          return review.rating === 'positive' ? 5 : 1;
        }
      });

      const avgRating = ratings.length > 0
        ? ratings.reduce((sum: number, rating: number) => sum + rating, 0) / ratings.length
        : 0;

      // Get last session date
      const skillSessions = sessionsBySkillId[skill.id] || [];
      const lastSessionDate = skillSessions.length > 0 ? skillSessions[0].scheduled_at : null;

      // Get available dates
      const skillAvailableDates = availableDatesBySkillId[skill.id] || [];

      // Process owner data
      let ownerObj: SkillOwner = {
        id: skill.owner_id,
        display_name: 'Unknown',
        avatar_url: null
      };

      // Check if owner exists and has the right structure
      if (skill.owner) {
        // Use type assertion with unknown first to avoid TypeScript errors
        const ownerData = skill.owner as unknown;

        if (Array.isArray(ownerData) && ownerData.length > 0) {
          // If owner is an array, take the first element
          const firstOwner = ownerData[0];
          if (firstOwner && typeof firstOwner === 'object') {
            if ('id' in firstOwner) ownerObj.id = firstOwner.id;
            if ('display_name' in firstOwner) ownerObj.display_name = firstOwner.display_name || 'Unknown';
            if ('avatar_url' in firstOwner) ownerObj.avatar_url = firstOwner.avatar_url;
          }
        } else if (ownerData && typeof ownerData === 'object' && !Array.isArray(ownerData)) {
          // If owner is an object, use it directly
          const singleOwner = ownerData as Record<string, any>;
          if ('id' in singleOwner) ownerObj.id = singleOwner.id;
          if ('display_name' in singleOwner) ownerObj.display_name = singleOwner.display_name || 'Unknown';
          if ('avatar_url' in singleOwner) ownerObj.avatar_url = singleOwner.avatar_url;
        }
      }

      console.log(`Skill ${skill.id} owner:`, ownerObj);

      // Return enhanced skill
      return {
        ...skill,
        owner: ownerObj,
        reviews: skillReviews,
        avg_rating: avgRating,
        last_session_date: lastSessionDate,
        available_dates: skillAvailableDates.slice(0, 3),
        difficulty_level: skill.difficulty_level || 'intermediate',
        image_url: skill.image_url
      };
    });

    console.log(`Enhanced ${enhancedSkills.length} skills with additional data`);

    // Shuffle the skills to get a random selection
    const shuffledSkills = [...enhancedSkills].sort(() => 0.5 - Math.random()).slice(0, 5);

    console.log(`Selected ${shuffledSkills.length} skills for display`);

    // Log the first skill for debugging
    if (shuffledSkills.length > 0) {
      const sampleSkill = shuffledSkills[0];
      const ownerName = sampleSkill.owner &&
                        typeof sampleSkill.owner === 'object' &&
                        'display_name' in sampleSkill.owner ?
                        sampleSkill.owner.display_name || 'Unknown' :
                        'Unknown';

      console.log('Sample skill:', {
        id: sampleSkill.id,
        title: sampleSkill.title,
        owner: ownerName,
        reviews_count: sampleSkill.reviews?.length || 0,
        avg_rating: sampleSkill.avg_rating,
        has_dates: (sampleSkill.available_dates?.length || 0) > 0,
        difficulty_level: sampleSkill.difficulty_level,
        has_image: !!sampleSkill.image_url
      });
    }

    return (
      <section className="py-16 bg-gradient-to-b from-gray-900 to-gray-950">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-white">
              Featured Skills
            </h2>
            <p className="text-xl text-gray-400 max-w-2xl mx-auto">
              Discover some of the amazing skills our community members are teaching
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <FeaturedSkills skills={shuffledSkills} />
          </div>
        </div>
      </section>
    );
  } catch (error) {
    console.error('Error in FeaturedSkillsSection:', error);

    // Return a fallback UI in case of error
    return (
      <section className="py-16 bg-gradient-to-b from-gray-900 to-gray-950">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-white">
              Featured Skills
            </h2>
            <p className="text-xl text-gray-400 max-w-2xl mx-auto">
              Discover some of the amazing skills our community members are teaching
            </p>
          </div>
          <div className="bg-gray-800 rounded-xl p-8 text-center max-w-4xl mx-auto">
            <p className="text-gray-400">Unable to load featured skills at this time.</p>
          </div>
        </div>
      </section>
    );
  }
}
