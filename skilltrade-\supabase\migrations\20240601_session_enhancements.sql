-- Migration for enhancing session completion, reviews, messaging, and notifications
-- Run this in the Supabase SQL Editor

-- 1. Update sessions table to track completion status by both parties
ALTER TABLE sessions 
ADD COLUMN teacher_marked_complete BOOLEAN DEFAULT FALSE,
ADD COLUMN learner_marked_complete BOOLEAN DEFAULT FALSE,
ADD COLUMN teacher_marked_at TIMESTAMPTZ,
ADD COLUMN learner_marked_at TIMESTAMPTZ,
ADD COLUMN notes TEXT;

-- 2. Update reviews table to use 5-star rating system
-- First, drop the check constraint on the rating column
ALTER TABLE reviews DROP CONSTRAINT IF EXISTS reviews_rating_check;

-- Then, alter the rating column to be numeric
ALTER TABLE reviews ALTER COLUMN rating TYPE INTEGER USING 
  CASE 
    WHEN rating = 'positive' THEN 5 
    WHEN rating = 'negative' THEN 1 
    ELSE 3 
  END;

-- Add a check constraint to ensure rating is between 1 and 5
ALTER TABLE reviews ADD CONSTRAINT reviews_rating_check 
  CHECK (rating >= 1 AND rating <= 5);

-- 3. Create messages table for session chat
CREATE TABLE messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  session_id UUID NOT NULL REFERENCES sessions(id) ON DELETE CASCADE,
  sender_id UUID NOT NULL REFERENCES profiles(id),
  content TEXT NOT NULL,
  is_read BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Add indexes for performance
CREATE INDEX idx_messages_session_id ON messages(session_id);
CREATE INDEX idx_messages_sender_id ON messages(sender_id);
CREATE INDEX idx_messages_created_at ON messages(created_at);

-- Set up Row Level Security (RLS)
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;

-- Messages are viewable by session participants
CREATE POLICY "Messages are viewable by participants" ON messages
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM sessions 
      WHERE sessions.id = messages.session_id 
      AND (auth.uid() = sessions.teacher_id OR auth.uid() = sessions.learner_id)
    )
  );

-- Users can insert their own messages
CREATE POLICY "Users can insert their own messages" ON messages
  FOR INSERT WITH CHECK (
    auth.uid() = sender_id AND
    EXISTS (
      SELECT 1 FROM sessions 
      WHERE sessions.id = messages.session_id 
      AND (auth.uid() = sessions.teacher_id OR auth.uid() = sessions.learner_id)
    )
  );

-- 4. Create notifications table
CREATE TABLE notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  type TEXT NOT NULL, -- 'message', 'session_update', 'review', etc.
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  related_id UUID, -- Can be session_id, message_id, etc.
  is_read BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Add indexes for performance
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_created_at ON notifications(created_at);
CREATE INDEX idx_notifications_is_read ON notifications(is_read);

-- Set up Row Level Security (RLS)
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- Notifications are viewable by the user
CREATE POLICY "Notifications are viewable by the user" ON notifications
  FOR SELECT USING (auth.uid() = user_id);

-- Users can update their own notifications (e.g., mark as read)
CREATE POLICY "Users can update their own notifications" ON notifications
  FOR UPDATE USING (auth.uid() = user_id);

-- 5. Create function to mark session as complete
CREATE OR REPLACE FUNCTION mark_session_complete(
  p_session_id UUID,
  p_user_id UUID
) RETURNS VOID AS $$
DECLARE
  v_session RECORD;
  v_now TIMESTAMPTZ := now();
BEGIN
  -- Get the session
  SELECT * INTO v_session FROM sessions WHERE id = p_session_id;
  
  -- Check if the user is a participant
  IF v_session.teacher_id = p_user_id THEN
    -- Teacher marking complete
    UPDATE sessions 
    SET 
      teacher_marked_complete = TRUE,
      teacher_marked_at = v_now
    WHERE id = p_session_id;
    
    -- Create notification for learner
    INSERT INTO notifications (
      user_id, 
      type, 
      title, 
      content, 
      related_id
    ) VALUES (
      v_session.learner_id,
      'session_update',
      'Session marked as complete',
      'The teacher has marked your session as complete. Please review and confirm completion.',
      p_session_id
    );
    
  ELSIF v_session.learner_id = p_user_id THEN
    -- Learner marking complete
    UPDATE sessions 
    SET 
      learner_marked_complete = TRUE,
      learner_marked_at = v_now
    WHERE id = p_session_id;
    
    -- Create notification for teacher
    INSERT INTO notifications (
      user_id, 
      type, 
      title, 
      content, 
      related_id
    ) VALUES (
      v_session.teacher_id,
      'session_update',
      'Session marked as complete',
      'The learner has marked your session as complete. Please review and confirm completion.',
      p_session_id
    );
  ELSE
    -- User is not a participant
    RAISE EXCEPTION 'User is not a participant in this session';
  END IF;
  
  -- Check if both have marked complete
  IF (
    SELECT teacher_marked_complete AND learner_marked_complete 
    FROM sessions 
    WHERE id = p_session_id
  ) THEN
    -- Both have marked complete, update status
    UPDATE sessions SET status = 'completed' WHERE id = p_session_id;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. Create function to auto-complete sessions after 24 hours
CREATE OR REPLACE FUNCTION auto_complete_sessions() RETURNS VOID AS $$
DECLARE
  v_session RECORD;
BEGIN
  -- Find sessions where one party marked complete more than 24 hours ago
  FOR v_session IN 
    SELECT * FROM sessions 
    WHERE 
      (
        (teacher_marked_complete AND NOT learner_marked_complete AND teacher_marked_at < now() - INTERVAL '24 hours')
        OR
        (learner_marked_complete AND NOT teacher_marked_complete AND learner_marked_at < now() - INTERVAL '24 hours')
      )
      AND status = 'accepted'
  LOOP
    -- Auto-complete the session
    UPDATE sessions 
    SET 
      status = 'completed',
      teacher_marked_complete = TRUE,
      learner_marked_complete = TRUE
    WHERE id = v_session.id;
    
    -- Create notifications for both parties
    INSERT INTO notifications (
      user_id, 
      type, 
      title, 
      content, 
      related_id
    ) VALUES 
    (
      v_session.teacher_id,
      'session_update',
      'Session auto-completed',
      'A session has been automatically marked as complete after 24 hours.',
      v_session.id
    ),
    (
      v_session.learner_id,
      'session_update',
      'Session auto-completed',
      'A session has been automatically marked as complete after 24 hours.',
      v_session.id
    );
  END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. Update the apply_credits_on_positive function to work with 5-star ratings
CREATE OR REPLACE FUNCTION apply_credits_on_review() RETURNS TRIGGER AS $$
BEGIN
  -- If rating is 3 or higher, consider it positive
  IF NEW.rating >= 3 THEN
    UPDATE sessions SET status = 'reviewed' WHERE id = NEW.session_id;

    -- Credit teacher
    INSERT INTO ledger(user_id, session_id, hours_delta, reason)
    SELECT teacher_id, NEW.session_id, s.duration_hours, 'teach'
    FROM sessions s WHERE s.id = NEW.session_id;

    -- Debit learner
    INSERT INTO ledger(user_id, session_id, hours_delta, reason)
    SELECT learner_id, NEW.session_id, -s.duration_hours, 'learn'
    FROM sessions s WHERE s.id = NEW.session_id;

    -- Update balances
    UPDATE profiles p
    SET credit_balance = p.credit_balance + l.hours_delta
    FROM ledger l 
    WHERE l.user_id = p.id AND l.session_id = NEW.session_id;
  ELSE
    -- If rating is below 3, mark as disputed
    UPDATE sessions SET status = 'disputed' WHERE id = NEW.session_id;
    
    -- Create notifications for both parties
    INSERT INTO notifications (
      user_id, 
      type, 
      title, 
      content, 
      related_id
    )
    SELECT 
      teacher_id,
      'dispute',
      'Session disputed',
      'A session has been marked as disputed. Please communicate with the other participant to resolve the issue.',
      NEW.session_id
    FROM sessions WHERE id = NEW.session_id;
    
    INSERT INTO notifications (
      user_id, 
      type, 
      title, 
      content, 
      related_id
    )
    SELECT 
      learner_id,
      'dispute',
      'Session disputed',
      'A session has been marked as disputed. Please communicate with the other participant to resolve the issue.',
      NEW.session_id
    FROM sessions WHERE id = NEW.session_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop the old trigger
DROP TRIGGER IF EXISTS trg_review_positive ON reviews;

-- Create the new trigger
CREATE TRIGGER trg_review_on_insert
AFTER INSERT ON reviews
FOR EACH ROW EXECUTE PROCEDURE apply_credits_on_review();

-- 8. Create a function to resolve disputes through participant agreement
CREATE OR REPLACE FUNCTION resolve_dispute(
  p_session_id UUID,
  p_resolution TEXT,
  p_user_id UUID,
  p_agreement_message TEXT
) RETURNS VOID AS $$
DECLARE
  v_session RECORD;
  v_other_user_id UUID;
BEGIN
  -- Get the session
  SELECT * INTO v_session FROM sessions WHERE id = p_session_id;
  
  -- Check if the session is disputed
  IF v_session.status != 'disputed' THEN
    RAISE EXCEPTION 'Session is not disputed';
  END IF;
  
  -- Check if the user is a participant
  IF v_session.teacher_id = p_user_id THEN
    v_other_user_id := v_session.learner_id;
  ELSIF v_session.learner_id = p_user_id THEN
    v_other_user_id := v_session.teacher_id;
  ELSE
    RAISE EXCEPTION 'User is not a participant in this session';
  END IF;
  
  -- Add a message about the resolution
  INSERT INTO messages (
    session_id,
    sender_id,
    content
  ) VALUES (
    p_session_id,
    p_user_id,
    'DISPUTE RESOLUTION: ' || p_agreement_message
  );
  
  -- Update the session status
  UPDATE sessions 
  SET 
    status = p_resolution,
    notes = COALESCE(notes, '') || E'\n\nDispute resolved by participants: ' || p_agreement_message
  WHERE id = p_session_id;
  
  -- Create notification for the other participant
  INSERT INTO notifications (
    user_id, 
    type, 
    title, 
    content, 
    related_id
  ) VALUES (
    v_other_user_id,
    'dispute_resolved',
    'Dispute resolved',
    'A dispute has been resolved for one of your sessions. Resolution: ' || p_resolution,
    p_session_id
  );
  
  -- If resolution is 'completed', apply credits
  IF p_resolution = 'completed' THEN
    -- Credit teacher
    INSERT INTO ledger(user_id, session_id, hours_delta, reason)
    VALUES (v_session.teacher_id, p_session_id, v_session.duration_hours, 'teach (dispute resolved)');

    -- Debit learner
    INSERT INTO ledger(user_id, session_id, hours_delta, reason)
    VALUES (v_session.learner_id, p_session_id, -v_session.duration_hours, 'learn (dispute resolved)');

    -- Update balances
    UPDATE profiles
    SET credit_balance = credit_balance + v_session.duration_hours
    WHERE id = v_session.teacher_id;
    
    UPDATE profiles
    SET credit_balance = credit_balance - v_session.duration_hours
    WHERE id = v_session.learner_id;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
