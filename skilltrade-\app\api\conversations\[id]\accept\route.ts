import { NextRequest, NextResponse } from 'next/server';
import { createServerSide } from '@/lib/supabase-server';
import { createAdminClient } from '@/lib/supabase-admin';

// POST /api/conversations/[id]/accept - Accept a conversation request
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createServerSide();

    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const conversationId = params.id;

    // Use the admin client to bypass RLS
    const adminSupabase = createAdminClient();

    // Check if the user is a participant in the conversation
    const { data: participant, error: participantError } = await adminSupabase
      .from('conversation_participants')
      .select('*')
      .eq('conversation_id', conversationId)
      .eq('user_id', user.id)
      .single();

    if (participantError) {
      console.error('Participant check error in accept endpoint:', participantError);
      console.log('User ID:', user.id);
      console.log('Conversation ID:', conversationId);

      // Log all participants for this conversation to debug
      const { data: allParticipants } = await adminSupabase
        .from('conversation_participants')
        .select('user_id')
        .eq('conversation_id', conversationId);

      console.log('All participants:', allParticipants);

      return NextResponse.json(
        { error: 'Conversation not found or you are not a participant' },
        { status: 404 }
      );
    }

    // Call the accept_conversation_request function with the correct parameter names
    console.log('Calling accept_conversation_request with:', {
      conversation_id_param: conversationId,
      user_id_param: user.id
    });

    const { data: result, error: functionError } = await adminSupabase
      .rpc('accept_conversation_request', {
        conversation_id_param: conversationId,
        user_id_param: user.id
      });

    if (functionError) {
      throw functionError;
    }

    return NextResponse.json({
      success: true,
      message: 'Conversation request accepted'
    });
  } catch (error: any) {
    console.error('Error accepting conversation request:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to accept conversation request' },
      { status: 500 }
    );
  }
}
