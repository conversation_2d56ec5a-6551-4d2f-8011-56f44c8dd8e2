-- Migration for conversation deletion functionality
-- This adds functions for ending and deleting conversations

-- 1. Add a new status 'ended' to the conversations table
ALTER TABLE conversations 
DROP CONSTRAINT IF EXISTS conversations_status_check;

ALTER TABLE conversations 
ADD CONSTRAINT conversations_status_check 
CHECK (status IN ('pending', 'accepted', 'rejected', 'ended'));

-- 2. Create function to end a conversation
CREATE OR REPLACE FUNCTION end_conversation(conversation_id UUID, user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  is_participant BOOLEAN;
  other_user_id UUID;
BEGIN
  -- Check if the user is a participant in the conversation
  SELECT EXISTS (
    SELECT 1 FROM conversation_participants
    WHERE conversation_id = $1 AND user_id = $2
  ) INTO is_participant;
  
  IF NOT is_participant THEN
    RAISE EXCEPTION 'User is not a participant in this conversation';
  END IF;
  
  -- Update the conversation status to ended
  UPDATE conversations
  SET status = 'ended'
  WHERE id = conversation_id;
  
  -- Get the other participant's ID
  SELECT cp.user_id INTO other_user_id
  FROM conversation_participants cp
  WHERE cp.conversation_id = $1 AND cp.user_id != $2
  LIMIT 1;
  
  -- Create a notification for the other user
  PERFORM create_notification(
    other_user_id,
    'conversation_ended',
    'Conversation ended',
    'The other participant has ended this conversation',
    '/dashboard/messages?id=' || conversation_id
  );
  
  RETURN TRUE;
END;
$$;

-- 3. Create function to delete a conversation for a user
-- This doesn't actually delete the conversation, but removes the user from it
-- If both users delete the conversation, it will be permanently deleted
CREATE OR REPLACE FUNCTION delete_conversation_for_user(conversation_id UUID, user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  is_participant BOOLEAN;
  other_participant_exists BOOLEAN;
BEGIN
  -- Check if the user is a participant in the conversation
  SELECT EXISTS (
    SELECT 1 FROM conversation_participants
    WHERE conversation_id = $1 AND user_id = $2
  ) INTO is_participant;
  
  IF NOT is_participant THEN
    RAISE EXCEPTION 'User is not a participant in this conversation';
  END IF;
  
  -- Remove the user from the conversation
  DELETE FROM conversation_participants
  WHERE conversation_id = $1 AND user_id = $2;
  
  -- Check if there are any other participants left
  SELECT EXISTS (
    SELECT 1 FROM conversation_participants
    WHERE conversation_id = $1
  ) INTO other_participant_exists;
  
  -- If no other participants, delete the conversation and all its messages
  IF NOT other_participant_exists THEN
    DELETE FROM direct_messages
    WHERE conversation_id = $1;
    
    DELETE FROM conversations
    WHERE id = $1;
  END IF;
  
  RETURN TRUE;
END;
$$;
