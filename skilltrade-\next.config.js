/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: ['iidqtbyxltqnhgyrpofd.supabase.co'],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '*.supabase.co',
        port: '',
        pathname: '/storage/v1/object/public/**',
      },
    ],
    unoptimized: true, // This allows direct loading of images from external URLs
  },
  // Add CORS headers for Supabase storage
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: '*',
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
