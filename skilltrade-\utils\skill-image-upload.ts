import { createClientSide } from '@/lib/supabase';

/**
 * Upload a skill image to Supabase Storage
 * @param skillId The skill ID (UUID)
 * @param ownerId The owner ID (UUID)
 * @param file The file to upload
 * @returns Object containing the path and URL of the uploaded image
 */
export async function uploadSkillImage(skillId: string, ownerId: string, file: File) {
  try {
    console.log('Uploading image for skill:', skillId);
    const supabase = createClientSide();

    // Validate file type
    if (!file.type.startsWith('image/')) {
      throw new Error('File must be an image');
    }

    // Limit file size (2MB)
    const MAX_SIZE = 2 * 1024 * 1024; // 2MB
    if (file.size > MAX_SIZE) {
      throw new Error('File size must be less than 2MB');
    }

    // Create a unique file name
    const fileExt = file.name.split('.').pop();
    const fileName = `${skillId}-${Date.now()}.${fileExt}`;
    const filePath = `${ownerId}/${fileName}`;

    console.log('Uploading to path:', filePath);

    // Upload the file
    const { data, error } = await supabase.storage
      .from('skill-images')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: true
      });

    if (error) {
      console.error('Storage upload error:', error);
      throw error;
    }

    // Get the public URL
    const { data: { publicUrl } } = supabase.storage
      .from('skill-images')
      .getPublicUrl(filePath);

    console.log('Public URL:', publicUrl);

    // Update the skill with the new image URL
    const { error: updateError } = await supabase
      .from('skills')
      .update({ image_url: publicUrl })
      .eq('id', skillId);

    if (updateError) {
      console.error('Skill update error:', updateError);
      throw updateError;
    }

    return {
      path: filePath,
      url: publicUrl
    };
  } catch (error) {
    console.error('Error uploading skill image:', error);
    throw error;
  }
}

/**
 * Delete a skill image from Supabase Storage
 * @param skillId The skill ID (UUID)
 * @param filePath The path of the file to delete
 */
export async function deleteSkillImage(skillId: string, filePath: string) {
  try {
    console.log('Deleting skill image:', filePath);
    const supabase = createClientSide();

    // Ensure the path is valid
    if (!filePath || filePath.trim() === '') {
      throw new Error('Invalid file path');
    }

    // Delete the file
    const { error } = await supabase.storage
      .from('skill-images')
      .remove([filePath]);

    if (error) {
      console.error('Storage removal error:', error);
      throw error;
    }

    // Update the skill to remove the image URL
    const { error: updateError } = await supabase
      .from('skills')
      .update({ image_url: null })
      .eq('id', skillId);

    if (updateError) {
      console.error('Skill update error:', updateError);
      throw updateError;
    }

    return true;
  } catch (error) {
    console.error('Error deleting skill image:', error);
    throw error;
  }
}

/**
 * Extract the file path from a Supabase Storage URL
 * @param url The Supabase Storage URL
 * @returns The file path
 */
export function extractPathFromUrl(url: string): string {
  try {
    // Extract the path from the URL
    const urlParts = url.split('/');
    const publicIndex = urlParts.indexOf('public');
    
    if (publicIndex <= 0 || publicIndex + 2 >= urlParts.length) {
      throw new Error('Invalid URL format');
    }
    
    const path = urlParts.slice(publicIndex + 2).join('/');
    
    if (!path || path.trim() === '') {
      throw new Error('Invalid file path');
    }
    
    return path;
  } catch (error) {
    console.error('Error extracting path from URL:', error);
    throw error;
  }
}
