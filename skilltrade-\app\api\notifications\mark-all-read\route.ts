import { createServerSide } from '@/lib/supabase-server';
import { createAdminClient } from '@/lib/supabase-admin';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerSide();

    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log('Marking all notifications as read for user:', user.id);

    // First, check if there are any unread notifications
    const { data: unreadNotifications, error: checkError } = await supabase
      .from('notifications')
      .select('id')
      .eq('user_id', user.id)
      .eq('is_read', false);

    if (checkError) {
      console.error('Error checking unread notifications:', checkError);
      return NextResponse.json(
        { error: 'Failed to check unread notifications' },
        { status: 500 }
      );
    }

    // If there are no unread notifications, return success early
    if (!unreadNotifications || unreadNotifications.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No unread notifications to mark as read',
        updatedNotifications: []
      });
    }

    console.log(`Found ${unreadNotifications.length} unread notifications to mark as read`);

    // Use the admin client to bypass RLS
    const adminSupabase = createAdminClient();

    // Update all unread notifications for the user
    const { data: updatedNotifications, error: updateError } = await adminSupabase
      .from('notifications')
      .update({ is_read: true })
      .eq('user_id', user.id)
      .eq('is_read', false)
      .select();

    if (updateError) {
      console.error('Error updating notifications:', updateError);
      return NextResponse.json(
        { error: 'Failed to mark all notifications as read', details: updateError.message },
        { status: 500 }
      );
    }

    console.log(`Successfully marked ${updatedNotifications?.length || 0} notifications as read`);

    return NextResponse.json({
      success: true,
      updatedNotifications: updatedNotifications || []
    });
  } catch (error: any) {
    console.error('Error marking all notifications as read:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}
