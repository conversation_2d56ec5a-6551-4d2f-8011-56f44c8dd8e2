-- Create a function to mark notifications as read that bypasses <PERSON>LS
CREATE OR REPLACE FUNCTION mark_notification_read(
  notification_id_param UUID
)
RETURNS void
SECURITY DEFINER -- This makes the function run with the privileges of the creator
AS $$
BEGIN
  UPDATE notifications
  SET is_read = true
  WHERE id = notification_id_param;
END;
$$ LANGUAGE plpgsql;

-- Create a function to mark all notifications as read for a user
CREATE OR REPLACE FUNCTION mark_all_notifications_read(
  user_id_param UUID
)
RETURNS void
SECURITY DEFINER -- This makes the function run with the privileges of the creator
AS $$
BEGIN
  UPDATE notifications
  SET is_read = true
  WHERE user_id = user_id_param
  AND is_read = false;
END;
$$ LANGUAGE plpgsql;
