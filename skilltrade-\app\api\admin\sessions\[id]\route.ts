import { NextRequest, NextResponse } from 'next/server';
import { createAdminClient } from '@/lib/supabase-admin';
import { isAdmin } from '@/lib/admin-utils';

// GET /api/admin/sessions/[id] - Get a specific session
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if the user is an admin
    const isUserAdmin = await isAdmin();
    if (!isUserAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    const sessionId = params.id;
    const supabase = createAdminClient();
    
    // Get session details
    const { data: session, error: sessionError } = await supabase
      .from('sessions')
      .select(`
        *,
        skill:skills(id, title, description, tags),
        teacher:profiles!sessions_teacher_id_fkey(id, display_name, email, avatar_url),
        learner:profiles!sessions_learner_id_fkey(id, display_name, email, avatar_url)
      `)
      .eq('id', sessionId)
      .single();
    
    if (sessionError) {
      throw sessionError;
    }
    
    // Get reviews for this session
    const { data: reviews, error: reviewsError } = await supabase
      .from('reviews')
      .select(`
        *,
        reviewer:profiles(display_name)
      `)
      .eq('session_id', sessionId);
    
    if (reviewsError) {
      throw reviewsError;
    }
    
    return NextResponse.json({
      session,
      reviews: reviews || [],
    });
  } catch (error: any) {
    console.error(`Error fetching session ${params.id}:`, error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch session' },
      { status: 500 }
    );
  }
}

// PATCH /api/admin/sessions/[id] - Update a session
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if the user is an admin
    const isUserAdmin = await isAdmin();
    if (!isUserAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    const sessionId = params.id;
    const supabase = createAdminClient();
    const body = await request.json();
    
    const { status, notes, scheduled_at, duration_hours } = body;
    
    // Update session
    const { data: session, error } = await supabase
      .from('sessions')
      .update({
        status,
        notes,
        scheduled_at,
        duration_hours,
        updated_at: new Date().toISOString(),
      })
      .eq('id', sessionId)
      .select(`
        *,
        skill:skills(id, title),
        teacher:profiles!sessions_teacher_id_fkey(id, display_name, email),
        learner:profiles!sessions_learner_id_fkey(id, display_name, email)
      `)
      .single();
    
    if (error) {
      throw error;
    }
    
    return NextResponse.json({
      session,
      message: 'Session updated successfully',
    });
  } catch (error: any) {
    console.error(`Error updating session ${params.id}:`, error);
    return NextResponse.json(
      { error: error.message || 'Failed to update session' },
      { status: 500 }
    );
  }
}
