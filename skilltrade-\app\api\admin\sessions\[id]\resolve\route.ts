import { NextRequest, NextResponse } from 'next/server';
import { createAdminClient } from '@/lib/supabase-admin';
import { isAdmin } from '@/lib/admin-utils';

// POST /api/admin/sessions/[id]/resolve - Resolve a disputed session
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if the user is an admin
    const isUserAdmin = await isAdmin();
    if (!isUserAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    const sessionId = params.id;
    const supabase = createAdminClient();
    const body = await request.json();

    const { resolution, adminNotes, creditAdjustment } = body;

    if (!resolution || !['completed', 'cancelled'].includes(resolution)) {
      return NextResponse.json(
        { error: 'Invalid resolution. Must be "completed" or "cancelled"' },
        { status: 400 }
      );
    }

    // Get the session to check if it's disputed
    const { data: session, error: sessionError } = await supabase
      .from('sessions')
      .select(`
        *,
        teacher:profiles!sessions_teacher_id_fkey(id),
        learner:profiles!sessions_learner_id_fkey(id)
      `)
      .eq('id', sessionId)
      .single();

    if (sessionError) {
      throw sessionError;
    }

    if (session.status !== 'disputed') {
      return NextResponse.json(
        { error: 'Only disputed sessions can be resolved' },
        { status: 400 }
      );
    }

    // Start a transaction
    const { error: transactionError } = await supabase.rpc('begin_transaction');
    if (transactionError) {
      throw transactionError;
    }

    try {
      // Update the session status
      const { error: updateError } = await supabase
        .from('sessions')
        .update({
          status: resolution,
          notes: session.notes ? `${session.notes}\n\nAdmin Resolution: ${adminNotes}` : `Admin Resolution: ${adminNotes}`,
          updated_at: new Date().toISOString(),
        })
        .eq('id', sessionId);

      if (updateError) {
        throw updateError;
      }

      // If there's a credit adjustment, use the adjust_credits function
      if (creditAdjustment && creditAdjustment.userId && creditAdjustment.hours) {
        const { error: adjustmentError } = await supabase.rpc('adjust_credits', {
          user_id: creditAdjustment.userId,
          hours_delta: creditAdjustment.hours,
          reason: `Admin adjustment for disputed session ${sessionId}`
        });

        if (adjustmentError) {
          throw adjustmentError;
        }
      }

      // Commit the transaction
      const { error: commitError } = await supabase.rpc('commit_transaction');
      if (commitError) {
        throw commitError;
      }

      // Get the updated session
      const { data: updatedSession, error: fetchError } = await supabase
        .from('sessions')
        .select(`
          *,
          skill:skills(id, title),
          teacher:profiles!sessions_teacher_id_fkey(id, display_name, email),
          learner:profiles!sessions_learner_id_fkey(id, display_name, email)
        `)
        .eq('id', sessionId)
        .single();

      if (fetchError) {
        throw fetchError;
      }

      return NextResponse.json({
        session: updatedSession,
        message: `Session resolved as ${resolution}`,
      });
    } catch (error) {
      // Rollback the transaction on error
      await supabase.rpc('rollback_transaction');
      throw error;
    }
  } catch (error: any) {
    console.error(`Error resolving session ${params.id}:`, error);
    return NextResponse.json(
      { error: error.message || 'Failed to resolve session' },
      { status: 500 }
    );
  }
}
