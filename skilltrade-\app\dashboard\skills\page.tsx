import { createServerSide } from '@/lib/supabase-server';
import Link from 'next/link';
import { redirect } from 'next/navigation';

export default async function SkillsPage() {
  const supabase = await createServerSide();

  // Check if user is logged in
  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    redirect('/login');
  }

  // Get user's skills
  const { data: skills } = await supabase
    .from('skills')
    .select('*')
    .eq('owner_id', user.id)
    .order('created_at', { ascending: false });

  return (
    <main className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">My Skills</h1>
          <Link
            href="/dashboard/skills/new"
            className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md transition"
          >
            Add New Skill
          </Link>
        </div>

        {skills && skills.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {skills.map((skill) => (
              <div key={skill.id} className="bg-gray-800 rounded-lg overflow-hidden shadow-lg">
                {skill.image_url && (
                  <div className="w-full h-40 overflow-hidden">
                    <img
                      src={skill.image_url}
                      alt={skill.title}
                      className="w-full h-full object-cover"
                    />
                  </div>
                )}
                <div className="p-6">
                  <div className="flex justify-between items-start">
                    <div>
                      <h2 className="text-xl font-semibold mb-1">{skill.title}</h2>
                      {skill.difficulty_level && (
                        <span className={`text-xs px-2 py-1 rounded-full inline-block mb-2 ${
                          skill.difficulty_level === 'beginner'
                            ? 'bg-green-900/50 text-green-300'
                            : skill.difficulty_level === 'intermediate'
                            ? 'bg-yellow-900/50 text-yellow-300'
                            : 'bg-red-900/50 text-red-300'
                        }`}>
                          {skill.difficulty_level.charAt(0).toUpperCase() + skill.difficulty_level.slice(1)}
                        </span>
                      )}
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs ${skill.is_active ? 'bg-green-900/50 text-green-300' : 'bg-gray-700 text-gray-300'}`}>
                      {skill.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                  <p className="text-gray-300 mb-4 line-clamp-3">
                    {skill.description || 'No description provided'}
                  </p>
                  <div className="flex flex-wrap gap-2 mb-4">
                    {skill.tags && skill.tags.map((tag: string, index: number) => (
                      <span key={index} className="bg-gray-700 px-2 py-1 rounded-full text-xs">
                        {tag}
                      </span>
                    ))}
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Link
                      href={`/dashboard/skills/${skill.id}`}
                      className="text-blue-400 hover:text-blue-300 transition"
                    >
                      View Details
                    </Link>
                    <span className="text-gray-500">|</span>
                    <Link
                      href={`/dashboard/skills/${skill.id}/edit`}
                      className="text-blue-400 hover:text-blue-300 transition"
                    >
                      Edit
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="bg-gray-800 rounded-lg p-8 text-center">
            <h2 className="text-xl font-semibold mb-4">You haven't added any skills yet</h2>
            <p className="text-gray-300 mb-6">
              Share your knowledge with the community by adding skills you can teach.
            </p>
            <Link
              href="/dashboard/skills/new"
              className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-6 rounded-md transition inline-block"
            >
              Add Your First Skill
            </Link>
          </div>
        )}
      </main>
  );
}
