'use client';

import React, { useState, useEffect } from 'react';
import { createClientSide } from '@/lib/supabase';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { formatDistanceToNow } from 'date-fns';
import { MessageCircle, Search, Plus } from 'lucide-react';
import NewConversationModal from './NewConversationModal';

interface Conversation {
  id: string;
  last_message_preview: string | null;
  last_message_at: string | null;
  created_at: string;
  updated_at: string;
  status: 'pending' | 'accepted' | 'rejected' | 'ended' | string;
  other_participants: {
    id: string;
    display_name: string | null;
    avatar_url: string | null;
  }[];
  unread_count: number;
  is_recipient?: boolean; // Flag to indicate if the current user is the recipient of the conversation request
}

interface ConversationListProps {
  selectedConversationId: string | null;
  currentUser: any;
}

export default function ConversationList({ selectedConversationId, currentUser }: ConversationListProps) {
  const router = useRouter();
  const supabase = createClientSide();

  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [showNewConversationModal, setShowNewConversationModal] = useState(false);

  // Add a debounce mechanism to prevent too many fetches
  const [fetchTimeoutId, setFetchTimeoutId] = useState<NodeJS.Timeout | null>(null);

  // Optimized fetchConversations function with debouncing, timeout handling and error recovery
  const fetchConversations = async () => {
    // Clear any existing timeout to implement debouncing
    if (fetchTimeoutId) {
      clearTimeout(fetchTimeoutId);
    }

    // Set a new timeout to delay the fetch
    const newTimeoutId = setTimeout(async () => {
      try {
        // Only show loading indicator on initial load, not on updates
        if (conversations.length === 0) {
          setLoading(true);
        }
        setError(null);

        // Use AbortController to handle timeouts
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

        // Add cache busting and use the controller signal
        const timestamp = new Date().getTime();
        const response = await fetch(`/api/conversations?t=${timestamp}`, {
          headers: {
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Expires': '0',
          },
          signal: controller.signal
        }).finally(() => clearTimeout(timeoutId));

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to fetch conversations');
        }

        const data = await response.json();

        // Process conversations with proper type checking
        const processedConversations = (data.conversations || []).map((conv: any) => {
          // Ensure status is set
          if (!conv.status) {
            return { ...conv, status: 'pending' };
          }

          // Ensure unread_count is a number
          if (typeof conv.unread_count !== 'number') {
            conv.unread_count = 0;
          }

          return conv;
        });

        // Sort conversations by last message time
        const sortedConversations = [...processedConversations].sort((a, b) => {
          const timeA = a.last_message_at ? new Date(a.last_message_at).getTime() : new Date(a.created_at).getTime();
          const timeB = b.last_message_at ? new Date(b.last_message_at).getTime() : new Date(b.created_at).getTime();
          return timeB - timeA;
        });

        // Update state with the sorted conversations
        setConversations(sortedConversations);
      } catch (error: any) {
        // Only set error if it's not an abort error (which is expected during cleanup)
        if (error.name !== 'AbortError') {
          console.error('Error fetching conversations:', error);
          setError(error.message || 'Failed to load conversations');
        }
      } finally {
        setLoading(false);
        setFetchTimeoutId(null);
      }
    }, 300); // 300ms debounce delay

    setFetchTimeoutId(newTimeoutId);
  };

  // Initial fetch of conversations
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const fetchConversationsMemoized = React.useCallback(fetchConversations, []);

  // Add a state to track if initial data has been loaded
  const [initialDataLoaded, setInitialDataLoaded] = useState(false);

  useEffect(() => {
    if (currentUser?.id) {
      // Immediately fetch conversations when component mounts
      const fetchData = async () => {
        try {
          await fetchConversationsMemoized();
          setInitialDataLoaded(true);
        } catch (error) {
          console.error('Error in initial fetch:', error);
          // Still mark as loaded even if there's an error to prevent infinite loading state
          setInitialDataLoaded(true);
        }
      };

      fetchData();

      // Set up a polling interval as a fallback for real-time updates
      const pollingInterval = setInterval(() => {
        console.log('Polling for conversation updates');
        fetchConversationsMemoized();
      }, 30000); // Poll every 30 seconds

      return () => {
        clearInterval(pollingInterval);
        // Also clear any pending fetch timeout when unmounting
        if (fetchTimeoutId) {
          clearTimeout(fetchTimeoutId);
        }
      };
    }
  }, [currentUser?.id, fetchTimeoutId, fetchConversationsMemoized]);

  // Set up polling for conversations (forum-style)
  useEffect(() => {
    if (!currentUser) return;

    console.log('Setting up polling for conversations list');

    // Set up polling interval for conversations
    const pollingInterval = setInterval(() => {
      console.log('Polling for conversation updates');
      fetchConversations();
    }, 5000); // Poll every 5 seconds

    return () => {
      console.log('Cleaning up polling for conversations');
      clearInterval(pollingInterval);
    };
  }, [currentUser?.id, fetchConversations]);

  const handleConversationClick = (conversationId: string) => {
    // Only navigate if the conversation ID is different from the current one
    if (conversationId !== selectedConversationId) {
      // Use window.history.pushState to update the URL without a full page reload
      window.history.pushState({}, '', `/dashboard/messages?id=${conversationId}`);
      // Then use router.replace to update Next.js router state without navigation
      router.replace(`/dashboard/messages?id=${conversationId}`);
    }
  };

  const handleNewConversation = (newConversation: Conversation) => {
    // Add the new conversation to the list
    setConversations(prev => [newConversation, ...prev]);

    // Navigate to the new conversation without causing a full page reload
    window.history.pushState({}, '', `/dashboard/messages?id=${newConversation.id}`);
    router.replace(`/dashboard/messages?id=${newConversation.id}`);

    // Close the modal
    setShowNewConversationModal(false);
  };

  // Filter conversations based on search query
  const filteredConversations = conversations.filter(conversation => {
    // Check if other_participants exists and has at least one entry
    if (!conversation.other_participants || conversation.other_participants.length === 0) {
      return false;
    }

    const otherParticipant = conversation.other_participants[0];
    const displayName = otherParticipant?.display_name || 'Unknown User';
    return displayName.toLowerCase().includes(searchQuery.toLowerCase());
  });

  // Sort conversations by last message time (most recent first)
  const sortedConversations = [...filteredConversations].sort((a, b) => {
    const timeA = a.last_message_at ? new Date(a.last_message_at).getTime() : new Date(a.created_at).getTime();
    const timeB = b.last_message_at ? new Date(b.last_message_at).getTime() : new Date(b.created_at).getTime();
    return timeB - timeA;
  });

  return (
    <div className="flex flex-col h-full">
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-900 dark:text-white">Messages</h2>
          <button
            onClick={() => setShowNewConversationModal(true)}
            className="p-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
            aria-label="New conversation"
          >
            <Plus size={20} />
          </button>
        </div>
        <div className="relative">
          <input
            type="text"
            placeholder="Search conversations..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full px-4 py-2 pl-10 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
        </div>
      </div>

      <div className="flex-1 overflow-y-auto">
        {loading && !initialDataLoaded ? (
          <div className="flex justify-center items-center h-32">
            <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : error ? (
          <div className="p-4 text-red-500 dark:text-red-400">{error}</div>
        ) : sortedConversations.length === 0 ? (
          <div className="p-4 text-center text-gray-500 dark:text-gray-400">
            {searchQuery ? 'No conversations match your search' : 'No conversations yet'}
          </div>
        ) : (
          <ul>
            {sortedConversations.map((conversation) => {
              const otherParticipant = conversation.other_participants[0];
              const displayName = otherParticipant?.display_name || 'Unknown User';
              const avatarUrl = otherParticipant?.avatar_url;
              const lastMessageTime = conversation.last_message_at
                ? formatDistanceToNow(new Date(conversation.last_message_at), { addSuffix: true })
                : formatDistanceToNow(new Date(conversation.created_at), { addSuffix: true });

              return (
                <li
                  key={conversation.id}
                  onClick={() => handleConversationClick(conversation.id)}
                  className={`p-4 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer ${
                    selectedConversationId === conversation.id ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                  }`}
                >
                  <div className="flex items-start">
                    <div className="flex-shrink-0 mr-3">
                      {avatarUrl ? (
                        <Image
                          src={avatarUrl}
                          alt={displayName}
                          width={40}
                          height={40}
                          className="rounded-full"
                        />
                      ) : (
                        <div className="w-10 h-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                          <span className="text-gray-600 dark:text-gray-300 text-sm font-medium">
                            {displayName.charAt(0).toUpperCase()}
                          </span>
                        </div>
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex justify-between">
                        <h3 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                          {displayName}
                        </h3>
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {lastMessageTime}
                        </span>
                      </div>
                      <div className="flex items-center">
                        {(conversation.status || 'pending') === 'pending' && (
                          <span className="inline-flex items-center justify-center h-4 w-4 rounded-full bg-yellow-500 mr-1.5"></span>
                        )}
                        {(conversation.status || '') === 'rejected' && (
                          <span className="inline-flex items-center justify-center h-4 w-4 rounded-full bg-red-500 mr-1.5"></span>
                        )}
                        {(conversation.status || '') === 'ended' && (
                          <span className="inline-flex items-center justify-center h-4 w-4 rounded-full bg-gray-500 mr-1.5"></span>
                        )}
                        <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                          {(conversation.status || 'pending') === 'pending'
                            ? conversation.is_recipient
                              ? `${displayName} sent you a request`
                              : `Waiting for ${displayName} to accept`
                            : (conversation.status || '') === 'rejected'
                              ? 'Conversation request declined'
                              : (conversation.status || '') === 'ended'
                                ? 'Conversation ended'
                                : conversation.last_message_preview || 'Start a conversation'}
                        </p>
                      </div>
                    </div>
                    {conversation.unread_count > 0 && (
                      <div className="ml-2 flex-shrink-0">
                        <span className="inline-flex items-center justify-center h-5 w-5 rounded-full bg-blue-600 text-white text-xs">
                          {conversation.unread_count}
                        </span>
                      </div>
                    )}
                  </div>
                </li>
              );
            })}
          </ul>
        )}
      </div>

      {showNewConversationModal && (
        <NewConversationModal
          onClose={() => setShowNewConversationModal(false)}
          onConversationCreated={handleNewConversation}
          currentUser={currentUser}
        />
      )}
    </div>
  );
}
