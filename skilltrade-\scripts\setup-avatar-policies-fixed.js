const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceRoleKey) {
  console.error('Error: Missing Supabase environment variables.');
  console.error('Make sure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set in .env.local');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);

async function setupAvatarPolicies() {
  try {
    console.log('Setting up storage policies for avatars bucket...');

    // Check if the avatars bucket exists
    const { data: buckets, error: bucketsError } = await supabase
      .storage
      .listBuckets();

    if (bucketsError) {
      throw new Error(`Error listing buckets: ${bucketsError.message}`);
    }

    const avatarsBucket = buckets.find(bucket => bucket.name === 'avatars');
    
    if (!avatarsBucket) {
      console.log('Avatars bucket not found. Creating it...');
      
      // Create the avatars bucket
      const { error: createError } = await supabase
        .storage
        .createBucket('avatars', { public: true });
        
      if (createError) {
        throw new Error(`Error creating avatars bucket: ${createError.message}`);
      }
      
      console.log('Avatars bucket created successfully.');
    } else {
      console.log('Avatars bucket found.');
    }

    // Execute the SQL for creating policies
    console.log('Setting up policies using SQL...');
    const sql = `
    -- Create policy for viewing avatars (public access)
    select storage.policy(
      'avatars', 
      'Avatars are viewable by everyone',
      'SELECT',
      'authenticated, anon',
      true
    );
    
    -- Create policy for uploading avatars (authenticated users only)
    select storage.policy(
      'avatars', 
      'Users can upload their own avatars',
      'INSERT',
      'authenticated',
      'auth.uid() IS NOT NULL'
    );
    
    -- Create policy for updating avatars (own avatars only)
    select storage.policy(
      'avatars', 
      'Users can update their own avatars',
      'UPDATE',
      'authenticated',
      'auth.uid() IS NOT NULL'
    );
    
    -- Create policy for deleting avatars (own avatars only)
    select storage.policy(
      'avatars', 
      'Users can delete their own avatars',
      'DELETE',
      'authenticated',
      'auth.uid() IS NOT NULL'
    );`;

    const { error } = await supabase.rpc('exec_sql', { sql });

    if (error) {
      console.error('Error executing SQL:', error.message);
      console.log('\nFalling back to manual policy creation...');
      
      // If SQL execution fails, try to create policies manually using the REST API
      await createPoliciesManually();
    } else {
      console.log('Avatar storage policies setup completed successfully!');
    }
  } catch (error) {
    console.error('Error setting up avatar policies:', error.message);
    console.log('\nPlease set up the policies manually in the Supabase dashboard:');
    console.log('1. Go to Storage > Buckets > avatars > Policies');
    console.log('2. Create the following policies:');
    console.log('   - "Avatars are viewable by everyone" (SELECT) with definition: true');
    console.log('   - "Users can upload their own avatars" (INSERT) with definition: auth.uid() IS NOT NULL');
    console.log('   - "Users can update their own avatars" (UPDATE) with definition: auth.uid() IS NOT NULL');
    console.log('   - "Users can delete their own avatars" (DELETE) with definition: auth.uid() IS NOT NULL');
    
    process.exit(1);
  }
}

async function createPoliciesManually() {
  try {
    console.log('Creating policies manually...');
    
    // Define the policies
    const policies = [
      {
        name: 'Avatars are viewable by everyone',
        operation: 'SELECT',
        definition: 'true',
        roles: ['authenticated', 'anon']
      },
      {
        name: 'Users can upload their own avatars',
        operation: 'INSERT',
        definition: 'auth.uid() IS NOT NULL',
        roles: ['authenticated']
      },
      {
        name: 'Users can update their own avatars',
        operation: 'UPDATE',
        definition: 'auth.uid() IS NOT NULL',
        roles: ['authenticated']
      },
      {
        name: 'Users can delete their own avatars',
        operation: 'DELETE',
        definition: 'auth.uid() IS NOT NULL',
        roles: ['authenticated']
      }
    ];
    
    // Create each policy
    for (const policy of policies) {
      console.log(`Creating policy: ${policy.name}`);
      
      // Use the REST API to create the policy
      const response = await fetch(`${supabaseUrl}/storage/v1/policies`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${supabaseServiceRoleKey}`,
          'apikey': supabaseServiceRoleKey
        },
        body: JSON.stringify({
          name: policy.name,
          bucket_id: 'avatars',
          operation: policy.operation,
          definition: policy.definition,
          roles: policy.roles
        })
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Failed to create policy: ${errorData.message || response.statusText}`);
      }
      
      console.log(`Policy "${policy.name}" created successfully.`);
    }
    
    console.log('All policies created successfully!');
  } catch (error) {
    console.error('Error creating policies manually:', error.message);
    throw error;
  }
}

// Run the setup
setupAvatarPolicies();
